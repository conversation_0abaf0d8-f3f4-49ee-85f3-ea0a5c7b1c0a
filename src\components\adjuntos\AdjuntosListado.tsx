/// <reference types="webpack/module" />

import React from "react"
import { getAdjuntos } from "../../services/adjuntosServices"

export const AdjuntosListado = (props: any) => {
  console.log("props.operacion:", props.operacion)

  const fetchAdjuntos = async () => {
    const _listadoAdjuntos = await getAdjuntos(props.operacion, '3108')
    console.log(_listadoAdjuntos)
    return _listadoAdjuntos
  }
  fetchAdjuntos().then((resp) => {
    console.log("resp fetchAdjuntos:", resp);
  })
  return (<div id='AdjuntosListadoList'>
    <button onClick={(e) => { fetchAdjuntos() }}>APRETAR PARA VER</button>
  </div>)
}