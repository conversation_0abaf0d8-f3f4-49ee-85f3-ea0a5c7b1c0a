/// <reference types="webpack/module" />

import React, { useContext } from "react";
import FacturaFormContext from "../contextProvider/counterContext";
import { CargaFacturaTop } from "./CargaFacturaTop";
import { FacturaArticulosComp } from "./FacturaArticulos";
import { FacturaHeader } from "../../../src/components/seguimiento_de_facturas/FacturaHeader";
import { FacturaMontos } from "./FacturaMontos";
import { DatosProvider } from "../contextProvider/datosProveedorContext";

export const Factura = () => {
  const _FacturaFormContext = useContext(FacturaFormContext);
  const hrefStart = "#"
  const IDsObject = {
    factura: 'factura' + _FacturaFormContext.counter,
    collapseFactura: 'facturaCollapse' + _FacturaFormContext.counter,
    collapseArticulo: 'ArticuloCollapse' + _FacturaFormContext.counter,
    btnExpandirPrinicipal: 'expandir-principal' + _FacturaFormContext.counter
  }

  const RenderFactura = () => {
    const _readOnly = _FacturaFormContext.onlyRead
    console.log("_readOnly:", _readOnly);
    const _cargaFacturaTopJsx = (_readOnly) ? <></> : <CargaFacturaTop />

    return (<div id={IDsObject.factura} className="invoice">
      <div className="d-flex justify-content-between align-items-center">
        <p>
          <a className="btn btn-primary expandir-principal" id={IDsObject.btnExpandirPrinicipal} data-toggle="collapse" href={hrefStart + IDsObject.collapseFactura} role="button" aria-expanded="true" aria-controls="collapseExample">
            {'Expandir Factura #-' + _FacturaFormContext.counter}
          </a>
        </p>
      </div>      
      {_cargaFacturaTopJsx}
      <DatosProvider><FacturaHeader /></DatosProvider>
      <div className="collapse show" id={IDsObject.collapseFactura}>
        <FacturaArticulosComp idCollapse={IDsObject.collapseArticulo} />
        <DatosProvider><FacturaMontos /></DatosProvider>
      </div>
    </div>)
  }

  return (
    RenderFactura()
  )
}