/// <reference types="webpack/module" />

import React, { useState, useEffect, useCallback } from "react"
import { getDataListadoMiBandejaV1, getDataListadoPaginado } from "../../services/operacionesService"
import { ListArmado } from "./armarListado"
import { DatosProvider, useProveedor } from "../contextProvider/datosProveedorContext"
import { LoaderLista } from "../loader/Loader";
import limpiarCamposFactura from "../limpiarCampos/limpiarCamposFactura"
import { PaginacionData } from '../../entities/PaginacionData';

export const PanelListado = () => {
  const { state } = useProveedor();
  const [isLoading, setIsLoading] = useState(false);
  const [jsx, setJsx] = useState(<></>);
  const [data, setData] = useState(null);
  const [tareas, setTareas] = useState(null);
  const [paginacion, setPaginacion] = useState<PaginacionData | null>(null);
  const [paginaActual, setPaginaActual] = useState(1);
  const [hasError, setHasError] = useState(false);
  const [esBusquedaFiltrada, setEsBusquedaFiltrada] = useState(false);
  const [datosBusqueda, setDatosBusqueda] = useState(null);
  const [paginacionBusqueda, setPaginacionBusqueda] = useState<PaginacionData | null>(null);

  limpiarCamposFactura();

  // Función global para manejar búsquedas filtradas
  (window as any).handleBusquedaFiltrada = (tipoFiltro: string, valorFiltro: string, datosBusquedaParam?: any, paginacionParam?: any) => {
    
    if (tipoFiltro === 'reset') {
      setEsBusquedaFiltrada(false);
      setDatosBusqueda(null);
      setPaginacionBusqueda(null);
      setPaginaActual(1);
      setData(null);
      setJsx(<></>);
      
      // Limpiar completamente el state de operacionCliente
      state.operacionCliente = [];
      state.listadoItemFacturas = [];
      state.paginacionBusqueda = null;
      
      return;
    }
    
    if (tipoFiltro === 'empty') {
      setEsBusquedaFiltrada(true);
      setDatosBusqueda([]);
      setPaginacionBusqueda(null);
      return;
    }
    
    if (tipoFiltro === 'update') {
      setEsBusquedaFiltrada(true);
      if (datosBusquedaParam) {
        setDatosBusqueda(datosBusquedaParam);
        setPaginacionBusqueda(paginacionParam);
      }
      return;
    }
    
    setEsBusquedaFiltrada(true);
    if (datosBusquedaParam) {
      setDatosBusqueda(datosBusquedaParam);
      setPaginacionBusqueda(paginacionParam);
    }
    setPaginaActual(1);
  };

  // Función global para limpiar búsqueda desde el menú
  (window as any).limpiarBusquedaYVolverBandeja = () => {
    // Limpiar todos los estados de búsqueda
    setEsBusquedaFiltrada(false);
    setDatosBusqueda(null);
    setPaginacionBusqueda(null);
    setPaginaActual(1);
    setData(null);
    setJsx(<></>);
    
    // Limpiar state global
    state.operacionCliente = [];
    state.listadoItemFacturas = [];
    state.paginacionBusqueda = null;
  };

  const fetchData = useCallback(async () => {
    if (isLoading) return;
    
    setIsLoading(true);
    setHasError(false);
    
    try {
      const dataResponse = !esBusquedaFiltrada 
        ? await getDataListadoMiBandejaV1('SEGFAC', paginaActual, 10)
        : await getDataListadoPaginado('SEGFAC', paginaActual, 10);
      
      setData(dataResponse);
      setPaginacion(dataResponse?.paginacion || null);
      
      setTareas([]);
      
      const operacionCreada = localStorage.getItem('codigoOperacionIniciada');
      if (operacionCreada && dataResponse?._operaciones) {
        const operacionEncontrada = dataResponse._operaciones.find((op: any) => op.id.toString() === operacionCreada);
        if (operacionEncontrada) {
          localStorage.removeItem('codigoOperacionIniciada');
        }
      }
      
    } catch (error) {
      console.error('Error al cargar datos:', error);
      setHasError(true);
    } finally {
      setIsLoading(false);
    }
  }, [esBusquedaFiltrada, paginaActual]);

  const handlePageChange = (nuevaPagina: number) => {
    if (esBusquedaFiltrada) {
      if ((window as any).handleBusquedaPaginada) {
        (window as any).handleBusquedaPaginada(nuevaPagina);
      }
      return;
    }
    
    if (nuevaPagina !== paginaActual) {
      setPaginaActual(nuevaPagina);
    }
  };

  useEffect(() => {
    if (!esBusquedaFiltrada) {
      fetchData();
    }
  }, [fetchData, esBusquedaFiltrada]);

  useEffect(() => {
    if (isLoading) {
      setJsx(<LoaderLista />);
      return;
    }

    if (hasError) {
      setJsx(
        <div className="text-center" style={{ marginTop: "20%", padding: "20px" }}>
          <h4>Error al cargar los datos</h4>
          <p>Por favor, intente nuevamente.</p>
          <button className="btn btn-primary" onClick={() => fetchData()}>
            Reintentar
          </button>
        </div>
      );
      return;
    }

    // Mostrar datos de búsqueda si están disponibles
    if (esBusquedaFiltrada) {
      if (datosBusqueda && datosBusqueda.length > 0) {
        setJsx(
          <ListArmado 
            operaciones={datosBusqueda} 
            tareas={tareas || []}
            paginacion={paginacionBusqueda}
            onPageChange={handlePageChange}
            esBusquedaFiltrada={true}
          />
        );
      } else if (datosBusqueda && datosBusqueda.length === 0) {
        setJsx(
          <div className="text-center" style={{ marginTop: "20%", padding: "20px" }}>
            <img src="../../img/abi4-error.svg" alt="Sin resultados" style={{ width: "100px", marginBottom: "20px" }} />
            <h4>No se encontraron resultados</h4>
            <p>No hay operaciones que coincidan con tu búsqueda.</p>
          </div>
        );
      }
      return;
    }

    // Mostrar datos normales
    if (data?._datos?.length > 0) {
      setJsx(
        <ListArmado 
          operaciones={data._datos} 
          tareas={tareas || []}
          paginacion={paginacion}
          onPageChange={handlePageChange}
          esBusquedaFiltrada={false}
        />
      );
    } else {
      setJsx(
        <div className="text-center" style={{ marginTop: "20%", padding: "20px" }}>
          <img src="../../img/abi4-error.svg" alt="Sin operaciones" style={{ width: "100px", marginBottom: "20px" }} />
          <h4>No hay operaciones disponibles</h4>
          <p>No se encontraron operaciones pendientes.</p>
        </div>
      );
    }
  }, [data, tareas, hasError, isLoading, esBusquedaFiltrada, datosBusqueda, paginacionBusqueda, paginacion]);

  return (
    <DatosProvider>
      <div id="idTabla" className="table-list">
        <div id="headerBandeja" className="table-list__header text-center">
          <h3 id="cabecera" className="mb-4">
            {esBusquedaFiltrada ? "Mi Bandeja - Resultados de Búsqueda" : "Mi Bandeja"}
          </h3>
        </div>
        <div id="listContenido">
          {jsx}
        </div>
      </div>
    </DatosProvider>
  );
};
