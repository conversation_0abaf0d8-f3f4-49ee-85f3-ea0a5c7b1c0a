/*
 
 ██╗  ██╗███████╗ █████╗ ██████╗ ███████╗██████╗ 
 ██║  ██║██╔════╝██╔══██╗██╔══██╗██╔════╝██╔══██╗
 ███████║█████╗  ███████║██║  ██║█████╗  ██████╔╝
 ██╔══██║██╔══╝  ██╔══██║██║  ██║██╔══╝  ██╔══██╗
 ██║  ██║███████╗██║  ██║██████╔╝███████╗██║  ██║
 ╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝╚═════╝ ╚══════╝╚═╝  ╚═╝
 - Estilos para el buscador principal                                                
 
*/

@include block('header-ppal') {
    
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-between;
    position: relative;
    border-bottom: 1px solid $gray-200;
    margin-bottom: ($spacer * 2);
    
    // Input Buscador
    @include element('search') {
        padding: $spacer $spacer $spacer ($spacer * 3);
        border:none;
    
            @include media-breakpoint-down(xs) {
                max-width: 115px;
            }

            @include media-breakpoint-up(md){
                flex: 1 0 auto;
            }

    }

    // Icon Search
    @include element('icon-search') {
        position: absolute;
        left: $spacer;

        // Hack IE11
        @media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
            top: $spacer;
        }

    }
    
    // Box Notification User
    @include element('user') {
        display: flex;
        align-items: center;
    }


    // Cantidad de notificacion
    @include element('notification') {
        position: absolute;
        top: 0;
        right: 5px;
        width: 18px;
        height: 18px;
        border-radius: 50px;
        font-size: 0.7rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color:$white;
        background-color:$danger-500;
    }
    
    // Al Activar
    // Ocultamos datos del user
    // Expandimos el input
    @include state('active') {

        .header-ppal__user   { display: none; }
        .header-ppal__search { 
            max-width: 100%;
            width: 100%; 
        }
 
    }

    

}//HeaderPpal


