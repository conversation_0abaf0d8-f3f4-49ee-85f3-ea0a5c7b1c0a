/*
 
 ██╗      █████╗ ██╗   ██╗ ██████╗ ██╗   ██╗████████╗   ███╗   ███╗███████╗███╗   ██╗██╗   ██╗      ███╗   ███╗ ██████╗ ██████╗ ██╗██╗     ███████╗
 ██║     ██╔══██╗╚██╗ ██╔╝██╔═══██╗██║   ██║╚══██╔══╝   ████╗ ████║██╔════╝████╗  ██║██║   ██║      ████╗ ████║██╔═══██╗██╔══██╗██║██║     ██╔════╝
 ██║     ███████║ ╚████╔╝ ██║   ██║██║   ██║   ██║█████╗██╔████╔██║█████╗  ██╔██╗ ██║██║   ██║█████╗██╔████╔██║██║   ██║██████╔╝██║██║     █████╗  
 ██║     ██╔══██║  ╚██╔╝  ██║   ██║██║   ██║   ██║╚════╝██║╚██╔╝██║██╔══╝  ██║╚██╗██║██║   ██║╚════╝██║╚██╔╝██║██║   ██║██╔══██╗██║██║     ██╔══╝  
 ███████╗██║  ██║   ██║   ╚██████╔╝╚██████╔╝   ██║      ██║ ╚═╝ ██║███████╗██║ ╚████║╚██████╔╝      ██║ ╚═╝ ██║╚██████╔╝██████╔╝██║███████╗███████╗
 ╚══════╝╚═╝  ╚═╝   ╚═╝    ╚═════╝  ╚═════╝    ╚═╝      ╚═╝     ╚═╝╚══════╝╚═╝  ╚═══╝ ╚═════╝       ╚═╝     ╚═╝ ╚═════╝ ╚═════╝ ╚═╝╚══════╝╚══════╝
 - Estilos para el menu y submenu                                                                                                                                                   
 
*/

// Menu Aside
/////////////////////////////////////////////////

@include block('menu-mobile') {
    
    position: fixed;
    bottom: 0;
    right: 0;
    left: 0;
    background-color: $blue;
    z-index: 9999;
    box-shadow: 0 -4px 5px rgba(0,0,0,.3);
    @include media-breakpoint-up(xl) { display: none; }
    
    ul {

       list-style: none;
       margin:0;
       padding: 0;
    }

    a {
        display: flex;
        flex-direction: column;
        color:$white;
        font-size: $font-size-sm;
        padding:($spacer / 2) $spacer;
        text-decoration: none;
    }

    span {
        display: block;
        padding-top: 3px;
        opacity: 0;
        height: 0;
        transform: translateY(50px);
        transition: all .3s ease-in; 
    }

    .icon {
        display: block;
        color:$primary-100;
        margin: 0 auto;
    }

    
    @include element('active') {
        .icon { color:$white }
        span  { 
            opacity: 1;
            transform: translateY(0); 
            height: auto;
        }       
    }



}//endMenuAside
