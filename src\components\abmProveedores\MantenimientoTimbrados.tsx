import React from 'react';

const MantenimientoTimbrados = () => {
  return (
    <div className='mt-2'>
      <div className='card m-2' id="mantenimiento-timbrados">
        <div className='card-body d-flex flex-column'>
          <div className='' style={{ width: '25%' }}>
            <div className='border p-1 text-center m-1 mw-100' style={{ width: '100%' }}>grillaControl</div>
          </div>
          <div className='d-flex align-items-center flex-wrap'>
            <input type="date" className='mr-2' />
            <div className='border p-2 text-center m-1'>(MainView)</div>
            <div className='border p-2 text-center m-1'>gridControl</div>
            <a href='#' className='ml-2'>(Click here to change view)</a>
          </div>
          <hr className='dropdown-divider' />
          <div className='d-flex justify-content-center'>
            <button className='btn btn-outline-secondary m-2'>Retrieve Details</button>
            <button className='btn btn-outline-secondary m-2'>Run Designer</button>
          </div>
        </div>
      </div>
      <div className='d-flex justify-content-end mb-2'>
        <button className='btn btn-success mx-4'>Refresh</button>
      </div>
      <div className='card m-2'>
        <div className='card-header'>
          <div className='font-weight-bold'>Alta de Timbrados</div>
        </div>
        <div className='card-body'>
          <div className='d-flex'>
            <label className=''>Nro Timbrado</label>
            <input type="number" className='mx-2' />
          </div>
          <div className='row d-flex align-items-center flex-wrap mt-4'>
            <fieldset className='form-group border p-3 col-md-8'>
              <legend style={{ fontSize: "17px" }} className='w-auto px-2 font-weight-bold'>Vencimiento</legend>
              <div className='d-flex flex-wrap w-75'>
                <div className='d-flex align-items-center'>
                  <label>Mes</label>
                  <select className='mx-2 my-1' defaultValue="">
                    <option value=""></option>
                    <option value="Enero">Enero</option>
                    <option value="Febrero">Febrero</option>
                    <option value="Marzo">Marzo</option>
                    <option value="Abril">Abril</option>
                    <option value="Mayo">Mayo</option>
                    <option value="Junio">Junio</option>
                    <option value="Julio">Julio</option>
                    <option value="Agosto">Agosto</option>
                    <option value="Septiembre">Septiembre</option>
                    <option value="Octubre">Octubre</option>
                    <option value="Noviembre">Noviembre</option>
                    <option value="Diciembre">Diciembre</option>
                  </select>
                </div>
                <div className='d-flex align-items-center'>
                  <label className=''>Año</label>
                  <input type='text' className='my-1 mx-2' />
                </div>
              </div>
            </fieldset>
            <div className='d-flex flex-column align-items-center col-md-4'>
              <button className='btn btn-primary my-2'>Guardar</button>
              <button className='btn btn-secondary'>Cancelar</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default MantenimientoTimbrados;