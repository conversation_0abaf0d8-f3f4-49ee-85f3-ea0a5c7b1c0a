/*
 
 ██╗      █████╗ ██╗   ██╗ ██████╗ ██╗   ██╗████████╗   ██████╗  █████╗ ███████╗██╗  ██╗██████╗  ██████╗  █████╗ ██████╗ ██████╗ 
 ██║     ██╔══██╗╚██╗ ██╔╝██╔═══██╗██║   ██║╚══██╔══╝   ██╔══██╗██╔══██╗██╔════╝██║  ██║██╔══██╗██╔═══██╗██╔══██╗██╔══██╗██╔══██╗
 ██║     ███████║ ╚████╔╝ ██║   ██║██║   ██║   ██║█████╗██║  ██║███████║███████╗███████║██████╔╝██║   ██║███████║██████╔╝██║  ██║
 ██║     ██╔══██║  ╚██╔╝  ██║   ██║██║   ██║   ██║╚════╝██║  ██║██╔══██║╚════██║██╔══██║██╔══██╗██║   ██║██╔══██║██╔══██╗██║  ██║
 ███████╗██║  ██║   ██║   ╚██████╔╝╚██████╔╝   ██║      ██████╔╝██║  ██║███████║██║  ██║██████╔╝╚██████╔╝██║  ██║██║  ██║██████╔╝
 ╚══════╝╚═╝  ╚═╝   ╚═╝    ╚═════╝  ╚═════╝    ╚═╝      ╚═════╝ ╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝╚═════╝  ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═╝╚═════╝ 
 - Estructura básica para el dashboard
    +------------------------------------------------------+
    |            ||         ||                             |
    | Menu       || submenu || Contenido                   |
    |            ||         ||                             |
    |            ||         ||                             |
    |            ||         ||                             |
    |            ||         ||                             |
    +------------------------------------------------------+

    @media (max-width: 575.98px)
    @include media-breakpoint-down(xs) { ... }

    @media (max-width: 767.98px)
    @include media-breakpoint-down(sm) { ... }

    @media (max-width: 991.98px)
    @include media-breakpoint-down(md) { ... }

    @media (max-width: 1199.98px)
    @include media-breakpoint-down(lg) { ... }
*/


// Layout
/////////////////////////////////////////////////

@include block('layout') {
    
    @include media-breakpoint-down(sm) {
        margin-bottom: ($spacer * 5);
    }
    
    // Desde 768px pasa a flex
    @include media-breakpoint-up(md) { 
        display:flex;
        height: 100%;
        align-items: stretch;
    }

        @include element('menu') {
            display: none;
            flex-basis: 240px;
            background-color: $blue;
                
                // Desde 1200px pasa a flex
                @include media-breakpoint-up(xl){
                    display:flex;
                }
        }
        
        // Submenu por default en none, agregando
        // la clase active pasa a flex
        @include element('submenu'){
            display: none;
            flex-direction: column;
            flex-basis: 220px;
            background-color: $gray-100;
            
            // Desde 1200px pasa a flex
            @include modifier('active') {
                @include media-breakpoint-up(xl) {
                    display: flex;
                }
            }
        }
        
        // El contenido ocupa el resto 
        // del espacio
        @include element('content'){
            flex:1 0 0;
            background-color: $white;
            margin-bottom: ($spacer * 3);
        }

}


