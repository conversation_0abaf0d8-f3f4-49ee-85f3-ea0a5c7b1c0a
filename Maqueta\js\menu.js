let menu = [
    {
      "accion": null,
      "identificador": 100000,
      "nivel": "1",
      "nombre": "Productos y Servicios",
      "subniveles": [
        {
          "accion": "ProductosServicios/Solicitud",
          "identificador": 100012,
          "nivel": "2",
          "nombre": "Bandeja de solicitudes"
        }
      ],
    },
    {
      "accion": null,
      "identificador": 100000,
      "nivel": "1",
      "nombre": "Clientes",
      "subniveles": [
        {
          "accion": "ProductosServicios/Solicitud",
          "identificador": 100012,
          "nivel": "2",
          "nombre": "Alta de Clientes"
        }
      ],
    },
    {
      "accion": null,
      "identificador": 100000,
      "nivel": "1",
      "nombre": "Solicitar",
      "subniveles": null,
    }
    
  ];

  const renderMenu = (item) =>{
    document.getElementById('menu').innerHTML = item;
  }

  const buildMenu = (menu) =>{
      let item = '';
      menu.forEach((menu, i) => {
        if (menu.nivel == "1" && menu.itemPadre == "S" && menu.subniveles == null) {
          item = `
            <li class="nav-item">
                <a href="${(menu.accion = 'null' ? '#' : menu.accion)}" class="nav-link">${menu.nombre}</a>
            </li>
          `;
        } else if (menu.subniveles != null){
          item += `
          <li class="nav-item">
              <a href="#" class="nav-link" data-toggle="collapse" data-target="#${menu.nombre.replace(/\s/g, '')}" aria-expanded="false" aria-controls="${menu.nombre.replace(/\s/g, '')}">${menu.nombre} <i class="fa fa-chevron-down"></i></a>
              <ul class="nav collapse" id="${menu.nombre.replace(/\s/g, '')}">
          `;
          menu.subniveles.forEach((menu, i) =>{
            item += `
                <li class="nav-item"><a class="nav-link" href="${window.location.origin}/${menu.accion}">${menu.nombre}</a></li>
            `;
          });
          item += `
              </ul>
            </li>
          `;
        } else {
          item += `
            <li class="nav-item">
                <a href="${(menu.accion = 'null' ? '#' : menu.accion)}" class="nav-link">${menu.nombre}</a>
            </li>
          `;
        }
    });
    renderMenu(item);
  }
  
  buildMenu(menu);
