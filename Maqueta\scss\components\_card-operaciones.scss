/*
 
  ██████╗ █████╗ ██████╗ ██████╗        ██████╗██╗   ██╗███████╗███╗   ██╗████████╗ █████╗ ███████╗
 ██╔════╝██╔══██╗██╔══██╗██╔══██╗      ██╔════╝██║   ██║██╔════╝████╗  ██║╚══██╔══╝██╔══██╗██╔════╝
 ██║     ███████║██████╔╝██║  ██║█████╗██║     ██║   ██║█████╗  ██╔██╗ ██║   ██║   ███████║███████╗
 ██║     ██╔══██║██╔══██╗██║  ██║╚════╝██║     ██║   ██║██╔══╝  ██║╚██╗██║   ██║   ██╔══██║╚════██║
 ╚██████╗██║  ██║██║  ██║██████╔╝      ╚██████╗╚██████╔╝███████╗██║ ╚████║   ██║   ██║  ██║███████║
  ╚═════╝╚═╝  ╚═╝╚═╝  ╚═╝╚═════╝        ╚═════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝   ╚═╝   ╚═╝  ╚═╝╚══════╝
  Estilos propios para card-cuentas                                                                                                 
 
*/

@include block('card-operacion') {
    padding: $spacer;
    cursor: pointer;
    border-radius: 4px;
    background-color: $white;
    border:1px solid $gray-200;
    border-radius: $border-radius;

    @include element('picture') {
        width:65px;
        height: 65px;
        border-radius: 100%;
        overflow: hidden;
        border:1px solid $gray-200;
        margin-bottom: 0;

        img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        @include modifier('logo') {
            border-radius: 4px;
        }
    }

    @include element('text') {
        padding-left: $spacer;
        flex: 1 0 0;
    }


    @include modifier('active') {
        transform: scale(1);
        border-bottom: 4px solid $blue;
        box-shadow: $box-shadow-sm;
    }
    
}

