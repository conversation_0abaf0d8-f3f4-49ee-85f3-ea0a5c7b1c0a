/// <reference types="webpack/module" />

import React from "react";
import { createRoot } from "react-dom/client";
import { ModalPortal } from "./ModalPortal";

'use strict'

export const BtnAdjuntosFacturas = (props: any) => {

  const abrirModal = () => {
    const node = document.createElement("div");
    node.id = 'ModalAdjuntos'
    let _root = document.getElementById('container')
    _root.appendChild(node)
    const _modalEl = document.getElementById('ModalAdjuntos')
    const _rootModal = createRoot(_modalEl)
    _rootModal.render(<ModalPortal></ModalPortal>)
  }

  return (
    <React.StrictMode>
      <>
        <span className="pull-right hidden-print">
          <a onClick={(e) => { abrirModal() }} data-toggle="modal" data-target="#filtrosAvanzados" className="btn btn-sm btn-white m-b-10 p-l-5" id="btn-adjuntar"><i
            className="fa fa-file t-plus-1 text-danger fa-fw fa-lg"></i>Adjuntar</a>
        </span>
      </>
    </React.StrictMode>
  )
}