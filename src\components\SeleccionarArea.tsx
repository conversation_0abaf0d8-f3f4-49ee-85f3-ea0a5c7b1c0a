import React, { useState } from 'react';
import { obtenerUsuario } from '../comm/obtenerUsuario';
import { Estados } from '../entities/Enums/estadosRespuesta';
import { useProveedor } from './contextProvider/datosProveedorContext';
import ModalValidacionInicioDeSeguimiento from './modales/ModalValidacionInicioDeSeguimiento';

interface SelectArea {
  inputData: string;
  dataList: string[];
  respuestaIdUsuarios: any[];
  operacionSeguimiento: string;
}

const SeleccionarArea = () => {

  const { state } = useProveedor();

  const [data, setData] = useState<SelectArea>({
    inputData: '',
    dataList: [],
    respuestaIdUsuarios: [],
    operacionSeguimiento: ''
  });

  const [modalValidacionFallida, setModalValidacionFallida] = useState(false);
  const cerrarModalValidacionFallida = () => { setModalValidacionFallida(false); };
  const [validacionInicioDeSeguimiento, setValidacionInicioDeSeguimiento] = useState("");
  const abrirModalYaExisteElAutorizante = () => { setModalValidacionFallida(true); setValidacionInicioDeSeguimiento("EL AUTORIZANTE YA SE ENCUENTRA EN LA LISTA."); };
  const abrirModalNoSeEncuentraElAutorizante = () => { setModalValidacionFallida(true); setValidacionInicioDeSeguimiento(`NO SE ENCUENTRA EL AUTORIZANTE: ${data.inputData}`); };
  const abrirModalValidacionCantidadMaximaDeAutorizantes = () => { setModalValidacionFallida(true); setValidacionInicioDeSeguimiento("SÓLO SE PERMITEN TRES AUTORIZANTES."); };

  let codigoAccionItem = localStorage.getItem("itemCodigoAccion");

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setData({ ...data, inputData: e.target.value });
  }

  const obtenerDatosAutorizantes = async () => {
    await obtenerUsuario(data.inputData);

    let respuestaEstadoUsuario = localStorage.getItem("respuestaEstadoUsuario");

    if (data.dataList.length < 3) {
      if (JSON.parse(respuestaEstadoUsuario).toString() === Estados.estadoExitoso.toString()) {
        if (data.dataList.includes(data.inputData)) {
          abrirModalYaExisteElAutorizante();
          return false;
        } else {
          const newDataList = [...data.dataList];

          newDataList.push(data.inputData);
          const newArrayID = [...data.respuestaIdUsuarios, JSON.parse(localStorage.getItem("respuestaIdUsuario"))];

          setData((prevData) => ({
            ...prevData,
            dataList: newDataList,
            inputData: '',
            respuestaIdUsuarios: newArrayID
          }));

          console.log("newArrayID:", newArrayID);
          state.arrayAutorizantes = newArrayID;
          localStorage.setItem("arrayID", JSON.stringify(newArrayID));
        }
      } else {
        abrirModalNoSeEncuentraElAutorizante();
        return false;
      }
    } else {
      abrirModalValidacionCantidadMaximaDeAutorizantes();
      return false;
    }
  }

  const handleRemoveItem = (index: number, e: any) => {
    e.preventDefault();
    const updatedList = [...data.dataList];
    updatedList.splice(index, 1);
    const updatedRespuestaIdUsuarios = [...data.respuestaIdUsuarios];
    updatedRespuestaIdUsuarios.splice(index, 1);
    setData({
      ...data,
      dataList: updatedList,
      respuestaIdUsuarios: updatedRespuestaIdUsuarios,
    });
    localStorage.setItem("arrayID", JSON.stringify(updatedRespuestaIdUsuarios));
    console.log("updatedList:", updatedList);
    state.listaAutorizantesActualizada = updatedList;
    console.log("data.respuestaIdUsuarios:", data.respuestaIdUsuarios);
  };

  localStorage.setItem("autorizantes", JSON.stringify(data.dataList));

  return (
    <div className='d-flex align-items-center justify-content-center flex-wrap'>
      {codigoAccionItem === "SF0002" || localStorage.getItem("areaSeleccionada") ? <div id=""><form>
        <div className="form-group col-md-6">
          <input type="text" placeholder="Autorizante area" name="inputData" id="seleccionarArea" value={data.inputData} onChange={handleChange} onKeyDown={(e) => {
            if (e.key === 'Tab' || e.key === 'Enter') {
              e.preventDefault();
              obtenerDatosAutorizantes();
              { data.inputData === "" }
            }
          }} />
        </div>
        <div>
          <ol className="">
            {data.dataList.map((item, index) => (
              <div key={index} className="d-flex justify-content-between align-items-center my-2">
                <li className="my-1">{item}</li>
                <div className="d-flex justify-content-end">
                  <div className="font-weight-bold text-danger" style={{ cursor: 'pointer' }} title="Eliminar" onClick={(e: any) => handleRemoveItem(index, e)}>x</div>
                </div>
              </div>
            ))}
          </ol>
        </div>
      </form>
      </div> : ""}
      {<ModalValidacionInicioDeSeguimiento show={modalValidacionFallida} onClose={cerrarModalValidacionFallida} message={validacionInicioDeSeguimiento} />}
    </div>
  )
}

export default SeleccionarArea