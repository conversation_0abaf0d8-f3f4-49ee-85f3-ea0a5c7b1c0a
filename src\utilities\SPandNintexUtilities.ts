import { getUserIdSP } from '../comm/ApiSPNintexType';
import { idUsuariosSitios } from '../comm/SPsitiosOBJ'
import { getUrlSPBase } from './contextInfo';
import { getUserName } from './_spPageContextInfo';

'use strict'

export const storeIdUserSitios = async () => {
  const prefix = "i:0#.w|bancontinental\\";
  let siteUrl = getUrlSPBase();
  const accountName = prefix + getUserName()
  idUsuariosSitios.forEach(async (item) => {
    siteUrl = siteUrl + item.site
    let id = await getUserIdSP(accountName, siteUrl)
    localStorage.setItem('idSPuserPerSite' + item.codProceso, id)
  })
}

export type IdUserSite =
  {
    id: number,
    site: string
  }
