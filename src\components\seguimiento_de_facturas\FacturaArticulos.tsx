/// <reference types="webpack/module" />

import React, { useContext, useState } from "react";
import { FacturaArticulosTable } from "./FacturaArticulosTable";
import FacturaFormContext from "../contextProvider/counterContext";
import columnasArticulosArr from "../../entities/Objects/columnasArticulos";
export const FacturaArticulosComp = (props: any) => {
  const _FacturaFormContext = useContext(FacturaFormContext)
  const idBody = 'idBody' + _FacturaFormContext.counter
  const _columnas = columnasArticulosArr

  return (
    <div className="collapse" id={props.idCollapse}>
      <div id='factura-Articulos' className="invoice-content">
        <FacturaArticulosTable id={idBody} columnas={_columnas} />
      </div>
    </div>
  )
}