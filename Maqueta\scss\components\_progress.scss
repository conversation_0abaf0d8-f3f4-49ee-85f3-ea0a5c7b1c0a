#progress-data{
    .progress {
        width: 100px;
        height: 100px;
        background: none;
        position: relative;
        margin: 0 auto;
      }
      
      .progress::after {
        content: "";
        width: 100%;
        height: 100%;
        border-radius: 50%;
        border: 15px solid #D1E4F9;
        position: absolute;
        top: 0;
        left: 0;
      }
      
      .progress>span {
        width: 50%;
        height: 100%;
        overflow: hidden;
        position: absolute;
        top: 0;
        z-index: 1;
      }
      
      .progress .progress-left {
        left: 0;
      }
      
      .progress .progress-bar {
        width: 100%;
        height: 100%;
        background: none;
        border-width: 15px;
        border-style: solid;
        position: absolute;
        top: 0;
      }
      
      .progress .progress-left .progress-bar {
        left: 100%;
        border-top-right-radius: 80px;
        border-bottom-right-radius: 80px;
        border-left: 0;
        -webkit-transform-origin: center left;
        transform-origin: center left;
      }
      
      .progress .progress-right {
        right: 0;
      }
      
      .progress .progress-right .progress-bar {
        left: -100%;
        border-top-left-radius: 80px;
        border-bottom-left-radius: 80px;
        border-right: 0;
        -webkit-transform-origin: center right;
        transform-origin: center right;
      }
      
      .progress .progress-value {
        position: absolute;
        top: 0;
        left: 0;
        font-weight: bold;
        line-height: 2rem;
        font-size:1rem;
        text-align: center;
      }
    
      .border-key-lime-pie{
          border-color: $key-lime-pie;
        //background: #BECD2E !important;
      }
}