#gallery {
	width: 100%;
    display: flex;
	margin:30px auto;
}

#gallery a {
	text-decoration:none;
}

#gallery .item a { 
    overflow: hidden;
}

.lightbox-cap {
    position: absolute;
    color: #fff;
    text-align: center;
    width: 100%;
    top: 35px;
}

#gallery .item a img {
	width: 100%; 
	align-self: center;
}

.lightbox {
	/** Hide the lightbox */
	width: 0;
    height: 0;
    overflow: hidden;
	
	/** Apply basic lightbox styling */
	position: fixed;
	z-index: 9999;
	top: 0;
	left: 0;
	color:#333333;
	transition: opacity .3s ease-in-out, height .3s ease-in-out;
    background: rgb(0 0 0 / 75%);
}

/** Show lightbox when it is target */
.lightbox:target {
  width: 100%;
  height: 100%;
  transition: opacity .3s ease-in-out, height .3s ease-in-out;
}

.lightbox .box {
    width: 60%;
    min-width: 500px;
    margin: 2% auto;
    position: relative;
    z-index: 2;
    margin-top: 70px;
}

.lightbox .title {
	margin:0;
	padding:0 0 10px 0px;
	border-bottom:1px #ccc solid;
	font-size:22px;
}

.lightbox .content {
	display: block;
    position: relative;
    text-align: center;
}

.lightbox .content .desc {
	z-index:99;
	bottom:0;
	position:absolute;
	padding:10px;
	margin:0 0 4px 0;
	background:rgba(0,0,0,0.8);
	color:#fff;
	font-size:17px;
	opacity:0;
	text-align: left;
	transition: opacity ease-in-out 0.5s;
}	
	
.lightbox .content:hover .desc	{
	opacity:1;
}

.lightbox .close {
	display: block;
    text-decoration: none;
    font-family: Gotham, "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 30px;
    color: #ffffff;
    position: absolute;
    z-index: 9999;
    top: 20px;
    right: 25px;
}

.prev {
	position: absolute;
    z-index: 999;
    left: 20px;
    color: #fff;
    font-size: 30px;
    top: 40%;
}

.next {
	position: absolute;
    z-index: 999;
    right: 20px;
    color: #fff;
    font-size: 30px;
    top: 40%;
}
	
.clear {
	display:block;
	clear:both;
}

.close_out {
  display: block;
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

@media screen and (max-width : 500px){
    .lightbox .box {
        width: 100%;
        min-width: auto;
        margin-top: 187px;
    }

    .lightbox-cap {
        top: 145px;
    }
}