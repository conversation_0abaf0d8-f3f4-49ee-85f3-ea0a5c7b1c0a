/* Contenedor principal */
.contenedor-principal {
  display: flex;
  height: 100vh;
}

.contenedor-principal .descripcion {
  margin-bottom: 20px;
}

.contenedor-principal .btn-custom {
  background-color: white;
  color: blue;
  border: 1px solid blue;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  transition: all 0.3s ease;
}

.contenedor-principal .btn-custom:hover {
  background-color: blue;
  color: white;
}

.contenedor-principal .descripcion strong {
  font-size: 16px;
  color: #333;
}

.contenedor-principal .descripcion p {
  font-size: 14px;
  color: #666;
  margin: 5px 0 15px;
}

.contenedor-principal .radio-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.contenedor-principal .radio-label {
  display: flex;
  align-items: center;
  gap: 5px;
}

.contenedor-principal .radio-label input[type="radio"] {
  margin: 0;
  vertical-align: middle;
}

.contenedor-principal .radio-label,
.contenedor-principal .busqueda-gestion {
  display: flex;
  align-items: center;
  font-size: 14px;
  margin-right: 10px;
  font-weight: 600;
}

.contenedor-principal input[type="radio"] {
  accent-color: #002D72;
  margin-right: 5px;
}

.contenedor-principal .input-busqueda {
  display: flex;
  gap: 8px;
  width: 100%;
  justify-content: flex-start;
}

.contenedor-principal input[type="text"] {
  padding: 10px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px;
  outline: none;
  width: 200px;
  margin-right: 10px;
}

.contenedor-principal input[type="text"]:focus {
  border-color: #002D72;
}

/* Botón */
.contenedor-principal button {
  background-color: white;
  color: #002D72;
  border: 1px solid #002D72;
  padding: 8px 15px;
  font-size: 14px;
  font-weight: 600;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.contenedor-principal button:hover {
  background-color: #002D72;
  color: white;
}

/* Bandeja */
.contenedor-principal .bandeja {
  width: 45%;
  padding: 20px;
  background-color: #f7f7f7;
  border-right: 1px solid #ccc;
}

.contenedor-principal .titulo-bandeja {
  margin-bottom: 20px;
  font-size: 20px;
}

.contenedor-principal .filtros button {
  margin-right: 10px;
  padding: 8px 12px;
  border: none;
  background-color: #ddd;
  cursor: pointer;
}

.contenedor-principal .filtros .activo {
  background-color: #4e73df;
  color: white;
}

.contenedor-principal .filtros {
  display: flex;
  gap: 10px;
}

.contenedor-principal .filtro-fechas {
  margin: 15px 0;
  display: flex;
  align-items: center;
  gap: 15px;
}

.contenedor-principal .filtro-fechas-desde,
.contenedor-principal .filtro-fechas-hasta {
  display: flex;
  align-items: center;
  gap: 5px;
}

.contenedor-principal .filtro-fechas label {
  font-weight: bold;
}

.contenedor-principal .filtro-fechas span {
  font-size: 14px;
  color: #333;
}

.contenedor-principal .filtro-fechas input {
  padding: 5px;
  min-width: 150px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.contenedor-principal .busqueda {
  display: flex;
  align-items: center;
}

.contenedor-principal .busqueda input {
  flex-grow: 1;
  padding: 5px;
}

.contenedor-principal .busqueda button {
  margin-left: 5px;
  padding: 5px;
}

.contenedor-principal .solicitud-item {
  border-bottom: 1px solid #ddd;
  padding: 10px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Estado solicitud */
.contenedor-principal .estado-solicitud {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  text-align: right;
  gap: 5px;
}

.contenedor-principal .texto-cuadro {
  flex: 1;
  display: flex;
  align-items: center;
}

.contenedor-principal .texto-estado {
  font-size: 14px;
  font-weight: bold;
  border-radius: 5px;
  padding: 2px 8px;
}

.contenedor-principal .texto-estado.rechazado {
  color: #ffffff;
  background-color: #f44336;
  border: 1px solid #f44336;
}

.contenedor-principal .texto-estado.pendiente {
  color: #ffffff;
  background-color: #ffd700;
  border: 1px solid #ffd700;
}

.contenedor-principal .texto-estado.aprobado {
  color: #ffffff;
  background-color: #68A7AD;
  border: 1px solid #68A7AD;
}

.contenedor-principal .solicitud-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
}

.contenedor-principal .checkbox-solicitud {
  margin-right: 10px;
}

.contenedor-principal .detalle-solicitud {
  flex: 1;
}

/* Icono y fecha */
.contenedor-principal .icono-fecha {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
}

.contenedor-principal .icono-estado {
  font-size: 18px;
}

.contenedor-principal .icono.pendiente {
  color: #ffd700;
}

.contenedor-principal .icono.aprobado {
  color: #68A7AD;
}

.contenedor-principal .icono.rechazado {
  color: #f44336;
}

.contenedor-principal .fecha-estado {
  font-size: 12px;
  color: #721c24;
}

/* Gestión */
.contenedor-principal .gestion {
  width: 65%;
  padding: 20px;
}

.contenedor-principal .titulo-gestion {
  margin-bottom: 20px;
  font-size: 20px;
}

.contenedor-principal .cliente-adicional input {
  margin-bottom: 20px;
  font-size: 20px;
}

.contenedor-principal .opciones-busqueda label {
  margin-right: 10px;
}

.contenedor-principal .datos-cliente {
  display: flex;
  align-items: center;
  gap: 15px;
  border-top: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  padding: 10px 0;
}

.contenedor-principal .avatar {
  width: 40px;
  height: 40px;
  background-color: #C3B63E;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  flex-shrink: 0;
}

.contenedor-principal .info-cliente {
  display: flex;
  flex-direction: column;
  gap: 5px;
  font-size: 14px;
}

.contenedor-principal .info-cliente p {
  margin: 0;
  color: #333;
}

.contenedor-principal .info-cliente p span {
  font-weight: bold;
  margin-right: 5px;
}

.contenedor-principal .info-cliente .linea-horizontal {
  display: flex;
  gap: 20px;
  align-items: center;
}

.contenedor-principal .checkbox-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-start;
  padding-left: 20px;
}

.contenedor-principal .emitir-tc {
  display: flex;
  margin-top: 15px;
  justify-content: flex-start;
  gap: 10px;
}

.contenedor-principal .botones-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 20px;
}

.contenedor-principal .btn-cancelar {
  background-color: #ccc;
  color: #333;
  border: none;
  padding: 8px 12px;
  border-radius: 5px;
  cursor: pointer;
}

.contenedor-principal .btn-guardar {
  background-color: #0056b3;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 5px;
  cursor: pointer;
}

.contenedor-principal .input-custom {
  width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  box-sizing: border-box;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.contenedor-principal .resultado-cliente {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin: 20px 0;
}

.contenedor-principal .input-custom::placeholder {
  font-size: 14px;
  color: #888;
}

.contenedor-principal .cliente-adicional-info {
  display: flex;
  align-items: center;
  margin-top: 20px;
}

.contenedor-principal .resultado-titulos,
.contenedor-principal .resultado-datos {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10px;
  text-align: center;
  font-size: 14px;
}

.contenedor-principal .resultado-titulos div {
  font-weight: bold;
}

.contenedor-principal .resultado-datos div {
  font-size: 14px;
  color: #333;
}

.contenedor-principal .linea-separadora {
  height: 1px;
  background-color: #ccc;
  margin: 10px 0;
}

.contenedor-principal .resultado-cliente {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 10px 0;
}

.contenedor-principal .resultado-cliente .avatar {
  background-color: #C3B63E;
  color: #ffffff;
  font-weight: bold;
  font-size: 16px;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  margin-right: 10px;
  margin-left: 10px;
  flex-shrink: 0;
}

.contenedor-principal .cliente-adicional-info {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.contenedor-principal .resultado-titulos,
.contenedor-principal .resultado-datos {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 2px;
  text-align: left;
  font-size: 14px;
}

.contenedor-principal .resultado-titulos div {
  font-weight: bold;
  margin-bottom: 0;
}

.contenedor-principal .resultado-datos div {
  font-size: 14px;
  color: #333;
  margin-top: 0;
}

.contenedor-principal .linea-separadora {
  height: 1px;
  background-color: #000000;
  margin: 5px 0;
}

.contenedor-principal .nombre-cliente {
  text-align: left;
  font-size: 14px;
  font-weight: bold;
  color: #333;
}

.contenedor-principal .btn-Rechazar {
  background-color: #0056b3;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 5px;
  cursor: pointer;
}

.contenedor-principal .botones-autorizante {
  display: flex;
  gap: 8px;
  width: 100%;
  justify-content: flex-start;
}

.contenedor-principal .input-busqueda-adicional {
  display: flex;
  gap: 8px;
  width: 100%;
  justify-content: flex-start;
  align-items: center;
}

.contenedor-principal .btn-custom2 {
  background-color: white;
  color: blue;
  border: 1px solid blue;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  text-align: center;
  transition: all 0.3s ease;
}

.contenedor-principal .btn-custom2:hover {
  background-color: blue;
  color: white;
}

.checkbox-item label {
  margin-left: 8px; /* Ajusta según sea necesario */
}