import { MenuElemento } from "../../entities/MenuElemento";

export type MenuElementoResponse = {
  elementos: MenuElemento[]
}


export function isMenuElementoResponse(menuElementoResponse: any): menuElementoResponse is MenuElementoResponse {
  return (menuElementoResponse as MenuElementoResponse).elementos !== undefined;
}

export function isMenuElemento(menuElemento: any): menuElemento is MenuElemento[] {
  return (menuElemento as MenuElemento[]).length > 0;
}