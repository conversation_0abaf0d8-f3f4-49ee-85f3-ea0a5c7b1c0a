import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';

interface ModalProps {
  abrir: boolean;
  cerrar: () => void;
}

const ModalFacturaNotificacionRegistrada: React.FC<ModalProps> = ({ abrir, cerrar }) => {
  return (
    <div>
      <Modal show={abrir} onHide={cerrar}>
        <Modal.Header>
          <Modal.Title className='text-secondary'></Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {/* <p className='text-center font-weight-bold text-danger'>Ya se realizó el inicio de seguimiento de esta Factura.</p> */}
          <p className='text-center font-weight-bold text-danger'>La Factura se encuentra registrada.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={cerrar}>Cerrar</Button>
        </Modal.Footer>
      </Modal>
    </div>
  )
}

export default ModalFacturaNotificacionRegistrada;