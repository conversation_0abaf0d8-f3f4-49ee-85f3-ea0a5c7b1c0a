import React, { useContext, useState } from 'react';
import { <PERSON><PERSON><PERSON>rovider, Dispatch, useProveedor } from '../contextProvider/datosProveedorContext';
import FacturaFormContext from '../contextProvider/counterContext';
import Loader from '../loader/Loader';
import CampoTimbrado from '../abmProveedores/CampoTimbrado';
import { getApiProveedorFacturaHeaders } from '../../comm/contracts/ApiBpmPoliticasProcesosRequestHeaders';
import { getAmbienteContexto, getUrlBase } from '../../utilities/contextInfo';
import axios, { AxiosResponse } from 'axios';
import EndPoint from '../../comm/EndPoint.Config';
import { EditarProveedor } from '../../entities/EditarProveedor';
import { ErrorResponse } from '../../comm/contracts/ErrorResponse';
import { Estados } from '../../entities/Enums/estadosRespuesta';
import { Button, Form, Modal, Nav } from 'react-bootstrap';
import ModalNotificacionExitosaModificarProveedor from './ModalNotificacionExitosaModificarProveedor';
import ModalNotificacionFallidaModificarProveedor from './ModalNotificacionFallidaModificarProveedor';

interface ModalModificarProps {
  abrir: boolean;
  cerrar: () => void;
  numeroDocumento: string;
  tipoDocumento: string;
  nombre: string;
  nombreDos: string;
  apellido: string;
  apellidoDos: string;
  tipoPersona: string;
  direccion: string;
  telefono: string;
  ciudad: string;
  pais: string;
  tipoProveedor: string;
  documentoPLD: string;
  fechaPLD: string;
}

interface ModalModificarHandler extends ModalModificarProps {
  handler: Dispatch;
}

const ModalModificarProveedor: React.FC<ModalModificarHandler> = ({ handler, abrir, cerrar, numeroDocumento, tipoDocumento, nombre, nombreDos, apellido, apellidoDos, tipoPersona, telefono, direccion, ciudad, pais, tipoProveedor, documentoPLD, fechaPLD }) => {

  const { state } = useProveedor();

  const _FacturaFormContext = useContext(FacturaFormContext);
  const hrefStart = "#";
  const IDsObject = {
    factura: "factura" + _FacturaFormContext.counter,
    collapseFactura: 'facturaCollapse' + _FacturaFormContext.counter,
    collapseArticulo: 'ArticuloCollapse' + _FacturaFormContext.counter,
    btnExpandirPrincipal: 'expandir-principal' + _FacturaFormContext.counter,
  }

  const [isLoading, setIsLoading] = useState(false);

  const [datosProvModificado, setDatosProvModificado] = useState({
    nombre: state.nombre, apellido: state.apellido, nombreDos: state.nombreDos, apellidoDos: state.apellidoDos, tipoPersona: state.tipoPersona,
    tipoProveedor: state.tipoProveedor, pais: state.pais, telefono: state.telefono, direccion: state.direccion, ciudad: state.ciudad, numeroDocumento: state.numeroDocumento,
    timbrado: state.timbrado, fechaCarga: state.fechaCarga, fechaVencimiento: state.fechaVencimiento, estado: state.estado, tipoDocumento: state.tipoDocumento, criticidad: state.criticidad,
    declaracionJurada: state.declaracionJurada, fechaDeclaracionJurada: state.fechaDeclaracionJurada, codigoCliente: state.codigoCliente, numeroTimbrado: state.numeroTimbrado, mes: state.mes,
    anho: state.anho, mensajeGrabadoCorrectamente: '', documentoPLD: state.documentoPLD, fechaPLD: state.fechaPLD, cedula: state.cedula
  });

  const [abrirModalExitosoEditarProveedor, setAbrirModalExitosoEditarProveedor] = useState(false);
  const [abrirModalFallidoEditarProveedor, setAbrirModalFallidoEditarProveedor] = useState(false);

  const Params = {
    urlBase: getUrlBase(),
    ambiente: getAmbienteContexto(),
    request: axios.create(),
    endpoints: EndPoint.apiProveedorFactura
  }

  const postModificarProveedor = async () => {
    let _headers = await getApiProveedorFacturaHeaders('');
    let _sturlEndpoint = Params.endpoints.vUno.post.proveedor;

    let _body = {
      datos: {
        tipoDocumento: datosProvModificado.tipoDocumento,
        cedula: datosProvModificado.numeroDocumento,
        nombre: datosProvModificado.nombre,
        nombreDos: datosProvModificado.nombreDos,
        apellido: datosProvModificado.apellido,
        apellidoDos: datosProvModificado.apellidoDos,
        tipoPersona: datosProvModificado.tipoPersona,
        direccion: datosProvModificado.direccion,
        telefono: datosProvModificado.telefono,
        ciudad: datosProvModificado.ciudad,
        pais: datosProvModificado.pais,
        tipoProveedor: datosProvModificado.tipoProveedor,
        documentoPLD: datosProvModificado.documentoPLD,
        fechaPLD: datosProvModificado.fechaPLD,
      },
      accion: "M"
    }

    console.log("_body post mod prov:", _body);

    let config = {
      method: 'post',
      url: Params.urlBase + _sturlEndpoint,
      headers: _headers
    }

    const promise = Params.request.post<any, AxiosResponse<EditarProveedor, ErrorResponse>>(config.url, _body, config)
      .then(function (response) {
        if (response.status === Estados.estadoDeSolicitudProcesada) {
          console.log("Status Post Prov:", response.status);
          console.log("Response post edit prov:", response.data);
          datosProvModificado.mensajeGrabadoCorrectamente = response.data.mensaje;
        }

        if (response.status === Estados.estadoValidacionIncorrecta) {
          alert("Por favor, verificar los datos.");
        }

        if (response.status === Estados.estadoNoAutorizado) {
          alert("Favor, refrescar la página y volver a intentar.");
        }

        if (response.status === Estados.estadoErrorServidores) {
          alert("Ocurrió un error al modificar los datos.");
        }

        state.responseStatusModificarProveedor = response.status.toString();

        return response.data
      }).catch(function (error) {
        console.log("Error post edit prov:", error.response);
        return error.response;
      });

    if (promise) {
      setIsLoading(true);
      await promise;
    } else {
      setIsLoading(false);
    }

    if (Estados.estadoExitoso || Estados.estadoSinContenido || Estados.estadoErrorServidores || Estados.estadoTiempoDeEspera) {
      setIsLoading(false);
    }

    const result = await promise;
    return result;
  }

  const handleChangeModificarProv = (e: any) => {
    setDatosProvModificado({
      ...datosProvModificado,
      [e.target.name]: e.target.value
    })
  }

  const handleAbrirModalExitosoEditarProveedor = () => setAbrirModalExitosoEditarProveedor(true);

  const handleCerrarModalExitosoEditarProveedor = () => {
    setAbrirModalExitosoEditarProveedor(false);
    window.location.reload();
  }

  const handleAbrirModalFallidoEditarProveedor = () => setAbrirModalFallidoEditarProveedor(true);

  const handleCerrarModalFallidoEditarProveedor = () => {
    setAbrirModalFallidoEditarProveedor(false);
    window.location.reload();
  }

  const editarProveedor = async () => {
    await postModificarProveedor();
    handler('datos');

    if (datosProvModificado.mensajeGrabadoCorrectamente) {
      handleAbrirModalExitosoEditarProveedor();
      console.log("Los datos fueron modificados de manera exitosa.");
    } else {
      handleAbrirModalFallidoEditarProveedor();
      console.log("No se pudo modificar los datos.");
    }
  }

  return (
    <div>
      <Modal show={abrir} size="xl">
        <Modal.Header className="modal-header d-flex align-items-center">
          <Modal.Title className="modal-title fs-5 text-primary">Editar Proveedor</Modal.Title>
          <Button variant='danger' onClick={cerrar} title='Editar Proveedor'>
            &#x2715;
          </Button>
        </Modal.Header>
        <Modal.Body className="modal-body d-flex flex-column justify-content-center align-items-center">
          <Form>
            <div className='d-flex justify-content-center align-items-center row'>
              <Form.Group className='d-flex flex-column align-items-center justify-content-center col'>
                <Form.Label>Tipo Documento</Form.Label>
                <Form.Select name='tipoDocumento' className='custom-select mx-2' defaultValue={tipoDocumento || datosProvModificado.tipoDocumento || "null"} onChange={handleChangeModificarProv}>
                  <option value="">TIPO DOCUMENTO</option>
                  <option value="1">CEDULA</option>
                  <option value="4">RUC</option>
                </Form.Select>
              </Form.Group>
              <Form.Group className='d-flex flex-column align-items-center justify-content-center col'>
                <Form.Label>Número de documento</Form.Label>
                <Form.Control type="text" name='numeroDocumento' className='form-control mb-2 w-100' placeholder='Número documento' defaultValue={numeroDocumento || datosProvModificado.cedula} onChange={handleChangeModificarProv} />
              </Form.Group>
            </div>
            <div className='d-flex justify-content-center align-items-center'>
              <ul className='nav nav-tabs my-4 mx-1' role="tablist" id="myNav">
                <li className='nav-item' role="datosPersonales">
                  <a className='nav-link active expandir-principal' id={`datos-personales` + IDsObject.btnExpandirPrincipal} data-toggle="tab" data-target="#datos-personales-pane" href={hrefStart + IDsObject.collapseFactura} role="tab" aria-controls="datos-personales-tab-pane" aria-selected="true">Datos Peronales</a>
                </li>
                <li className='nav-item' role="timbrado-nav">
                  <a className='nav-link' id="timbrado-data" data-toggle="tab" data-target="#timbrado-tab-pane" href="timbrado-tab" role="tab" aria-controls="timbrado-tab-pane" aria-selected="false">Timbrado</a>
                </li>
              </ul>
            </div>
            <Nav justify variant="tabs" className='tab-content' id="myNavContent">
              <Nav.Item id="datos-personales-pane" className="tab-pane fade show active" role="tabpanel" aria-labelledby="datos-personales-tab" tabIndex={0}>
                <div className='d-flex justify-content-center align-items-center row'>
                  <Form.Group className='col d-flex flex-column align-items-center'>
                    <Form.Label className='align-baseline'>Nombre</Form.Label>
                    <Form.Control type="text" className='form-control mb-2 w-100' placeholder='Nombre' name="nombre" defaultValue={nombre || datosProvModificado.nombre} onChange={handleChangeModificarProv} />
                  </Form.Group>
                  <Form.Group className='col d-flex flex-column align-items-center'>
                    <Form.Label>Segundo Nombre</Form.Label>
                    <Form.Control type="text" className='form-control mb-2 w-100' placeholder='Segundo Nombre' name="nombreDos" defaultValue={nombreDos || datosProvModificado.nombreDos} onChange={handleChangeModificarProv} />
                  </Form.Group>
                </div>
                <div className='d-flex align-items-center justify-content-center row'>
                  <Form.Group className='d-flex flex-column align-items-center col'>
                    <Form.Label>Apellido</Form.Label>
                    <Form.Control type="text" className='form-control mb-2 w-100' placeholder='Apellido' name="apellido" defaultValue={apellido || datosProvModificado.apellido} onChange={handleChangeModificarProv} />
                  </Form.Group>
                  <Form.Group className='d-flex flex-column align-items-center col'>
                    <Form.Label>Segundo Apellido</Form.Label>
                    <Form.Control type="text" className='form-control w-100' placeholder='Segundo Apellido' name="apellidoDos" defaultValue={apellidoDos || datosProvModificado.apellidoDos} onChange={handleChangeModificarProv} />
                  </Form.Group>
                </div>
                <div className='d-flex align-items-center justify-content-center p-1 row'>
                  <Form.Group className='d-flex flex-column align-items-center col'>
                    <Form.Label>Tipo Persona</Form.Label>
                    <Form.Select name="tipoPersona" className='custom-select' defaultValue={tipoPersona || datosProvModificado.tipoPersona} onChange={handleChangeModificarProv}>
                      <option value="">TIPO PERSONA</option>
                      <option value="J">JURIDICA</option>
                      <option value="F">FISICA</option>
                    </Form.Select>
                  </Form.Group>
                  <Form.Group className='d-flex flex-column align-items-center col'>
                    <Form.Label>Dirección</Form.Label>
                    <Form.Control type="text" className='form-control mb-2 w-100' placeholder='Dirección' name="direccion" defaultValue={direccion || datosProvModificado.direccion} onChange={handleChangeModificarProv} />
                  </Form.Group>
                </div>
                <div className='d-flex align-items-center justify-content-center row'>
                  <Form.Group className='d-flex flex-column align-items-center col'>
                    <Form.Label>Teléfono</Form.Label>
                    <Form.Control type="text" className='form-control mb-2 w-100' placeholder='Teléfono' name="telefono" defaultValue={telefono || datosProvModificado.telefono} onChange={handleChangeModificarProv} />
                  </Form.Group>
                  <Form.Group className='d-flex flex-column align-items-center col'>
                    <Form.Label>Ciudad</Form.Label>
                    <Form.Control type="text" className='form-control mb-2 w-100' placeholder='Ciudad' name="ciudad" defaultValue={ciudad || datosProvModificado.telefono} onChange={handleChangeModificarProv} />
                  </Form.Group>
                </div>
                <div className='d-flex align-items-center justify-content-center row'>
                  <Form.Group className='d-flex flex-column align-items-center col'>
                    <Form.Label>País</Form.Label>
                    <Form.Control type="text" className='form-control mb-2 w-100' placeholder='País' name="pais" defaultValue={pais || datosProvModificado.pais} onChange={handleChangeModificarProv} />
                  </Form.Group>
                  <Form.Group className='d-flex flex-column align-items-center col'>
                    <Form.Label>Tipo Proveedor</Form.Label>
                    <Form.Select name="tipoProveedor" className='custom-select my-2 w-100' defaultValue={tipoProveedor || datosProvModificado.tipoProveedor} onChange={handleChangeModificarProv}>
                      <option value="">TIPO PROVEEDOR</option>
                      <option value="PON">Proveedor ocasional - No Cliente</option>
                      <option value="PHCC">Proveedor habitual con contrato - Cliente</option>
                      <option value="PHSC">Proveedor habitual sin contrato - Cliente</option>
                      <option value="POC">Proveedor ocasional - Cliente</option>
                      <option value="PHCN">Proveedor habitual con contrato - No Cliente</option>
                      <option value="PHSN">Proveedor habitual sin contrato - No Cliente</option>
                    </Form.Select>
                  </Form.Group>
                </div>
                <div className="d-flex align-items-center justify-content-center row">
                  <Form.Group className="d-flex flex-column align-items-center col my-2">
                    <Form.Label>Declaración Jurada</Form.Label>
                    <Form.Select className='custom-select w-100' name="declaracionJurada" defaultValue={documentoPLD || datosProvModificado.documentoPLD} onChange={handleChangeModificarProv}>
                      <option value="">DECLARACION JURADA</option>
                      <option value="S">SI</option>
                      <option value="N">NO</option>
                    </Form.Select>
                  </Form.Group>
                </div>
                <div className="d-flex flex-column my-2 w-100">
                  <Form.Group className='d-flex flex-column align-items-center'>
                    <Form.Label>Fecha Declaración Jurada</Form.Label>
                    <Form.Control type="date" className='form-control ml-2' name="fechaDeclaracionJurada" defaultValue={fechaPLD != null ? state.fechaPLD.slice(0, -9) : state.fechaPLD || datosProvModificado.fechaPLD} onChange={handleChangeModificarProv} />
                  </Form.Group>
                </div>
                {isLoading ?
                  <div className='text-center mx-4 my-4'><Loader /></div>
                  : ""}
                <div className="modal-footer d-flex justify-content-center">
                  <Button variant="secondary" onClick={cerrar}>Cerrar</Button>
                  <Button variant="primary" onClick={editarProveedor}>
                    Guardar Cambios
                  </Button>
                </div>
              </Nav.Item>
              <Nav.Item className='tab-pane fade' id="timbrado-tab-pane">
                <DatosProvider><CampoTimbrado cerrar={cerrar} /></DatosProvider>
              </Nav.Item>
            </Nav>
          </Form>
        </Modal.Body>
      </Modal>
      {<ModalNotificacionExitosaModificarProveedor abrir={abrirModalExitosoEditarProveedor} cerrar={handleCerrarModalExitosoEditarProveedor} />}
      {<ModalNotificacionFallidaModificarProveedor abrir={abrirModalFallidoEditarProveedor} cerrar={handleCerrarModalFallidoEditarProveedor} />}
    </div>
  )
}

export default ModalModificarProveedor;