/// <reference types="webpack/module" />

import React, { useState, useEffect } from "react";
import EndPoint from "../../comm/EndPoint.Config";
import { getUrlBase, getAmbienteContexto } from "../../utilities/contextInfo";
import axios from "axios";
import { Proveedor } from "../../entities/Proveedor";
import { getApiProveedorFacturaHeaders } from "../../comm/contracts/ApiBpmPoliticasProcesosRequestHeaders";
import { DatosProvider, Dispatch, useProveedor } from "../contextProvider/datosProveedorContext";
import BtnModificarProveedor from "../abmProveedores/BtnModificarProveedor";
import { ClienteProveedor } from "../../entities/ProveedorGet";
import { PerfilStatus } from "../../entities/PerfilStatus";
import { DocumentosVencidos } from "../../entities/DocumentosVencidos";
import { Estados } from "../../entities/Enums/estadosRespuesta";
import { LoaderSmall } from "../loader/Loader";
import ModalValidacionCamposBuscadorProveedor from "../../components/modales/ModalValidacionCamposBuscadorProveedor";
import ModalIntermedioAltaProveedor from "../modales/ModalIntermedioAltaProveedor";
import ModalNoConectaALaAPI from "../modales/ModalNoConectaALaAPI";

'use strict'

export const ProveedorBuscador = ({ handler }: { handler: Dispatch }, e: any) => {

  const { state, dispatch } = useProveedor();

  const [dato, setDato] = useState({ nroDoc: '', tipoDoc: '', responseGetStatus: '' });

  const [isLoading, setIsLoading] = useState(false);

  const [mostrarModal, setMostrarModal] = useState(false);

  const [mostrarModalIntermedio, setMostrarModalIntermedio] = useState(false);

  const [mostrarModalNoConectaALaAPI, setMostrarModalNoConectaALaAPI] = useState(false);

  const Params = {
    urlBase: getUrlBase(),
    ambiente: getAmbienteContexto(),
    request: axios.create({}),
    endpoints: EndPoint.apiProveedorFactura
  }

  const obtenerDatosProveedor = async () => {
    let _cacheControl = 'max-age=60*60*10, private'
    let _headers = await getApiProveedorFacturaHeaders(_cacheControl)
    let _sturlEndpoint = Params.endpoints.vUno.get.proveedor
    let urlEndpoint = _sturlEndpoint.replace("${codigoCliente}", dato.nroDoc.trim())
    let tipoDocumento = `?tipoDocumento=`;

    let config = {
      method: 'get',
      url: Params.urlBase + urlEndpoint + tipoDocumento + `${dato.tipoDoc}`,
      headers: _headers
    };

    let Promise = Params.request<Proveedor>(config)
      .then(function (response) {
        console.log("response:", response);
        dato.responseGetStatus = response.status.toString();

        if (response.status === Estados.estadoExitoso) {
          state.nombre = response.data.persona.nombre;
          state.apellido = response.data.persona.apellido;
          state.nombreDos = response.data.persona.nombreDos;
          state.apellidoDos = response.data.persona.apellidoDos;
          state.tipoPersona = response.data.persona.tipoPersona;
          state.tipoProveedor = response.data.tipoProveedor;
          state.pais = response.data.ubicacion.pais;
          state.telefono = response.data.persona.telefono;
          state.direccion = response.data.ubicacion.direccion;
          state.ciudad = response.data.ubicacion.ciudad;
          state.numeroDocumento = response.data.persona.numeroDocumento;
          let arrayTimbrado: any = response.data.timbrado;
          state.timbrado = arrayTimbrado;
          console.log("state.timbrado:", state.timbrado);
          console.log("state.timbrado.numero:", state.timbrado.map((item: any) => item.numero));
          state.fechaCarga = response.data.timbrado.fechaCarga;
          state.fechaVencimiento = response.data.timbrado.fechaVencimiento;

          state.estado = response.data.estado;
          state.codigoCliente = response.data.persona.codigoCliente;
          console.log("state.codigoCliente obtenerDatosProveedor:", state.codigoCliente);
          state.mes = response.data.timbrado.fechaVencimiento;
          state.anho = response.data.timbrado.fechaVencimiento;

          state.tipoDocumento = response.data.persona.tipoDocumento;

          state.estadoTimbrado = response.data.timbrado.estado;

          state.documentoPLD = response.data.documentoPLD;

          state.fechaPLD = response.data.fechaPLD;
        }

        if (response.status === Estados.estadoValidacionIncorrecta) {
          alert("Por favor, verificar los datos.");
          return false;
        }

        if (response.status === Estados.estadoNoAutorizado) {
          alert("Favor, refrescar la página y volver a intentar.");
          return false;
        }

        if (response.status === Estados.estadoErrorServidores) {
          alert("Ocurrió un error.");
          return false;
        }

        state.responseObtenerStatusProveedor = response.status.toString();

        console.log("Response data:", response.data);

        return response.data, response.status, state.timbrado;
      })
      .catch(function (error) {
        console.log("Error get obtenerDatosProveedor:", error);
      });

    if (Promise) {
      setIsLoading(true);
      await Promise;
    } else {
      setIsLoading(false);
    }

    if (Estados.estadoExitoso || Estados.estadoSinContenido || Estados.estadoErrorServidores || Estados.estadoTiempoDeEspera) {
      setIsLoading(false);
    }

    let result = await Promise;

    return result
  }

  state.nroD = dato.nroDoc.trim();
  state.tipD = dato.tipoDoc;

  const getClienteProveedor = async () => {
    let _cacheControl = 'max-age=60*60*10, private';
    let _headers = await getApiProveedorFacturaHeaders(_cacheControl);
    let _sturlEndpoint = Params.endpoints.vUno.get.clienteProveedor;
    let urlEndpoint = _sturlEndpoint.replace("${numeroDocumento}", dato.nroDoc);
    let tipoDocumento = `?tipoDocumento=`;

    let config = {
      method: "get",
      url: Params.urlBase + urlEndpoint + tipoDocumento + `${dato.tipoDoc}`,
      headers: _headers
    };

    let promise = Params.request<ClienteProveedor>(config)
      .then(function (response) {
        if (response.status === Estados.estadoExitoso) {
          console.log("Response.data getClienteProveedor:", response.data);
          console.log("codigoCliente:", response.data.codigoCliente);
          state.codigoClienteGCP = response.data.codigoCliente;
          localStorage.setItem("codigoClienteProveedor", state.codigoCliente);
        }

        if (response.status === Estados.estadoValidacionIncorrecta) {
          alert("Por favor, verificar los datos.");
        }

        if (response.status === Estados.estadoNoAutorizado) {
          alert("Favor, refrescar la página y volver a intentar.");
        }

        if (response.status === Estados.estadoErrorServidores) {
          alert("Ocurrió un error.");
        }

        state.responseObtenerStatusCliente = response.status.toString();

        return response.data;
      }).catch(function (error) {
        console.log("Error get Cliente Proveedor, Proveedor Buscador:", error.response);
        return error.response;
      });

    if (promise) {
      setIsLoading(true);
      await promise;
    } else {
      setIsLoading(false);
    }

    if (Estados.estadoExitoso || Estados.estadoSinContenido || Estados.estadoErrorServidores || Estados.estadoTiempoDeEspera) {
      setIsLoading(false);
    }

    let result = await promise;
    return result;
  }

  const getEstadoPerfil = async () => {
    let _cacheControl = 'max-age=60*60*10, private';
    let _headers = await getApiProveedorFacturaHeaders(_cacheControl);
    let _sturlEndpoint = Params.endpoints.vUno.get.perfilesVencidos;
    let urlEndpoint = _sturlEndpoint.replace("${codigoCliente}", state.codigoCliente);

    let config = {
      method: 'get',
      url: Params.urlBase + urlEndpoint,
      headers: _headers
    }

    console.log("state.codigoCliente getEstadoPerfil:", state.codigoCliente);
    console.log("config.url getEstadoPerfil:", config.url);

    let promise = Params.request<PerfilStatus>(config)
      .then(function (response) {
        console.log("Perfil Response.data:", response.data);
        state.respuestaEstadoPerfilMensaje = response.status.toString();
        console.log("STATE GET ESTADO PERFIL:", state.respuestaEstadoPerfilMensaje);
        if (response.status === Estados.estadoExitoso) {
          localStorage.setItem("estadoPerfil", JSON.stringify(response.data.mensaje));
          console.log("response.data.mensaje:", response.data.mensaje);
          state.estadoPerfilMensaje = response.data.mensaje;
          console.log("state.estadoPerfilMensaje GET 200:", state.estadoPerfilMensaje);
          console.log("Perfil getEstadoPerfil status 200:", response.status);
        }
        if (response.status === Estados.estadoSinContenido) {
          console.log("Perfil getEstadoPerfil status 204:", response.status);
          state.estadoPerfilMensaje = "";
          console.log("state.estadoPerfilMensaje GET 204:", state.estadoPerfilMensaje);
        }

        return response.data;
      }).catch(function (error) {
        console.log("Error getEstadoPerfil perfil vencido:", error.response);
        return error.response;
      });

    if (promise) {
      setIsLoading(true);
      await promise;
    } else {
      setIsLoading(false);
    }

    if (Estados.estadoExitoso || Estados.estadoSinContenido || Estados.estadoErrorServidores || Estados.estadoTiempoDeEspera) {
      setIsLoading(false);
    }

    let result = await promise;
    return result;
  }

  const getDocumentosVencidos = async () => {
    let _cacheControl = 'max-age=60*60*10, private';
    let _headers = await getApiProveedorFacturaHeaders(_cacheControl);
    let _sturlEndpoint = Params.endpoints.vUno.get.documentosVencidos;
    let urlEndpoint = _sturlEndpoint.replace("${codigoCliente}", state.codigoCliente);

    let config = {
      method: 'get',
      url: Params.urlBase + urlEndpoint,
      headers: _headers
    }

    let promise = Params.request<DocumentosVencidos>(config)
      .then(function (response) {
        if (response.status === Estados.estadoExitoso) {
          console.log("response.data DocumentosVencidos:", response.data);
          localStorage.setItem("documentoVencido", JSON.stringify(response.data.map((item) => item.tipoDocumento).join(" ").toString()));
          state.DVTipoDocumento = response.data.map((item) => item.tipoDocumento).join(" ").toString();
          state.DVEstadoDocumento = response.data.map((item) => item.estadoDocumento).toString();
        }

        return response.data;
      }).catch(function (error) {
        console.log("Error DocumentosVencidos:", error.response);
      });

    if (promise) {
      setIsLoading(true);
      await promise;
    } else {
      setIsLoading(false);
    }

    if (Estados.estadoExitoso || Estados.estadoSinContenido || Estados.estadoErrorServidores || Estados.estadoTiempoDeEspera) {
      setIsLoading(false);
    }

    let result = await promise;
    return result;
  }

  useEffect(() => {
    const render = async () => {
      await handleBtnForm(e);
    }
    render();
  }, []);

  const mostrarModalIntermedioAltaProveedor = () => setMostrarModalIntermedio(true);
  const cerrarModalIntermedioAltaProveedor = () => setMostrarModalIntermedio(false);

  const handleBtnForm = async (e: any) => {
    e.preventDefault();
    await obtenerDatosProveedor();
    if (dato.responseGetStatus === Estados.estadoExitoso.toString()) {
      await getClienteProveedor();
      await getEstadoPerfil();
      await getDocumentosVencidos();

      console.log("STATE RESPUESTA ESTADO PERFIL MENSAJE:", state.respuestaEstadoPerfilMensaje);
      handler("datos");

      if (state.estadoPerfilMensaje === "NO") {
        handler('datos');
      }

      if (state.estadoPerfilMensaje === "SI") {
        handler('datos');
      }

      console.log("state.DVEstadoDocumento:", state.DVEstadoDocumento);
      console.log("state.DVTipoDocumento:", state.DVTipoDocumento);

      if (state.DVTipoDocumento) {
        handler('datos');
      }

    } else if (dato.responseGetStatus === Estados.estadoSinContenido.toString()) {
      mostrarModalIntermedioAltaProveedor();
    }
    handler('datos');
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setDato({
      ...dato,
      [e.target.name]: e.target.value
    });
  }

  const handleMostrarModalValidacionCampos = () => setMostrarModal(true);
  const handleOcultarModalValidacionCampos = () => setMostrarModal(false);

  const handleMostrarModalNoConectaALaAPI = () => setMostrarModalNoConectaALaAPI(true);
  const handleCerrarModalNoConectaALaAPI = () => setMostrarModalNoConectaALaAPI(false);

  return (
    <>
      <div className="form-inline w-100 d-flex align-items-center justify-content-center">
        <div className="d-flex align-items-center justify-content-center flex-wrap">
          <div className="d-flex flex-column align-items-center">
            <input className="form-control mr-sm-3 bg-light" type="search" placeholder="Número de documento"
              aria-label="Search" id="buscadorProveedor" defaultValue={dato.nroDoc.trim()} name="nroDoc" onChange={handleChange} required />
          </div>
          <div className="d-flex flex-column align-items-center">
            <select className="custom-select my-1" id="tipoDocumento" defaultValue={dato.tipoDoc} name="tipoDoc" onChange={handleChange} required >
              <option value="">Tipo Documento</option>
              <option value="1">CEDULA</option>
              <option value="4">RUC</option>
            </select>
          </div>
          <div className="mx-4 mt-2 mb-2">
            {
              !dato.nroDoc || !dato.tipoDoc ? <button className="btn btn-outline-success my-2 my-sm-0 d-flex" id="btnBuscar" data-toggle="modal" data-target="" onClick={dato.responseGetStatus === null ? handleMostrarModalNoConectaALaAPI : handleMostrarModalValidacionCampos}>Buscar Proveedor {isLoading ? <div className="text-center ml-2"><LoaderSmall /></div> : ""}</button> :
                <button className="btn btn-outline-success my-2 my-sm-0 d-flex" id="btnBuscar" onClick={handleBtnForm}>Buscar Proveedor {isLoading ? <div className="text-center ml-2"><LoaderSmall /></div> : ""}</button>
            }
          </div>
          <br />
          {dato.responseGetStatus === Estados.estadoExitoso.toString() ?
            <DatosProvider><BtnModificarProveedor /></DatosProvider> :
            ""}
        </div>
      </div>
      {<ModalValidacionCamposBuscadorProveedor show={mostrarModal} onClose={handleOcultarModalValidacionCampos} />}
      {<ModalIntermedioAltaProveedor mostrar={mostrarModalIntermedio} cerrar={cerrarModalIntermedioAltaProveedor} handler={dispatch} />}
      {<ModalNoConectaALaAPI mostrarModal={mostrarModalNoConectaALaAPI} cerrarModal={handleCerrarModalNoConectaALaAPI} />}
    </>
  )
}