import React from 'react';
import { <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap';

interface ModalProps {
  abrir: boolean;
  ocultar: () => void;
}

const ModalNoSeEncuentraElCliente: React.FC<ModalProps> = ({ abrir, ocultar }) => {

  return (
    <div>
      <Modal show={abrir} onHide={ocultar}>
        <Modal.Header>
          <Modal.Title className='text-secondary'></Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p className='text-center font-weight-bold text-danger'>No se encuentra el cliente.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={ocultar}>Cerrar</Button>   
        </Modal.Footer>
      </Modal>
    </div>
  )
}

export default ModalNoSeEncuentraElCliente