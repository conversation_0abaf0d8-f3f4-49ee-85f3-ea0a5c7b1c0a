/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "./Maqueta/js/finansys.js":
/*!********************************!*\
  !*** ./Maqueta/js/finansys.js ***!
  \********************************/
/***/ (() => {

eval("var username = '';\nvar previewDiv = document.getElementById('prevcontent');\nvar defaultDiv = document.getElementById('defaultcont');\n// Close preview\nvar closePreview = function closePreview() {\n  document.getElementById('prevcontent').classList.remove('preview--show');\n  $('.tbl-item').removeClass('tbl-item--selected');\n\n  // Show default content\n  showDefaultView();\n};\nvar getDetailsData = function getDetailsData() {\n  // Activar la linea clickeada agregando la clase tbl-item--selected\n\n  // Traer los datos completos de la operacion\n\n  // Mostrar el preview\n  showDetailsView(data);\n};\n$('.tbl-item').on('click', function () {\n  $(this).toggleClass('tbl-item--selected');\n  var div = this;\n  username = div.getElementsByClassName('tbl-item__name')[0].innerText;\n  // Mostrar el preview\n  showDetailsView(username);\n});\nvar defaultContent = function defaultContent() {\n  return \"\\n    <div class=\\\"text-center\\\">\\n      <i class=\\\"fa fa-file fa-2x mb-3\\\"></i>\\n      <h5 class=\\\"mb-0\\\">Seleccione una Operaci\\xF3n</h5>\\n      <p>Los detalles se visualizan aqui</p>\\n    </div>\\n    \";\n};\nvar defaultLoader = function defaultLoader() {\n  return \"\\n    <div class=\\\"text-center\\\">\\n      <i class=\\\"fa fa-sync-alt fa-spin fa-2x mb-3\\\"></i>\\n      <p>Obteniendo datos...</p>\\n    </div>\\n    \";\n};\nvar multiSelect = function multiSelect(number) {\n  number = 2;\n  return \"\\n    <div class=\\\"text-center\\\">\\n      <i class=\\\"fa fa-copy fa-2x mb-3\\\"></i>\\n      <h5 class=\\\"mb-0\\\">\".concat(number, \" Operaciones Seleccionadas</h5>\\n      <p><a href=\\\"#\\\">Seleccionar todas</a> las operaciones</p>\\n      <div class=\\\"text-left px-5\\\">\\n        <a href=\\\"#\\\" class=\\\"d-block mb-2\\\"><i class=\\\"fa fa-check\\\"></i> Aprobar</a>\\n        <a href=\\\"#\\\" class=\\\"d-block mb-2\\\" data-toggle=\\\"modal\\\" data-target=\\\"#rechazar\\\"><i class=\\\"fa fa-times\\\"></i> Rechazar</a>\\n      </div>\\n    </div>\\n  \");\n};\n\n// Function to display operation detail\nvar operacion = function operacion(username) {\n  // mapear/recibir array\n  return \"\\n    <div class=\\\"px-4 py-3 d-flex justify-content-between bg-white sticky-top\\\">\\n        <div class=\\\"d-flex align-items-center\\\">\\n            <span onclick=\\\"closePreview()\\\" class=\\\"pw-close\\\">\\n              <span class=\\\"pw-close__bar-1\\\"></span>\\n              <span class=\\\"pw-close__bar-2\\\"></span>\\n            </span>\\n            <p class=\\\"ml-3 mb-0\\\">Solicitud No. 5750131219</p>\\n        </div>\\n        <!-- Preview header actions -->\\n        <div class=\\\"btn-group\\\">\\n            <a href=\\\"#\\\" class=\\\"btn btn-outline-fun-blue\\\" data-toggle=\\\"modal\\\" data-target=\\\"#rechazar\\\">Rechazar</a>\\n            <a href=\\\"#\\\" class=\\\"btn btn-fun-blue\\\">Aprobar</a>\\n        </div>\\n    </div>\\n    <div class=\\\"prw-cont__detail px-4\\\">\\n      <div class=\\\"row mb-4\\\">\\n        <div class=\\\"col-12 col-md-3\\\">\\n          <figure class=\\\"avatar avatar--lg bg-key-lime\\\">\\n            <span>SS</span>\\n          </figure>\\n          <div class=\\\"mt-2\\\">\\n            <h5 class=\\\"mb-0\\\">\".concat(username, \"</h5>\\n            <p class=\\\"mb-0\\\">Clasificacion 1</p>\\n            <p>Cod.: 410241</p>\\n          </div>\\n          <div class=\\\"form-group\\\">\\n            <button onclick=\\\"showDocumentsView()\\\" class=\\\"btn btn-secondary\\\">Ver documentos</button>\\n          </div>\\n        </div>\\n        <div class=\\\"col-12 col-md-9\\\">\\n          <div class=\\\"row\\\">\\n            <div class=\\\"col-6\\\">\\n              <div class=\\\"client-data\\\">\\n                <b>Documento</b>\\n                <p>CI - 5.864.546</p>\\n              </div>\\n              <div class=\\\"client-data\\\">\\n                <b>Email</b>\\n                <p><EMAIL> <span class=\\\"badge badge-warning\\\">Nuevo</span></p>\\n              </div>\\n              <div class=\\\"client-data\\\">\\n                <b>Sucursal</b>\\n                <p>Coronel Oviedo</p>\\n              </div>\\n            </div>\\n            <div class=\\\"col-6\\\">\\n              <div class=\\\"client-data\\\">\\n                <b>Celular</b>\\n                <p>0984893626 <span class=\\\"badge badge-warning\\\">Nuevo</span></p>\\n              </div>\\n              <div class=\\\"client-data\\\">\\n                <b>Estado Civil</b>\\n                <p>Soltero</p>\\n              </div>\\n              <div class=\\\"client-data\\\">\\n                <b>Oficial</b>\\n                <p>Alejandro Centurion</p>\\n              </div>\\n            </div>\\n          </div>\\n        </div>\\n      </div>\\n\\n      <ul class=\\\"nav nav-tabs mb-4\\\" id=\\\"myTab\\\" role=\\\"tablist\\\">\\n        <li class=\\\"nav-item\\\">\\n          <a class=\\\"nav-link active\\\" id=\\\"home-tab\\\" data-toggle=\\\"tab\\\" href=\\\"#home\\\" role=\\\"tab\\\" aria-controls=\\\"home\\\" aria-selected=\\\"true\\\">Detalles</a>\\n        </li>\\n        <li class=\\\"nav-item\\\">\\n          <a class=\\\"nav-link\\\" id=\\\"profile-tab\\\" data-toggle=\\\"tab\\\" href=\\\"#profile\\\" role=\\\"tab\\\" aria-controls=\\\"profile\\\" aria-selected=\\\"false\\\">Seguimiento</a>\\n        </li>\\n      </ul>\\n      <div class=\\\"tab-content mb-4\\\" id=\\\"myTabContent\\\">\\n        <div class=\\\"tab-pane fade show active\\\" id=\\\"home\\\" role=\\\"tabpanel\\\" aria-labelledby=\\\"home-tab\\\">\\n            \\n          <!-- Contenido de la operacion -->\\n\\n            <div class=\\\"col-12\\\">\\n              <div class=\\\"row\\\">\\n                <div class=\\\"col-6\\\">\\n                  <div class=\\\"client-data\\\">\\n                    <b>Monto</b>\\n                    <p>5.864.546</p>\\n\\n                    <b>Plazo</b>\\n                    <p>60 meses</p>\\n\\n                    <b>Tasa</b>\\n                    <p>26%</p>\\n                  </div>\\n                </div>\\n                <div class=\\\"col-6\\\">\\n                  <div class=\\\"client-data\\\">\\n                    <b>Cuota</b>\\n                    <p>540.178</p>\\n          \\n                    <b>Destino</b>\\n                    <p>Vehiculo</p>\\n                    \\n                    <b>Gastos</b>\\n                    <p>550.000</p>\\n                  </div>\\n                </div>\\n              </div>\\n            </div>\\n        </div>\\n\\n        <div class=\\\"tab-pane fade\\\" id=\\\"profile\\\" role=\\\"tabpanel\\\" aria-labelledby=\\\"profile-tab\\\">\\n        \\n          <!-- Seguimiento -->\\n\\n          <div class=\\\"row\\\">\\n            <div class=\\\"col-12 col-md-4 text-center\\\">\\n              <div class=\\\"card py-4\\\">\\n                <h3 class=\\\"mb-0\\\">3 de 8</h3>\\n                <p class=\\\"mb-0\\\">Aprobado</p>\\n              </div>\\n            </div> \\n            <div class=\\\"col-12 col-md-8\\\">\\n              <div class=\\\"steps-vertical\\\">\\n                <div class=\\\"step active\\\">\\n                  <div class=\\\"step-info\\\">\\n                    <div class=\\\"step-label\\\">An\\xE1lisis de Consumo</div>\\n                  </div>\\n                  <div class=\\\"step-content\\\">\\n                    <span class=\\\"text-muted\\\">2020-06-22 11:39</span> \\n                    <p>Jose Ignacio Barrios Rodriguez</p>\\n                  </div>\\n                </div>\\n                <div class=\\\"step warning-step active\\\">\\n                  <div class=\\\"step-info\\\">\\n                    <div class=\\\"step-label\\\">Aprobaci\\xF3n de seguimiento</div>\\n                  </div>\\n                  <div class=\\\"step-content\\\">\\n                    <span class=\\\"text-muted\\\">2020-06-22 11:39</span> \\n                    <p>Jose Ignacio Barrios Rodriguez</p>\\n                  </div>\\n                </div>\\n                <div class=\\\"step\\\">\\n                  <div class=\\\"step-info\\\">\\n                    <div class=\\\"step-label\\\">Inicio de seguimiento</div>\\n                  </div>\\n                  <div class=\\\"step-content\\\">\\n                    <span class=\\\"text-muted\\\">2020-06-22 11:39</span> \\n                    <p>Jose Ignacio Barrios Rodriguez</p> \\n                  </div>\\n                </div>\\n              </div>\\n            </div>\\n                     \\n          </div>\\n        </div>\\n\\n      </div>\\n      <div class=\\\"comments py-4\\\">\\n        <h6 class=\\\"mb-2\\\">Comentarios</h6>\\n        <div class=\\\"comment\\\">\\n          <div class=\\\"comment__photo\\\">\\n            <figure class=\\\"avatar avatar--md avatar--text bg-fun-blue\\\">\\n                <span class=\\\"text-white\\\">SS</span>\\n            </figure>\\n          </div>\\n          <div class=\\\"comment__content\\\">\\n            <div class=\\\"d-flex justify-content-between\\\">\\n              <h5>Gonzalo Ruiz</h5>\\n              <p class=\\\"text-muted\\\">JUE. FEB. 27, 15:40Hs.</p>\\n            </div>\\n            <p class=\\\"charge\\\">Oficial de cuentas</p>\\n            <p>APROBADO POR RIESGOS, NO NOS IMPORTA</p>\\n          </div>\\n        </div>\\n\\n        <div class=\\\"comment comment--owner\\\">\\n          <div class=\\\"comment__photo\\\">\\n            <figure class=\\\"avatar avatar--md avatar--text bg-fun-blue\\\">\\n                <span class=\\\"text-white\\\">SS</span>\\n            </figure>\\n          </div>\\n          <div class=\\\"comment__content\\\">\\n            <div class=\\\"d-flex justify-content-between\\\">\\n              <h5>Sebastian Solis</h5>\\n              <p class=\\\"text-muted\\\">JUE. FEB. 27, 15:40Hs.</p>\\n            </div>\\n            <p class=\\\"charge\\\">Oficial de cuentas</p>\\n            <p>Vamos a darle al cliente lo que quiere</p>\\n          </div>\\n        </div>\\n      \\n        <div class=\\\"comment-box\\\">\\n          <label for=\\\"comentarios\\\">Comentar</label>\\n          <textarea id=\\\"comentairos\\\" class=\\\"form-control\\\" rows=\\\"3\\\"></textarea>\\n          <button class=\\\"btn btn-outline-fun-blue mt-2\\\">Guardar</button>\\n        </div>\\n      </div>\\n    \");\n};\nvar documentos = function documentos() {\n  return \"\\n      <div class=\\\"px-4 py-3 d-flex justify-content-between bg-white sticky-top\\\">\\n        <div class=\\\"d-flex align-items-center\\\">\\n            <span onclick=\\\"closePreview()\\\" class=\\\"pw-close\\\">\\n              <span class=\\\"pw-close__bar-1\\\"></span>\\n              <span class=\\\"pw-close__bar-2\\\"></span>\\n            </span>\\n            <p class=\\\"ml-3 mb-0\\\">Solicitud No. 5750131219</p>\\n        </div>\\n        <!-- Preview header actions -->\\n        <div class=\\\"btn-group\\\">\\n            <a href=\\\"#\\\" class=\\\"btn btn-outline-fun-blue\\\">Rechazar</a>\\n            <a href=\\\"#\\\" class=\\\"btn btn-fun-blue\\\">Aprobar</a>\\n        </div>\\n    </div>\\n      <div class=\\\"prw-cont__detail px-4\\\">\\n        <div class=\\\"pt-5 pb-3\\\">\\n            <h4 class=\\\"text-center\\\">Documentos</h4>\\n            <button onclick=\\\"showDetailsView()\\\" class=\\\"btn btn-link\\\"><i class=\\\"fa fa-chevron-left\\\"></i> Operaci\\xF3n</button>\\n        </div>\\n    \\n        <div class=\\\"py-2 row\\\">\\n            <div class=\\\"col-12 col-md-4\\\">\\n                <input class=\\\"form-control\\\" type=\\\"text\\\" placeholder=\\\"Buscar documentos...\\\">\\n            </div>\\n            <div class=\\\"col-12 col-md-4 offset-md-4 text-right\\\">\\n                <a href=\\\"#\\\" class=\\\"btn btn-secondary\\\">Filtros</a>\\n            </div>\\n        </div>\\n    \\n        <div class=\\\"card-documentos border-fun-blue\\\">\\n            <div class=\\\"file-info d-flex align-items-center card-documentos__info\\\">\\n              <div class=\\\"mr-3\\\">\\n                <input type=\\\"checkbox\\\">\\n              </div>\\n              <div class=\\\"text\\\">\\n                  <p class=\\\"card-documentos__title\\\">Comprobante de Ingresos</p>\\n                  <p class=\\\"mb-0\\\">Tu archivo debe ser PDF, JPG o PNG</p>\\n              </div>\\n            </div>\\n            <div class=\\\"text-left card-documentos__text\\\">\\n                <p class=\\\"text-muted mb-0\\\">Carpeta Digital</p>\\n                <p class=\\\"text-muted mb-0\\\">Vence: 10/20/22</p>\\n                <span class=\\\"badge badge-success\\\">Certificado</span>\\n            </div>\\n            <div class=\\\"file-action card-documentos__action\\\">\\n                <a href=\\\"#\\\" class=\\\"btn btn-outline-secondary\\\">Adjuntar</a>\\n            </div>\\n        </div>\\n\\n        <div class=\\\"card-documentos border-fun-blue\\\">\\n            <div class=\\\"file-info d-flex align-items-center card-documentos__info\\\">\\n              <div class=\\\"mr-3\\\">\\n                <input type=\\\"checkbox\\\">\\n              </div>\\n              <div class=\\\"text\\\">\\n                  <p class=\\\"card-documentos__title\\\">Comprobante de Ingresos</p>\\n                  <p class=\\\"mb-0\\\">Tu archivo debe ser PDF, JPG o PNG</p>\\n              </div>\\n            </div>\\n            <div class=\\\"text-left card-documentos__text\\\">\\n                <p class=\\\"text-muted mb-0\\\">Carpeta Digital</p>\\n                <p class=\\\"text-muted mb-0\\\">Vence: 10/20/22</p>\\n                <span class=\\\"badge badge-success\\\">Certificado</span>\\n            </div>\\n            <div class=\\\"file-action card-documentos__action\\\">\\n                <a href=\\\"#\\\" class=\\\"btn btn-outline-secondary\\\">Adjuntar</a>\\n            </div>\\n        </div>\\n\\n        <div class=\\\"card-documentos border-fun-blue\\\">\\n            <div class=\\\"file-info d-flex align-items-center card-documentos__info\\\">\\n              <div class=\\\"mr-3\\\">\\n                <input type=\\\"checkbox\\\">\\n              </div>\\n              <div class=\\\"text\\\">\\n                  <p class=\\\"card-documentos__title\\\">Comprobante de Ingresos</p>\\n                  <p class=\\\"mb-0\\\">Tu archivo debe ser PDF, JPG o PNG</p>\\n              </div>\\n            </div>\\n            <div class=\\\"text-left card-documentos__text\\\">\\n                <p class=\\\"text-muted mb-0\\\">Carpeta Digital</p>\\n                <p class=\\\"text-muted mb-0\\\">Vence: 10/20/22</p>\\n                <span class=\\\"badge badge-success\\\">Certificado</span>\\n            </div>\\n            <div class=\\\"file-action card-documentos__action\\\">\\n                <a href=\\\"#\\\" class=\\\"btn btn-outline-secondary\\\">Adjuntar</a>\\n            </div>\\n        </div>\\n\\n        <div class=\\\"btn-group mb-5\\\">\\n          <a href=\\\"#\\\" class=\\\"btn btn-outline-fun-blue disabled\\\">Solicitar</a>\\n          <a href=\\\"#\\\" class=\\\"btn btn-outline-fun-blue\\\">Anexar a Carpeta</a>\\n        </div>\\n      </div>\\n    \";\n};\nvar showDetailsView = function showDetailsView(username) {\n  defaultDiv.classList.remove('default--show');\n  //opendetailview\n  previewDiv.classList.add('preview--show');\n  previewDiv.innerHTML = operacion(username);\n};\nvar showDocumentsView = function showDocumentsView() {\n  defaultDiv.classList.remove('default--show');\n  //opendetailview\n  previewDiv.classList.add('preview--show');\n  previewDiv.innerHTML = documentos();\n};\nvar showMultiView = function showMultiView() {\n  defaultDiv.classList.remove('default--show');\n  previewDiv.classList.remove('preview--show');\n  //opendetailview\n  defaultDiv.classList.add('default--show');\n  defaultDiv.innerHTML = multiSelect();\n};\nvar showDefaultView = function showDefaultView() {\n  defaultDiv.classList.add('default--show');\n  defaultDiv.innerHTML = defaultContent();\n};\nvar showLoaderView = function showLoaderView() {\n  defaultDiv.classList.add('default--show');\n  defaultDiv.innerHTML = defaultLoader();\n};\n\n//# sourceURL=webpack://front-bpm-consola/./Maqueta/js/finansys.js?");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = {};
/******/ 	__webpack_modules__["./Maqueta/js/finansys.js"]();
/******/ 	
/******/ })()
;