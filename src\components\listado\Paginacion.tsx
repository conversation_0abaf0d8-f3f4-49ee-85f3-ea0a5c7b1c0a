import React from 'react';
import { IPaginacionProps } from '../../entities/Interfaces/IPaginacion';

export const Paginacion: React.FC<IPaginacionProps> = ({
  numeroPagina,
  totalPaginas,
  tienePaginaAnterior,
  tienePaginaSiguiente,
  onPageChange
}) => {
  const renderPageButton = (page: number, isActive: boolean, label?: string | number) => {
  // Crear una clave única combinando el número de página y la etiqueta (si está presente) 
    const key = label ? `page-${page}-${String(label)}` : `page-${page}`;
    
    return (
      <button
        key={key}
        className={`btn btn-sm mx-1 ${isActive ? 'btn-secondary' : 'btn-outline-primary'}`}
        onClick={() => onPageChange(page)}
      >
        {label || page}
      </button>
    );
  };

  const renderPageDots = (key: string) => (
    <span key={`dots-${key}`} className="mx-1">...</span>
  );

  const renderPageRange = (start: number, end: number) => {
    const pages = [];
    for (let i = start; i <= end; i++) {
      pages.push(renderPageButton(i, i === numeroPagina));
    }
    return pages;
  };

  const renderDynamicPagination = () => {
    const pages = [];
    
    // Botón anterior
    if (tienePaginaAnterior) {
      pages.push(renderPageButton(numeroPagina - 1, false, '‹'));
    }
    
    // Calcular rango de páginas
    let startPage = Math.max(1, numeroPagina - 2);
    let endPage = Math.min(totalPaginas, numeroPagina + 2);
    
    // Ajustar para mostrar 5 páginas cuando sea posible
    if (endPage - startPage < 4) {
      endPage = startPage === 1 
        ? Math.min(totalPaginas, startPage + 4)
        : endPage;
      startPage = endPage === totalPaginas
        ? Math.max(1, endPage - 4)
        : startPage;
    }
    
    // Primera página
    if (startPage > 1) {
      pages.push(renderPageButton(1, 1 === numeroPagina));
      if (startPage > 2) pages.push(renderPageDots('dots1'));
    }
    
    // Rango de páginas actual
    pages.push(...renderPageRange(startPage, endPage));
    
    // Última página
    if (endPage < totalPaginas) {
      if (endPage < totalPaginas - 1) pages.push(renderPageDots('dots2'));
      pages.push(renderPageButton(totalPaginas, totalPaginas === numeroPagina));
    }
    
    // Botón siguiente
    if (tienePaginaSiguiente) {
      pages.push(renderPageButton(numeroPagina + 1, false, '›'));
    }
    return pages;
  };

  return (
    <div className="d-flex justify-content-center align-items-center mt-3">
      <div className="d-flex align-items-center">
        {renderDynamicPagination()}
      </div>
    </div>
  );
};
