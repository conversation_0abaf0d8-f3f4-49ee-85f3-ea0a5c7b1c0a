/*
 
 ███████╗ ██████╗ ███╗   ██╗████████╗███████╗ █████╗  ██████╗███████╗
 ██╔════╝██╔═══██╗████╗  ██║╚══██╔══╝██╔════╝██╔══██╗██╔════╝██╔════╝
 █████╗  ██║   ██║██╔██╗ ██║   ██║   █████╗  ███████║██║     █████╗  
 ██╔══╝  ██║   ██║██║╚██╗██║   ██║   ██╔══╝  ██╔══██║██║     ██╔══╝  
 ██║     ╚██████╔╝██║ ╚████║   ██║   ██║     ██║  ██║╚██████╗███████╗
 ╚═╝      ╚═════╝ ╚═╝  ╚═══╝   ╚═╝   ╚═╝     ╚═╝  ╚═╝ ╚═════╝╚══════╝
 Fuente para el font-face                                                                     
 
*/

body {
    -webkit-font-smoothing: antialiased;
}

@font-face {
    font-family: 'ContiSans';
    src: url('../fonts/ContiSansLight.eot');
    src: url('../fonts/ContiSansLight.eot?#iefix') format('embedded-opentype'),
        url('../fonts/ContiSansLight.woff2') format('woff2'),
        url('../fonts/ContiSansLight.woff') format('woff'),
        url('../fonts/ContiSansLight.svg#ContiSansLight') format('svg');
    font-weight: 300;
    font-style: normal;
}

@font-face {
    font-family: 'ContiSans';
    src: url('../fonts/ContiSansBold.eot');
    src: url('../fonts/ContiSansBold.eot?#iefix') format('embedded-opentype'),
        url('../fonts/ContiSansBold.woff2') format('woff2'),
        url('../fonts/ContiSansBold.woff') format('woff'),
        url('../fonts/ContiSansBold.svg#ContiSansBold') format('svg');
    font-weight: bold;
    font-style: normal;
}

@font-face {
    font-family: 'ContiSans';
    src: url('../fonts/ContiSansRegular.eot');
    src: url('../fonts/ContiSansRegular.eot?#iefix') format('embedded-opentype'),
        url('../fonts/ContiSansRegular.woff2') format('woff2'),
        url('../fonts/ContiSansRegular.woff') format('woff'),
        url('../fonts/ContiSansRegular.svg#ContiSansRegular') format('svg');
    font-weight: normal;
    font-style: normal;
}

