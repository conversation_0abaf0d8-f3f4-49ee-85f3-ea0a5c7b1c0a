import React, { useContext, useMemo, useState } from 'react'
import { useTable } from 'react-table'
import FacturaFormContext from '../contextProvider/counterContext'

export const FacturaArticulosTable = (props: any) => {
  const _FacturaFormContext = useContext(FacturaFormContext)
  const [counter, setCounter] = useState(1)
  const [counterAux, setCounterAux] = useState(0)
  let _idObj = {
    btnAgregar: 'btnAgregar' + _FacturaFormContext.counter + 'a' + counter,
    artcol: 'artcol' + _FacturaFormContext.counter + 'a' + counter,
    preciounitariocol: 'preciounitariocol' + _FacturaFormContext.counter + 'a' + counter,
    cantidadcol: 'cantidadcol' + _FacturaFormContext.counter + 'a' + counter,
    exentascol: 'exentascol' + _FacturaFormContext.counter + 'a' + counter,
    cincopcntcol: 'cincopcntcol' + _FacturaFormContext.counter + 'a' + counter,
    diezpcentcol: 'diezpcentcol' + _FacturaFormContext.counter + 'a' + counter,
    montocol: 'montocol' + _FacturaFormContext.counter + 'a' + counter
  }
  let button = <button type="button" id={_idObj.btnAgregar} className="btn btn-outline-primary"
    onClick={(e) => { setCounterAux(1) }}>Agregar</button>
  let fila = {
    artcol: <input id={_idObj.artcol} className="form-control" type="text" />,
    preciounitariocol: <input id={_idObj.preciounitariocol} className="form-control" type="number" />,
    cantidadcol: <input id={_idObj.cantidadcol} className="form-control" type="number" />,
    exentascol: <input id={_idObj.exentascol} className="form-control" type="number" />,
    cincopcntcol: <input id={_idObj.cincopcntcol} className="form-control" type="number" />,
    diezpcentcol: <input id={_idObj.diezpcentcol} className="form-control" type="number" />,
    montocol: <input id={_idObj.montocol} className="form-control" type="number" />,
    botonholdercol: button
  }
  const [filas, setFilas] = useState([fila])

  function agregarFilas(_counter: number, _fila?: any) {
    console.log('entro Aca')
    _idObj = {
      btnAgregar: 'btnAgregar' + _FacturaFormContext.counter + 'a' + _counter,
      artcol: 'artcol' + _FacturaFormContext.counter + 'a' + _counter,
      preciounitariocol: 'preciounitariocol' + _FacturaFormContext.counter + 'a' + _counter,
      cantidadcol: 'cantidadcol' + _FacturaFormContext.counter + 'a' + _counter,
      exentascol: 'exentascol' + _FacturaFormContext.counter + 'a' + _counter,
      cincopcntcol: 'cincopcntcol' + _FacturaFormContext.counter + 'a' + _counter,
      diezpcentcol: 'diezpcentcol' + _FacturaFormContext.counter + 'a' + _counter,
      montocol: 'montocol' + _FacturaFormContext.counter + 'a' + counter
    }


    button = <button type="button" id={_idObj.btnAgregar} className="btn btn-outline-primary" onClick={(e) => {
      setCounterAux(_counter + 1)

    }}>Agregar</button>
    let filaSec = {
      artcol: <input id={_idObj.artcol} className="form-control" type="text" />,
      preciounitariocol: <input id={_idObj.preciounitariocol} className="form-control" type="number" />,
      cantidadcol: <input id={_idObj.cantidadcol} className="form-control" type="number" />,
      exentascol: <input id={_idObj.exentascol} className="form-control" type="number" />,
      cincopcntcol: <input id={_idObj.cincopcntcol} className="form-control" type="number" />,
      diezpcentcol: <input id={_idObj.diezpcentcol} className="form-control" type="number" />,
      montocol: <input id={_idObj.montocol} className="form-control" type="number" />,
      botonholdercol: button
    }
    setFilas((prevFilas) => ([...prevFilas, filaSec]))
    const _counterMinus = _counter - 1
    const idBtnAnterior = 'btnAgregar' + _FacturaFormContext.counter + 'a' + _counterMinus
    hideEl(idBtnAnterior)

  }
  let columnasData: [{ Header: string, accesor: string }]

  columnasData = props.columnas

  const columns = useMemo(
    () =>
      columnasData
    , [])

  const data = useMemo(
    () => filas, [filas]
  )

  const hideEl = (idEl: string) => {
    const _btnEl = document.getElementById(idEl)
    _btnEl.hidden = true
  }



  useMemo(() => {
    setCounter((x) => { let resp = x + 1; return resp })
    console.log(data)
    let arrayHoler = filas
    if (counter > 1) {
      agregarFilas(counter, arrayHoler)
    }
  }, [counterAux])

  let tableInstance = useTable({ columns, data })
  let {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    rows,
    prepareRow,
  } = tableInstance

  return (

    <div className="table-responsive">
      <table className="table table-invoice" {...getTableProps()}>
        <thead>
          {// Loop over the header rows
            headerGroups.map(headerGroup => (
              // Apply the header row props
              <tr className="text-center"{...headerGroup.getHeaderGroupProps()}>
                {// Loop over the headers in each row
                  headerGroup.headers.map(column => (
                    // Apply the header cell props
                    <th {...column.getHeaderProps()}>
                      {// Render the header
                        column.render('Header')}
                    </th>
                  ))}
              </tr>
            ))}
        </thead>
        {/* Apply the table body props */}
        <tbody id={'articulosBody' + _FacturaFormContext.counter} {...getTableBodyProps()}>
          {// Loop over the table rows
            rows.map((row, indexrow) => {
              // Prepare the row for display
              prepareRow(row)
              return (
                // Apply the row props
                <tr id={'articuloRow' + _FacturaFormContext.counter + indexrow} className="text-center"{...row.getRowProps()}>
                  {// Loop over the rows cells
                    row.cells.map((cell, cellIndex) => {
                      // Apply the cell props
                      return (
                        <td {...cell.getCellProps()}>
                          {// Render the cell contents
                            cell.render('Cell')}
                        </td>
                      )
                    })}
                </tr>
              )
            })}
        </tbody>
      </table>
    </div>

  )

}