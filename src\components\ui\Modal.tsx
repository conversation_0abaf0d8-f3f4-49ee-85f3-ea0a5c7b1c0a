import React, { useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  closeOnBackdrop?: boolean;
  closeOnEscape?: boolean;
}

export const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  size = 'md',
  closeOnBackdrop = true,
  closeOnEscape = true
}) => {
  const dialogRef = useRef<HTMLDialogElement>(null);

  useEffect(() => {
    if (!isOpen) return;

    const dialog = dialogRef.current;
    if (dialog) {
      dialog.showModal();
    }

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && closeOnBackdrop) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);

    return () => {
      if (dialog) {
        dialog.close();
      }
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose, closeOnBackdrop]);

  if (!isOpen) return null;

  const sizeClasses = {
    sm: 'modal-sm',
    md: 'modal-md', 
    lg: 'modal-lg',
    xl: 'modal-xl'
  };

  return createPortal(
    <dialog 
      ref={dialogRef}
      className="modal fade show d-block"
      style={{ backgroundColor: 'transparent', border: 'none' }}
      onClose={onClose}
    >
      <div className={`modal-dialog modal-dialog-centered ${sizeClasses[size]}`}>
        <div className="modal-content">
          {title && (
            <div className="modal-header">
              <h5 className="modal-title">{title}</h5>
              <button
                type="button"
                className="close"
                onClick={onClose}
                aria-label="Close"
              >
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
          )}
          <div className="modal-body">
            {children}
          </div>
        </div>
      </div>
    </dialog>,
    document.body
  );
};


