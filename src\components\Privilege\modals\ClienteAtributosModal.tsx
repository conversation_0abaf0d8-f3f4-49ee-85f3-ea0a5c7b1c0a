import React, { useState } from 'react';
import { Button, Modal, FormControl } from 'react-bootstrap';
import { esNumerico, formatMonto, quitarCerosIzquierda } from '../../../utilities/dataUtil';

const ClienteAtributosModal = ({ show, onHide, datosCliente }: { show: boolean, onHide: () => void, datosCliente?: any[] }) => {
  const [searchTerm, setSearchTerm] = useState('');

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const filteredDatosCliente = datosCliente?.filter(detalle => {
    const atributo = detalle?.atributo?.atributo || '';
    const valor = detalle?.valor != null ? detalle.valor.toString() : '';
    return (
      atributo.toLowerCase().includes(searchTerm.toLowerCase()) ||
      valor.toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  return (
    <Modal show={show} onHide={onHide} size="lg" aria-labelledby="contained-modal-title-vcenter" centered>
      <Modal.Header style={{ justifyContent: 'center' }}>
        <Modal.Title>Detalles del Cliente</Modal.Title>
      </Modal.Header>
      <Modal.Body 
        id="style-4" 
        className="force-overflow scroll-bar" 
        style={{ marginLeft: '0px', padding: '25px', maxHeight: '70vh', overflowY: 'auto' }}        
      >
        <FormControl
          type="text"
          placeholder="Buscar..."
          value={searchTerm}
          onChange={handleSearchChange}
          className="mb-3"
          style={{ position: 'fixed', top: 90, width: '49%', zIndex: 100 }}
        />
        {filteredDatosCliente && filteredDatosCliente.length > 0 ? (
          filteredDatosCliente.map((detalle, index) => (
            <div key={index} className="card tarjeta-atributos mt-2">
              <div className="card-body">
                <h5 className="card-title">{detalle?.atributo?.atributo}</h5>
                <span className="card-subtitle mb-2 text-muted">
                  {esNumerico(detalle?.valor) ? `${formatMonto(quitarCerosIzquierda(detalle?.valor))}` : detalle?.valor}
                </span>
              </div>
            </div>
          ))
        ) : (
          <p>No se encontraron coincidencias.</p>
        )}
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Cerrar
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ClienteAtributosModal;