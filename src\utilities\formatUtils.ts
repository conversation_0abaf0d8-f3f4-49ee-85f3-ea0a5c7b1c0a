export const formatearNumeroFactura = (numeroFactura: string): string => {
  if (!numeroFactura || numeroFactura === '0') return '0';
  
  // Si ya tiene el formato correcto, devolverlo
  if (numeroFactura.includes('-')) return numeroFactura;
  
  // Limpiar espacios y caracteres no numéricos
  const numeroLimpio = numeroFactura.replace(/\D/g, '');
  
  // Todos los números tienen 13 dígitos, formatear como 000-000-0000000
  if (numeroLimpio.length === 13) {
    return `${numeroLimpio.substring(0, 3)}-${numeroLimpio.substring(3, 6)}-${numeroLimpio.substring(6)}`;
  }
  
  // Si tiene menos dígitos, agregar padding y formatear
  if (numeroLimpio.length >= 7) {
    const padded = numeroLimpio.padStart(13, '0');
    return `${padded.substring(0, 3)}-${padded.substring(3, 6)}-${padded.substring(6)}`;
  }
  
  return numeroFactura;
};



