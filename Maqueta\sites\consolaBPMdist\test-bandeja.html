<!doctype html>
<html lang="es">

<head>
  <!-- Required meta tags -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <!-- CSS -->
  <link rel="stylesheet" href="../../css/finansys.css">
  <link rel="stylesheet" href="./c_css/custom.css">
  <title>Bandeja de Solicitudes</title>
  <script src="https://kit.fontawesome.com/192ee6ec02.js" crossorigin="anonymous"></script>

  <style>
    a:hover {
      text-decoration: none;
    }

    #body-row {
      margin-left: 0;
      margin-right: 0;
    }

    #sidebar-container {
      min-height: 100vh;
      background-color: #343A40;
      padding: 0;
    }

    .list-group-item {
      position: relative;
      display: block;
      padding: .75rem 1.25rem;
      background-color: #fff;
      border: 0px solid rgba(0, 0, 0, .125);
    }

    .sidebar-expanded {
      width: 290px;
    }

    .sidebar-collapsed {
      width: 60px;
    }

    #sidebar-container .list-group a {
      height: 50px;
      color: white;
    }

    #sidebar-container .list-group .sidebar-submenu a {
      height: 45px;
      padding-left: 30px;
    }

    .sidebar-submenu {
      font-size: 0.9rem;
    }

    .sidebar-separator-title {
      background-color: #333;
      height: 35px;
    }

    .sidebar-separator {
      background-color: #333;
      height: 25px;
    }

    .logo-separator {
      background-color: #333;
      height: 60px;
    }

    #sidebar-container .list-group .list-group-item[aria-expanded="false"] .submenu-icon::after {
      content: " \f107";
      font-family: FontAwesome;
      display: inline;
      text-align: right;
      padding-left: 10px;
    }

    #sidebar-container .list-group .list-group-item[aria-expanded="true"] .submenu-icon::after {
      content: " \f106";
      font-family: FontAwesome;
      display: inline;
      text-align: right;
      padding-left: 10px;
    }

    .fa-2x {
      font-size: 24px;
    }
  </style>

</head>

<body>
  <nav class="navbar navbar-expand-lg navbar-dark fixed-top bg-fun-blue">
    <a class="navbar-brand" href="#"><img src="../../img/brand/bc-bg-dark.svg" class="brand brand--md"></a>

    <div class="collapse navbar-collapse" id="navbarText">
      <div class="ml-3">
        <input type="text" class="header-search" id="inputSearch" placeholder="Buscar clientes, operaciones, etc..." />
      </div>
      <button class="btn btn-primary ml-2" id="buscarClientes">Buscar</button>
      <span class="ml-auto navbar-text d-flex align-items-center">
        <figure class="avatar avatar--sm mr-2">
          <img src="https://randomuser.me/api/portraits/men/1.jpg">
        </figure>
        Alejandro Emanuel Denis Piris
      </span>
    </div>
  </nav>

  <div class="grid-container">
    <!-- Sidebar -->
    <div id="sidebar-container" class="sidebar-expanded d-md-block d-sm-block">
      <!-- d-* hiddens the Sidebar in smaller devices. Its itens can be kept on the Navbar 'Menu' -->
      <!-- Bootstrap List Group -->
      <ul class="list-group pl-0">

        <a href="#" class="bg-dark list-group-item list-group-item-action flex-column align-items-start">
          <div class="d-flex w-100 justify-content-start align-items-center">
            <span class="fas fa-home mr-3"></span>
            <span class="menu-collapsed">Inicio</span>
          </div>
        </a>
        <a href="#submenu1" data-toggle="collapse" aria-expanded="false"
          class="bg-dark list-group-item list-group-item-action flex-column align-items-start">
          <div class="d-flex w-100 justify-content-start align-items-center">
            <span class="fas fa-toolbox mr-3"></span>
            <span class="menu-collapsed">Productos y servicios</span>
            <span class="submenu-icon ml-auto"></span>
          </div>
        </a>
        <!-- Submenu content -->
        <div id='submenu1' class="collapse sidebar-submenu">
          <a href="#" class="list-group-item list-group-item-action bg-dark text-white">
            <span class="menu-collapsed">Solicitudes</span>
          </a>
          <a href="#" class="list-group-item list-group-item-action bg-dark text-white">
            <span class="menu-collapsed">Crear solicitudes</span>
          </a>
        </div>
        <a href="#submenu2" data-toggle="collapse" aria-expanded="false"
          class="bg-dark list-group-item list-group-item-action flex-column align-items-start">
          <div class="d-flex w-100 justify-content-start align-items-center">
            <span class="fas fa-users mr-3"></span>
            <span class="menu-collapsed">Clientes</span>
            <span class="submenu-icon ml-auto"></span>
          </div>
        </a>
        <!-- Submenu content -->
        <div id='submenu2' class="collapse sidebar-submenu">
          <a href="#" class="list-group-item list-group-item-action bg-dark text-white">
            <span class="menu-collapsed">Alta de cliente</span>
          </a>
        </div>
        <a href="#" class="bg-dark list-group-item list-group-item-action flex-column align-items-start">
          <div class="d-flex w-100 justify-content-start align-items-center">
            <span class="fas fa-file-import mr-3"></span>
            <span class="menu-collapsed">Bandeja de solicitudes</span>
            <span class="submenu-icon ml-auto"></span>
          </div>
        </a>

        <a href="#" class="bg-dark list-group-item list-group-item-action flex-column align-items-start">
          <div class="d-flex w-100 justify-content-start align-items-center">
            <span class="fas fa-hourglass-end mr-3"></span>
            <span class="menu-collapsed">Bandeja de aprobaciones</span>
            <span class="submenu-icon ml-auto"></span>
          </div>
        </a>

        <a href="#" class="bg-dark list-group-item list-group-item-action flex-column align-items-start">
          <div class="d-flex w-100 justify-content-start align-items-center">
            <span class="fas fa-gift mr-3"></span>
            <span class="menu-collapsed">Pre-Aprobados</span>
            <span class="submenu-icon ml-auto"></span>
          </div>
        </a>

        <a href="#submenu3" data-toggle="collapse" aria-expanded="false"
          class="bg-dark list-group-item list-group-item-action flex-column align-items-start">
          <div class="d-flex w-100 justify-content-start align-items-center">
            <span class="fas fa-credit-card mr-3"></span>
            <span class="menu-collapsed">Tarjetas</span>
            <span class="submenu-icon ml-auto"></span>
          </div>
        </a>
        <!-- Submenu content -->
        <div id='submenu3' class="collapse sidebar-submenu">
          <a href="#" class="list-group-item list-group-item-action bg-dark text-white">
            <span class="menu-collapsed">Gestionar tarjetas</span>
          </a>
        </div>

        <a href="#top" data-toggle="sidebar-colapse"
          class="bg-dark list-group-item list-group-item-action d-flex align-items-center">
          <div class="d-flex w-100 justify-content-start align-items-center">
            <span id="collapse-icon" class="fa fa-2x mr-3"></span>
            <span id="collapse-text" class="menu-collapsed"></span>
          </div>
        </a>
      </ul><!-- List Group END-->
    </div><!-- sidebar-container END -->

    <!-- Page content -->
    <div class="content">
      <link href="https://maxcdn.bootstrapcdn.com/font-awesome/4.3.0/css/font-awesome.min.css" rel="stylesheet">

      <div class="col-md-12">
        <div class="invoice">
          <!-- begin invoice-company -->
          <div class="invoice-company text-inverse f-w-600">
            <span class="pull-right hidden-print">
              <a href="javascript:;" class="btn btn-sm btn-white m-b-10 p-l-5"><i
                  class="fa fa-file t-plus-1 text-danger fa-fw fa-lg"></i>Adjuntar</a>
            </span>
            Carga de Facturas
          </div>
          <!-- end invoice-company -->
          <!-- begin invoice-header -->
          <div class="invoice-header">
            <form class="form-inline">
            <input class="form-control mr-sm-2 bg-light" type="search" placeholder="Cod Cliente"
            aria-label="Search">
          <button class="btn btn-outline-success my-2 my-sm-0" type="submit">Buscar Proveedor</button>
          </form><br>
            <div class="invoice-from">
              <small>from</small>
              <address class="m-t-5 m-b-5">
                <strong class="text-inverse">RIO PARANA.</strong><br>
                CALLE 10 Nº 183 C/ BERNARDINO CABALLERO<br>
                SALTO DEL GUAIRA-PARAGUAY<br>
                Telefono: 0986-209-721<br>
                Numero de documento: 38295156
              </address>
            </div>
            <div class="invoice-to">
              <small>to</small>
              <address class="m-t-5 m-b-5">
                <strong class="text-inverse">Banco Continental</strong><br>
                Street Address<br>
                City, Zip Code<br>
                Phone: (*************<br>
                Fax: (*************
              </address>
            </div>
            <div class="invoice-date">
              <small>Factura / July period</small>
              <div class="date text-inverse m-t-5">Augusto 13,2012</div>
              <div class="invoice-detail">
                <div class="input-group mb-3">
                  <div class="input-group-prepend">
                    <span class="input-group-text" id="timbrado-input">T</span>
                  </div>
                  <input type="text" class="form-control bg-light" placeholder="Timbrado" aria-label="Timbrado"
                    aria-describedby="timbrado-input">
                </div>
                <div class="input-group mb-3">
                  <div class="input-group-prepend">
                    <span class="input-group-text" id="Numero-de-factura-input">#</span>
                  </div>
                  <input type="text" class="form-control bg-light" placeholder="Numero de Factura" aria-label="Numero de Factura"
                    aria-describedby="Numero-de-factura-input">
                </div>
                <form class="row">
                  <div class="col">
                    <input class="form-check-input" type="checkbox" value="" id="provisionCheck">
                    <label class="form-check-label text-left" for="provisionCheck">
                      Es provision
                    </label>
                  </div>
                  <div class="col">
                    <input class="form-check-input" type="checkbox" id="revisionCheck" value="">
                    <label class="form-check-label" for="revisionCheck">Revision?</label>
                  </div>
                  <div class="col">
                    <label for="inputState">Moneda</label>
                    <select id="inputState" class="form-control">
                      <option selected>PYG</option>
                      <option>USD</option>
                    </select>
                  </div>
                </form>
              </div>
            </div>
            </div>
          <!-- end invoice-header -->
          <!-- begin invoice-content -->
          <div class="invoice-content">
            <!-- begin table-responsive -->
            <div class="table-responsive">
              <table class="table table-invoice">
                <thead>
                  <tr>
                    <th>ARTICULO</th>
                    <th class="text-center" width="10%">PRECIO UNITARIO</th>
                    <th class="text-center" width="10%">EXENTAS</th>
                    <th class="text-right" width="10%">5%</th>
                    <th class="text-right" width="10%">10%</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>
                      <span class="text-inverse">Website design &amp; development</span><br>
                      <small>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed id sagittis arcu.</small>
                    </td>
                    <td class="text-center">$50.00</td>
                    <td class="text-center">50</td>
                    <td class="text-right">$2,500.00</td>
                  </tr>
                  <tr>
                    <td>
                      <span class="text-inverse">Branding</span><br>
                      <small>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed id sagittis arcu.</small>
                    </td>
                    <td class="text-center">$50.00</td>
                    <td class="text-center">40</td>
                    <td class="text-right">$2,000.00</td>
                  </tr>
                  <tr>
                    <td>
                      <span class="text-inverse">Redesign Service</span><br>
                      <small>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed id sagittis arcu.</small>
                    </td>
                    <td class="text-center">$50.00</td>
                    <td class="text-center">50</td>
                    <td class="text-right">$2,500.00</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <!-- end table-responsive -->
            <!-- begin invoice-price -->
            <div class="invoice-price">
              <div class="invoice-price-left">
              <form>
                <div class="row">
                  <div class="col">
                    <label for="total-input"><strong>TOTAL</strong></label>
                    <input type="number" id ="total-input" class="form-control" placeholder=0>
                  </div>
                  <div class="col">
                    <label for="condicionv-input">Condicion Venta</label>
                    <input type="number" id="condicionv-input" class="form-control" placeholder=0>
                  </div>
                </div>
                <div class="row">
                  <div class="col">
                    <label for="5pct-input">Monto 5%</label>
                    <input type="number" id="5pct-input" class="form-control" placeholder=0>
                  </div>
                  <div class="col">
                    <label for="10pct-input">Monto 10%</label>
                    <input type="number" id="10pct-input" class="form-control" placeholder=0>
                  </div>
                </div>
                <div class="row">
                  <div class="col">
                    <label for="gravadas5-input">Gravadas 5%</label>
                    <input type="number" id="gravadas5-input" class="form-control" placeholder=0>
                  </div>
                  <div class="col">
                    <label for="gravadas10-input">Gravadas 10%</label>
                    <input type="number" id="gravadas10-input" class="form-control" placeholder=0>
                  </div>
                </div>
                <div class="row">
                  <div class="col">
                    <label for="iva5-input">IVA 5%</label>
                    <input type="number" id="iva5-input" class="form-control" placeholder=0>
                  </div>
                  <div class="col">
                    <label for="iva10-input">IVA 10%</label>
                    <input type="number" id="iva10-input" class="form-control" placeholder=0>
                  </div>
                </div>
                <div class="row">
                <div class="form-group col">
                  <label for="exampleFormControlTextarea1">Descripcion</label>
                  <textarea class="form-control" maxlength="256" id="exampleFormControlTextarea1" rows="3"></textarea>
                </div>
                <div class="col">
                  <label for="exentas-input">Exentas</label>
                  <input type="number" id="exentas-input" class="form-control" placeholder=0>
                  <br/>
                  <button type="submit" class="btn btn-primary mb-2 left">Iniciar Seguimiento</button>
                </div>
              </div>
              </form>
            </div>
              <!-- <div class="invoice-price-left">
                <div class="invoice-price-row">
                  <div class="sub-price">
                    <small>SUBTOTAL</small>
                    <span class="text-inverse">$4,500.00</span>
                  </div>
                  <div class="sub-price">
                    <i class="fa fa-plus text-muted"></i>
                  </div>
                  <div class="sub-price">
                    <small>PAYPAL FEE (5.4%)</small>
                    <span class="text-inverse">$108.00</span>
                  </div>
                </div> -->
              </div>
              <!-- <div class="invoice-price-right">
                <small>TOTAL</small> <span class="f-w-600">$4508.00</span>
              </div> -->
            </div>

            <!-- end invoice-price -->
          </div>
          <!-- end invoice-content -->
          <!-- begin invoice-note -->
          <div class="invoice-note">
            * Make all cheques payable to [Your Company Name]<br>
            * Payment is due within 30 days<br>
            * If you have any questions concerning this invoice, contact [Name, Phone Number, Email]
          </div>
          <!-- end invoice-note -->
          <!-- begin invoice-footer -->
          <div class="invoice-footer">
            <p class="text-center m-b-5 f-w-600">
              THANK YOU FOR YOUR BUSINESS
            </p>
            <p class="text-center">
              <span class="m-r-10"><i class="fa fa-fw fa-lg fa-globe"></i> matiasgallipoli.com</span>
              <span class="m-r-10"><i class="fa fa-fw fa-lg fa-phone-volume"></i> T:016-18192302</span>
              <span class="m-r-10"><i class="fa fa-fw fa-lg fa-envelope"></i> <EMAIL></span>
            </p>
          </div>
          <!-- end invoice-footer -->
        </div>
      </div>

    </div>
  </div>

  <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js"
    integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo"
    crossorigin="anonymous"></script>
  <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/js/bootstrap.min.js"
    integrity="sha384-wfSDF2E50Y2D1uUdj0O3uMBJnjuUD4Ih7YwaYd1iqfktj0Uod8GCExl3Og8ifwB6"
    crossorigin="anonymous"></script>
  <script src="./list.main.js"></script>
  <script src="./finasys.main.js"></script>
  <script src="https://unpkg.com/react@18/umd/react.development.js" crossorigin></script>
  <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js" crossorigin></script>



  <script>
    // Hide submenus
    $('#body-row .collapse').collapse('hide');

    // Collapse/Expand icon
    $('#collapse-icon').addClass('fa-angle-double-left');

    // Collapse click
    $('[data-toggle=sidebar-colapse]').click(function () {
      SidebarCollapse();
    });

    function SidebarCollapse() {
      $('.menu-collapsed').toggleClass('d-none');
      $('.sidebar-submenu').toggleClass('d-none');
      $('.submenu-icon').toggleClass('d-none');
      $('#sidebar-container').toggleClass('sidebar-expanded sidebar-collapsed');

      // Treating d-flex/d-none on separators with title
      var SeparatorTitle = $('.sidebar-separator-title');
      if (SeparatorTitle.hasClass('d-flex')) {
        SeparatorTitle.removeClass('d-flex');
      } else {
        SeparatorTitle.addClass('d-flex');
      }

      // Collapse/Expand icon
      $('#collapse-icon').toggleClass('fa-angle-double-left fa-angle-double-right');
    }

  </script>
</body>

</html>