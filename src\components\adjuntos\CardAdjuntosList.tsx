/// <reference types="webpack/module" />

import { isNull, isUndefined } from "lodash";
import React, { useMemo, useState } from "react";
import { getAdjuntos } from "../../services/adjuntosServices";
import { BtnSubirAdjuntos } from "./BtnSubirAdjuntos";
import { AdjuntoCard } from "./AdjuntoCard"

import { subirAdjuntos } from "../../services/adjuntosServices";
import { DatosProvider, useProveedor } from "../contextProvider/datosProveedorContext";

'use strict'

export const CardAdjuntosList = (props: any) => {
  const {state} = useProveedor();
  const [cardAdjuntos, setCardAdjuntos] = useState([<></>])
  const [btnAdjuntar, setBtnAdjuntar] = useState(<></>)
  const [adjuntos, setAdjuntos] = useState({})

  const [selectedFile, setSelectedFile] = useState(null);

  const documentosASubir = () => {
    if (isNull(window.appGlobal) || isUndefined(window.appGlobal)) {
      setAdjuntos(null)
    }
    else {
      setAdjuntos(window.appGlobal)
      console.log("adjuntos:", adjuntos);
    }
    console.log("window.appGlobal:", window.appGlobal);
  }

  const armarListado = async () => {
    let _listadoAdjuntos: any = await getAdjuntos(props.operacion, '3108');
    console.log("_listadoAdjuntos:", _listadoAdjuntos);
    const _jsxArrCardAdjuntos = _listadoAdjuntos?.map((e: any) => (
      <div key={e.idDocumentos}>
        {AdjuntoCard(e)}
      </div>
    ));
    console.log("_jsxArrCardAdjuntos:", _jsxArrCardAdjuntos);
    return _jsxArrCardAdjuntos;
  }

  const handleFileChange = async (files: FileList) => {
    try {
      await subirAdjuntos(files, 'codigoCliente');
      console.log("Subida exitosa del archivo:", files);
      setSelectedFile(files[0]);
    } catch (error) {
      console.error("Error al subir el archivo:", error);
    }
  };

  console.log("selectedFile:", selectedFile);

  useMemo(async () => {
    state.inicioDeSeguimientoCondicion = props.isInicioDeSeguimiento;
    if (props.isInicioDeSeguimiento) {
      documentosASubir();
      const _btnSubirAdjuntos = <div className="btn-group">
        <DatosProvider><BtnSubirAdjuntos adjuntos={window.appGlobal} onFileChange={handleFileChange} /></DatosProvider>
      </div>
      setBtnAdjuntar(_btnSubirAdjuntos);
    }
    else {
      const _unCardAdjunto = await armarListado();
      console.log("_unCardAdjunto:", _unCardAdjunto);
      setCardAdjuntos(_unCardAdjunto);
    }
  }, [props.isInicioDeSeguimiento, window.appGlobal]) // MODIFICADO

  return (
    <React.StrictMode>
      <div id='CardAdjuntosList'>
        {cardAdjuntos}
        {btnAdjuntar}
      </div>
    </React.StrictMode>
  )
}

