/// <reference types="webpack/module" />

import { List } from "./listado";
import { ListaItem } from "./listadoData";
import React, { useState, useCallback } from "react";
import { IlistadoItemFactura } from "../../entities/Intefaces/IListado";
import { useProveedor } from "../contextProvider/datosProveedorContext";
import { Paginacion } from './Paginacion';
import { formatearNumeroFactura } from '../../utilities/formatUtils';

export const ListArmado = (props: any) => {
  const { state } = useProveedor();
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const esBusquedaFiltrada = props.esBusquedaFiltrada || false;

  const buscarIdTarea = useCallback((nroOp: string, listadoDatos: IlistadoItemFactura[]): number | undefined => {
    const tarea = listadoDatos.find(t => t.titulo?.includes(nroOp));
    return tarea?.idTarea;
  }, []);

  const procesarDatos = useCallback(() => {
    let listadoDatos: IlistadoItemFactura[] = [];
    let tareas: any[] = [];
    let tareasOperacionID: any[] = [];

    if (!props.operaciones || props.operaciones.length === 0) {
      listadoDatos = List || [];
    } else {
      listadoDatos = props.operaciones;
      tareas = props.tareas || [];
      if (tareas.length > 0) {
        tareasOperacionID = tareas.map((item) => item.WorkflowLink?.Description || '');
      }
    }

    if (props.operaciones && props.operaciones.length > 0) {
      state.listadoItemFacturas = props.operaciones;
    }

    return { listadoDatos, tareas, tareasOperacionID };
  }, [props.operaciones, props.tareas, state]);

  const { listadoDatos, tareas, tareasOperacionID } = procesarDatos();

  const procesarOperacionesTransformadas = useCallback(() => {
    if (!esBusquedaFiltrada || !state.operacionCliente || !Array.isArray(state.operacionCliente) || state.operacionCliente.length === 0) {
      return [];
    }

    return state.operacionCliente.map((operacion: any) => {
      if (!operacion || typeof operacion.id !== 'number') {
        return null;
      }

      const formatearFecha = (fechaISO: string) => {
        try {
          const fecha = new Date(fechaISO);
          const dia = fecha.getDate().toString().padStart(2, '0');
          const mes = (fecha.getMonth() + 1).toString().padStart(2, '0');
          const anio = fecha.getFullYear();
          const hora = fechaISO.slice(11, 19);
          return `${dia}/${mes}/${anio} ${hora}`;
        } catch {
          return fechaISO;
        }
      };

      const mapearEstado = (codigoEstado: string) => {
        const estados: Record<string, string> = {
          "ESTAPR": "Aprobado",
          "ESTPEN": "Pendiente", 
          "ESTREC": "Rechazado",
          "ESTESP": "En Espera",
          "ESTBAJ": "En Baja",
          "ESTINA": "Inactivo",
          "ESTREL": "Realizado"
        };
        return estados[codigoEstado] || "Pendiente";
      };

      const nroOperacion = operacion.id.toString();
      const tareaID = buscarIdTarea(nroOperacion, listadoDatos);

      const nroFactura = operacion.atributos?.find((item: any) => item.codigo === "ATRFAC")?.valor || 
                        operacion.atributos?.find((item: any) => item.codigo === "ATRLFA")?.valor || '0';

      return {
        titulo: `Operacion Nro: ${operacion.id}`,
        subTitulo: `Nro de Factura: ${formatearNumeroFactura(nroFactura)}`,
        fecha: {
          string: formatearFecha(operacion.fechaHoraCreacion),
          date: new Date(operacion.fechaHoraCreacion)
        },
        estado: mapearEstado(operacion.codigoEstado),
        monto: operacion.atributos?.find((item: any) => item.codigo === "ATRMON")?.valor === "USD" ?
          (operacion.atributos?.find((atributo: any) => atributo.codigo === "ATMONF"))?.valor :
          (operacion.atributos?.find((atributo: any) => atributo.codigo === "ATMONG"))?.valor,
        monedaTipo: operacion.atributos?.find((item: any) => item.codigo === "ATRMON")?.valor || 'PYG',
        idTarea: tareaID
      };
    }).filter(Boolean);
  }, [esBusquedaFiltrada, state.operacionCliente, buscarIdTarea, listadoDatos]);

  const operacionesTransformadas = procesarOperacionesTransformadas();

  const procesarListaFiltrada = () => {
    let listadoFiltrado: IlistadoItemFactura[] = [];

    if (listadoDatos && listadoDatos.length > 0) {
      listadoFiltrado = [...listadoDatos];
      
      if (tareas && tareas.length > 0) {
        tareasOperacionID.forEach((element, index) => {
          const regex = /\d*$/;
          const operacionEncontrada = listadoFiltrado.find((item) => {
            if (!item.titulo) return false;
            const match = regex.exec(item.titulo.toLowerCase());
            return match?.[0]?.indexOf(element.toLowerCase()) !== -1;
          });
          
          if (operacionEncontrada && !operacionEncontrada.idTarea) {
            operacionEncontrada.idTarea = tareas[index].ID;
          }
        });
      }
    }

    return listadoFiltrado;
  };

  const filteredList = procesarListaFiltrada();

  let listToShow;
  if (esBusquedaFiltrada) {
    listToShow = operacionesTransformadas;
  } else {
    listToShow = filteredList && filteredList.length > 0 ? filteredList : [];
  }

  const finalList = props.paginacion ? listToShow : listToShow.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  return (
    <div>
      <ol start={props.paginacion ? ((props.paginacion.numeroPagina - 1) * props.paginacion.tamanoPagina + 1) : ((currentPage - 1) * itemsPerPage + 1)}>
        {finalList.map((item, index) => (
          <li key={`item-${props.paginacion?.numeroPagina || currentPage}-${index}`}>
            <ListaItem {...item} />
          </li>
        ))}
      </ol>
      {props.paginacion && props.paginacion.totalPaginas > 1 && (
        <Paginacion
          numeroPagina={props.paginacion.numeroPagina}
          totalPaginas={props.paginacion.totalPaginas}
          tienePaginaAnterior={props.paginacion.tienePaginaAnterior}
          tienePaginaSiguiente={props.paginacion.tienePaginaSiguiente}
          onPageChange={props.onPageChange}
        />
      )}
      {!props.paginacion && listToShow.length > itemsPerPage && (
        <Paginacion
          numeroPagina={currentPage}
          totalPaginas={Math.ceil(listToShow.length / itemsPerPage)}
          tienePaginaAnterior={currentPage > 1}
          tienePaginaSiguiente={currentPage < Math.ceil(listToShow.length / itemsPerPage)}
          onPageChange={setCurrentPage}
        />
      )}
    </div>
  );
};
