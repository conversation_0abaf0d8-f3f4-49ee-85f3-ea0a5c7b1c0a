import React, { useEffect, useState } from 'react'
import { Button, Form, Modal } from 'react-bootstrap';
import delegarDe from '../delegarTareas/delegarDe';
import { getUrlSeguimientoFactura, getUrlSPBase } from '../../utilities/contextInfo';
import { obtenerUsuario } from '../../comm/obtenerUsuario';
import { Estados } from '../../entities/Enums/estadosRespuesta';
import { LoaderSmall } from '../loader/Loader';
import ModalValidacionDelegacionDeTareasFallida from '../modales/ModalValidacionDelegacionDeTareasFallida';
import { getAccionesPorEstado } from '../../comm/apiBpmPoliticasProcesos';
import { actualizarAutorizante } from '../actualizarAutorizante';
import { obtenerItemsAutorizantes } from '../../comm/apiSharePoint';

const BtnDelegacionDeTareas = () => {
  const [show, setShow] = useState(false);
  const [numeroOperacion, setNumeroOperacion] = useState("");
  const [delegarde, setDelegarde] = useState("");;
  const [asignarA, setAsignarA] = useState("");
  const [loading, setLoading] = useState(false);

  const [modalValidacionFallida, setModalValidacionFallida] = useState(false);
  const [mensajeValidacion, setMensajeValidacion] = useState("");
  const cerrarModalValidacionFallida = () => { setModalValidacionFallida(false); };
  const abrirModalValidacionCompletarTodosLosCampos = () => { setModalValidacionFallida(true); setMensajeValidacion("Debe completar todos los campos."); };
  const abrirModalValidacionNoSePuedeDelegar = () => { setModalValidacionFallida(true); setMensajeValidacion("No se puede delegar a sí mismo."); };

  const abrirModal = () => { setShow(true) }
  const cerrarModal = () => { setShow(false) }

  const obtenerID = (numeroOperacion: any, results: any) => {
    const resultado = results.find((item: any) => item.WorkflowLink.Description === numeroOperacion);
    return resultado ? resultado.ID : null;
  };

  async function getTareaActual(lista: string, title: any, userId: any): Promise<any> {
    try {
      const filtro = "AssignedTo eq " + userId + " and (Status eq 'No iniciada' or Status eq 'En curso')";
      const url = `${getUrlSeguimientoFactura()}/_api/web/lists/GetByTitle('${lista}')/items?$filter=${filtro}&@target=${getUrlSeguimientoFactura()}`;

      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Accept": "application/json;odata=verbose",
          "Content-Type": "application/json;odata=verbose"
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      const items = data.d;
      const array = items.results;
      console.log("data:", data);
      const idTareaObtenida = obtenerID(numeroOperacion, array);
      return idTareaObtenida;
    } catch (error) {
      console.log("Hubo un error al obtener los detalles", error);
      throw error;
    }
  }

  const invocarDelegacionTarea = async (operacion: any, usuarioActual: any, usuarioADelegar: any) => {
    if (!operacion || !usuarioActual || !usuarioADelegar) {
      abrirModalValidacionCompletarTodosLosCampos();
      setLoading(false);
      return false;
    }

    if (usuarioActual === usuarioADelegar) {
      abrirModalValidacionNoSePuedeDelegar();
      setLoading(false);
      return false;
    }

    try {
      const usuarioA: any = await obtenerUsuario(usuarioActual);
      let respuestaEstadoUsuario: any = localStorage.getItem("respuestaEstadoUsuario");
      console.log("respuestaEstadoUsuario:", respuestaEstadoUsuario);
      let respuestaIdUsuario: any = localStorage.getItem("respuestaIdUsuario");
      console.log("respuestaIdUsuario:", respuestaIdUsuario);
      const usuarioD: any = await obtenerUsuario(usuarioADelegar);
      console.log("usuarioD:", usuarioD);
      
      const abrirModalValidacionNoPoseeTarea = () => { setModalValidacionFallida(true); setMensajeValidacion(`El usuario ${usuarioActual} no posee tarea con el número de operación ${numeroOperacion}.`); };
      if (usuarioA.status === Estados.estadoExitoso) {
        setLoading(true);
        let tareaId = await getTareaActual("Tareas Seguimiento Facturas", operacion, usuarioA.data.d.Id);
        if (tareaId) {
          let estadoActualOperacion: any = await getAccionesPorEstado(numeroOperacion, "ESTPEN");
          console.log("estadoActualOperacion.data[0].codAccion:", estadoActualOperacion.data[0].codAccion);
          if (estadoActualOperacion.data[0].codAccion === "SF0003" ||
            estadoActualOperacion.data[0].codAccion === "SF0013" ||
            estadoActualOperacion.data[0].codAccion === "SF0014") {
            const urlBase = getUrlSPBase() + 'Sistemas/SeguimientoFacturas';
            const nombreLista = 'AutorizantesSeguimiento';
            console.log("usuarioA.data.d.Id:", usuarioA.data.d.Id);
            console.log("numeroOperacion:", numeroOperacion);
            let idRecuperado: any = await obtenerItemsAutorizantes(urlBase, nombreLista, usuarioA.data.d.Id, numeroOperacion);
            console.log("idRecuperado:", idRecuperado);
            console.log("operacion:", operacion);
            console.log("usuarioD.data.d.Id:", usuarioD.data.d.Id);
            await actualizarAutorizante(idRecuperado, operacion, usuarioD.data.d.Id);
          }

          await delegarDe(tareaId, usuarioADelegar, "Tareas Seguimiento Facturas");
          setLoading(false);
          cerrarModal();
        } else {
          abrirModalValidacionNoPoseeTarea();
          setLoading(false);
          return false;
        }
      }
    } catch (error) {
      console.error("Error al obtener usuario:", error);
    }
  }

  const handleChangeNroOperacion = (e: any) => {
    setNumeroOperacion(e.target.value);
  }

  const handleChangeDelegarDe = (e: any) => {
    setDelegarde(e.target.value);
  }

  const handleChangeAsignarA = (e: any) => {
    setAsignarA(e.target.value);
  }

  useEffect(() => {
    abrirModal();
  }, []);

  return (
    <div>
      <Modal show={show} id="modalReinicio" className='modal modalReinicio' aria-labelledby="contained-modal-title-vcenter" centered>
        <Modal.Header className="d-flex justify-content-between">
          <Modal.Title>Delegación de Tareas</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div>
            <Form.Label>Número de operación</Form.Label>
            <Form.Control type="number" className='form-control' name="numeroOperacion" placeholder="Número de operación" onChange={handleChangeNroOperacion} />
          </div>
          <div className='mt-3'>
            <Form.Label>Delegar de</Form.Label>
            <Form.Control type="text" className='form-control' name="delegarde" placeholder="Ingrese usuario dominio" onChange={handleChangeDelegarDe} />
          </div>
          <div className='mt-3'>
            <Form.Label>Asignar a</Form.Label>
            <Form.Control type="text" className='form-control' name="asignarA" placeholder="Ingrese usuario dominio a delegar" onChange={handleChangeAsignarA} />
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="primary" onClick={() => invocarDelegacionTarea(numeroOperacion, delegarde, asignarA)}>Delegar <span>{loading ? <LoaderSmall /> : ""}</span></Button>
          <Button variant="secondary" onClick={cerrarModal}>Cancelar</Button>
        </Modal.Footer>
      </Modal>
      {<ModalValidacionDelegacionDeTareasFallida show={modalValidacionFallida} onClose={cerrarModalValidacionFallida} message={mensajeValidacion} />}
    </div>
  )
}

export default BtnDelegacionDeTareas