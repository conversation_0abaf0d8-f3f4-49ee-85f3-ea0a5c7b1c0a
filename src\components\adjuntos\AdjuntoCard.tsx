/// <reference types="webpack/module" />

import React from "react";
import { DocumentoAdjuntoResponse } from "../../comm/contracts/DocumentoAdjuntoResponse";
import { bpmprocesosParams } from "../../entities/Objects/bpmprocesosParams";
import { BtnBajarAdjunto } from "./BtnBajarAdjunto";

("use strict");

export const AdjuntoCard = (unAdjunto: DocumentoAdjuntoResponse) => {
  const tipoDocumento = bpmprocesosParams.tipo_documentos.find(
    (element) => (element.doc_id = unAdjunto.idTipoDocumento.toString())
  ).descripcion;
  const _btn = <BtnBajarAdjunto adjunto={unAdjunto} />;

  return (
    <React.StrictMode>
      <div className="card-documentos border-fun-blue">
        <div className="file-info d-flex align-items-center">
          <div className="mr-3">
            <i className="fa fa-file-pdf fa-2x"></i>
          </div>
          <div className="text">
            <p className="card-documentos__title">
              {unAdjunto.descripcionCarpeta}
            </p>
            <p className="mb-0">{tipoDocumento}</p>
          </div>
        </div>{" "}
        <div>
          <p className="text-muted mb-0">Adjunto</p>
        </div>
        {_btn}
      </div>
    </React.StrictMode>
  );
};
