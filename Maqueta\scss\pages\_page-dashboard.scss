/*
 
 ██████╗  █████╗  ██████╗ ███████╗    ██████╗  █████╗ ███████╗██╗  ██╗██████╗  ██████╗  █████╗ ██████╗ ██████╗ 
 ██╔══██╗██╔══██╗██╔════╝ ██╔════╝    ██╔══██╗██╔══██╗██╔════╝██║  ██║██╔══██╗██╔═══██╗██╔══██╗██╔══██╗██╔══██╗
 ██████╔╝███████║██║  ███╗█████╗█████╗██║  ██║███████║███████╗███████║██████╔╝██║   ██║███████║██████╔╝██║  ██║
 ██╔═══╝ ██╔══██║██║   ██║██╔══╝╚════╝██║  ██║██╔══██║╚════██║██╔══██║██╔══██╗██║   ██║██╔══██║██╔══██╗██║  ██║
 ██║     ██║  ██║╚██████╔╝███████╗    ██████╔╝██║  ██║███████║██║  ██║██████╔╝╚██████╔╝██║  ██║██║  ██║██████╔╝
 ╚═╝     ╚═╝  ╚═╝ ╚═════╝ ╚══════╝    ╚═════╝ ╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝╚═════╝  ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═╝╚═════╝ 
 - Estilos para el dashboard                                                                                                              
 
*/

// SliderCuentas
/////////////////////////////////////////////////

@include block('lateral-g') {
    
    @include modifier('right') {
        
        &:before {
            content: "";
            display: block;
            position: absolute;
            right: 0;
            top: 0;
            height: 100%;
            width: 40px;
            background: -webkit-gradient(linear, left top, right top, color-stop(0, rgba(255,255,255,0)), to(#fff));
            background: linear-gradient(to right, rgba(255,255,255,0) 0, #fff 100%);
            z-index: 3;

        }

    }    

}//endSwiperContendor




