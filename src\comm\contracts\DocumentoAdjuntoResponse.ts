export type DocumentoAdjuntoResponse =
  {
    idDocumentos: number,
    idTipoDocumento: number,
    rutaDocumento: string,
    fechaRegistro: Date,
    codigoUsuario: string,
    codigoSucursalUsuario: string,
    codigoDepartamento: number,
    fechaDocumento: Date,
    fechaVencimiento: Date,
    periodo: string,
    descripcionCarpeta: string,
    codigoGrupo: number,
    codigoCliente: string,
    idLegajo: number,
    idProducto: number,
    idSubProducto: number,
    esInterno: string,
    fechaCierreLegajo: Date,
    esTemporal: string,
    operacion: string
  }

export function isDocumentoAdjuntoResponse(DocumentoAdjuntoResponse: any): DocumentoAdjuntoResponse is DocumentoAdjuntoResponse[] {
  return (DocumentoAdjuntoResponse as DocumentoAdjuntoResponse[]).length > 0;
}