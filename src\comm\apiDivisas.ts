import axios from "axios"
import { getAmbienteContexto, getUrlBase } from "../utilities/contextInfo"
import EndPoint from "./EndPoint.Config"
import { getApiProveedorFacturaHeaders } from "./contracts/ApiBpmPoliticasProcesosRequestHeaders"
import { Estados } from "../entities/Enums/estadosRespuesta"

const Params = {
  urlBase: getUrlBase(),
  ambiente: getAmbienteContexto(),
  request: axios.create({}),
  endpoints: EndPoint.apiDivisas
}

export const getCotizacion = async (idMoneda: any) => {
  console.log("ENTRE EN GET COTIZACION.");
  let _cacheControl = 'max-age=60*60*10, private';
  let _headers = await getApiProveedorFacturaHeaders(_cacheControl);

  let _sturlEndpoint = Params.endpoints.vUno.get.cotizacionId;

  let config = {
    method: 'get',
    url: Params.urlBase + _sturlEndpoint + idMoneda,
    headers: _headers
  }
  console.log("config getCotizacion:", config);

  let promise = Params.request<any, any>(config)
    .then(function (response) {
      if (response.status === Estados.estadoExitoso) {
        console.log("response getCotizacion:", response);
        return response;
      }
    }).catch(function (error) {
      console.log("error catch getCotizacion:", error);
    })
  let result = await promise;
  return result;
}

