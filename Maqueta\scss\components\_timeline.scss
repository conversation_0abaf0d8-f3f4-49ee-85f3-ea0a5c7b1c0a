#timeline {
    margin: 20px auto -10px;
    position: relative;
    display: inline-flex;
   
    .dot {
      z-index: 99;
      transition : 0.3s ease-in-out;
      width: auto;
      height: 20px;
      //position: absolute;
      //top: -9px;
      //position: relative;
      text-align: center;
      cursor: pointer;
      &:nth-child(1){
        left: -10%;
      }
      &:nth-child(2){
        left: 15%;
      }
      &:nth-child(3){
        left: 40%;
      }
      &:nth-child(4){
        left: 65%;
      }
      &:nth-child(5){
        left: 90%;
      }
      &:hover {
        transform: scale(1.1);
        text-decoration: none;
      }

      p {
        text-transform: uppercase;
        font-size: .7rem;
        display: block;
        position: relative;
        text-align: center;
      }
      span {
        display: inline-block;
        margin-top: 2px;
        width: 15px;
        height: 15px;
        background-color: $white;
        position: relative;
        border-radius: 50%;
        border: 2px solid $hippie-blue;
      }

      &.active{
        span{
          border-color: $key-lime-pie;
          background-color: $key-lime-pie;
        }
        p{
            font-weight: bold;
        }
      }

      &.complete{
        span{
          border-color: $fun-blue;
          background-color: $fun-blue;
        }
        p{
            //font-weight: bold;
        }
      }
    }
  }

.progress{
    width: 86%;
    margin: 0 auto;
}

@media only screen and (min-width:861px) and (max-width:991px) {
    .progress{
        width: 86%;
    }
}

@media only screen and (min-width:769px) and (max-width:860px) {
    .progress{
        width: 80%;
    }
}

@media only screen and (min-width:411px) and (max-width:768px) {
    #timeline{
        .dot{
            width:auto;
            p{
               display: none;
            }
        }
    }

    .progress{
        width: 100%;
    }
}
  
@media only screen and (min-width:240px) and (max-width:410px) {
    #timeline{
        .dot{
            width:auto;
            p{
                display: none;
            }
        }
    }

    .progress{
        width: 100%;
    }
}

