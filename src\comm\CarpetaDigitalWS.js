import { xml2Json3 } from "../utilities/Parsers";

export function armarXmlDocument(id) {
  var validacion = ";"
  for (var i = 0; i < document.getElementById(id).files.length; ++i) {
    var esUltimo = document.getElementById(id);
    if (i == document.getElementById(id).files.length - 1) {
      esUltimo = true;

    }
    var extencion = obtenerExtension(document.getElementById(id).files[i].name);
    if (extencion == "pdf" || extencion == "PDF") {
      insertFile(document.getElementById(id).files[i], esUltimo);
    } else {
      validacion = validacion + document.getElementById(id).files[i].name + ";";
    }
    if (esUltimo == true && validacion != ";") {
      console.log(validacion);
      alert("Sólo se admiten documentos PDF : " + validacion);

    } else {
      if (esUltimo == true && validacion == ";") {
        alert("Archivos adjuntados exitosamente.");
      }
    }
  }
}
export function insertFile(file, esUltimo) {
  var trueDate = new Date();
  var fecha = trueDate.getDate() + trueDate.getMonth() + trueDate.getFullYear();
  var path = "digitalizacion_documentos\\" + datosOperacionSeleccionada[0].Codigo_x0020_cliente + "\\" + armarFechaCarpeta() + "\\" + file.name;
  var formData = new FormData();

  console.log("path", path);


  formData.append("file", file);
  formData.append("path", path);
  formData.append("usuario", currentUser);
  formData.append("noise", "");
  formData.append("stringResult", "");

  var data = {
    path: path,
    file: file
  }
  var url = getUrlWebService() + "api/nintex/uploadFileNintex";
  XMLHttpfunctionFormData(url, "POST", formData, insertIntoCarpetaDogital, onErrorService, data);

  if (esUltimo == true) {
    document.getElementById("loaderCarpetaDigital").style.display = "block";
    limpiarSector("accordionExample");
    gruposCarpetaDigital = "";
    if (datosOperacionSeleccionada[0].EsBase == "SI") {
      if (datosOperacionSeleccionada[0].Codigo_x0020_aplicacion == 'undefined') {
        datosOperacionSeleccionada[0].Codigo_x0020_aplicacion = getAplicaOP(datosOperacionSeleccionada[0].Title);
      }
      armarCarpetaDigital(datosOperacionSeleccionada[0].Codigo_x0020_cliente, "CRD", "DESCUENTO DE CHEQUES", datosOperacionSeleccionada[0].Codigo_x0020_aplicacion, datosOperacionSeleccionada[0].Title, currentUser, metodoCarpetaDigital);
      getArchivosAdjuntos();
      setTimeout(function () {

        document.getElementById("loaderCarpetaDigital").style.display = "none";
      }, 2000)
      return;
    }
    armarCarpetaDigital(datosOperacionSeleccionada[0].Codigo_x0020_cliente, datosOperacionSeleccionada[0].Codigo_x0020_producto, datosOperacionSeleccionada[0].Sub_x0020_producto, datosOperacionSeleccionada[0].Codigo_x0020_aplicacion, datosOperacionSeleccionada[0].Title, currentUser, metodoCarpetaDigital);
    getArchivosAdjuntos();
    setTimeout(function () {

      document.getElementById("loaderCarpetaDigital").style.display = "none";
    }, 2000)
  }

}


export function insertIntoCarpetaDigital(xml, data) {

  var jsonResponse = xml2Json3(xml);
  respCodigo = jsonResponse;
  console.log("code", jsonResponse.Respuesta.respCod["#text"]);
  if (jsonResponse.Respuesta.respCod["#text"] == "0") {
    var trueDate = new Date();
    var d = trueDate.getMonth();
    if (d == 0) {
      d = d + 1;
    }
    var url = getUrlWebService() + "api/nintex/PostDigitalizarDocumento";

    console.log("data carpetaDigitalWS:", data);

    var mensaje = '<?xml version ="1.0" encoding="UTF-8"?>' +
      '<Creditos.ArchivoCP>' +
      '<codigoDocumento>' + data.codigoDocumento + '</codigoDocumento>' +
      '<codRespuesta>' + data.codRespuesta + '</codRespuesta>' +
      '<pi_path>' + data.path + '</pi_path>' +
      '<pi_tipodoc>' + getCodDocumentos() + '</pi_tipodoc>' +
      '<pi_rutaDoc>' + data.pi_rutaDoc + '</pi_rutaDoc>' +
      '<pi_fechaDoc>' + data.pi_fechaDoc + '</pi_fechaDoc>' +
      '<pi_descriCarp>' + data.file.name + '</pi_descriCarp>' +
      '<pi_cliente>' + datosOperacionSeleccionada[0].Codigo_x0020_cliente + '</pi_cliente>' +
      '<pi_producto>' + data.pi_producto + '</pi_producto>' +
      '<pi_subproducto>' + data.pi_subproducto + '</pi_subproducto>' +
      '<pi_temporal>' + data.pi_temporal + '</pi_temporal>' +
      '<pi_comentario>' + data.pi_comentario + '</pi_comentario>' +
      '<pi_cantidad_oper>' + data.pi_cantidad_oper + '</pi_cantidad_oper>' +
      '<pi_oper>' + datosOperacionSeleccionada[0].Title + '</pi_oper>' +
      '<pi_user>' + userTresLetras + '</pi_user>' +
      '</Creditos.ArchivoCP>'
    console.log("xml insert", mensaje);
    XMLHttpfunction(url, "POST", mensaje, successInsertCarpetaDigital, onErrorService, true);
  } else {
    alert("ERROR EN LA INSERCION");
    console.log("respuesta", jsonResponse);
  }


}

function armarFechaCarpeta() {
  var trueDate = new Date();
  var dia, mes, año;
  if (trueDate.getDate().toString().length == 1) {
    dia = "0" + trueDate.getDate();
  } else {
    dia = trueDate.getDate();
  }

  mes = trueDate.getMonth() + 1;
  año = trueDate.getFullYear();

  var fecha = dia + "" + mes + "" + año;


  return fecha;
}

export function OnSuccessCarpetaDigital(data, tituloArchivo) {
  let binaryString = data;
  let fileExtension = tituloArchivo.split('.').pop().toLowerCase();
  let mimeType;

  console.log("DATA:", data);
  console.log("tituloArchivo:", tituloArchivo);

  switch (fileExtension) {
    case 'pdf':
      mimeType = 'application/pdf';
      break;
    case 'doc':
    case 'docx':
      mimeType = 'application/msword';
      break;
    case 'xls':
    case 'xlsx':
      mimeType = 'application/vnd.ms-excel';
      break;
    case 'jpg':
    case 'jpeg':
      mimeType = 'image/jpeg';
      break;
    case 'odt':
      mimeType = 'application/vnd.oasis.opendocument.text';
      break;
    default:
      mimeType = 'application/pdf';
      break;
  }

  if (window.navigator && window.navigator.msSaveOrOpenBlob) {
    let byteCharacters = window.atob(binaryString);
    let byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    let byteArray = new Uint8Array(byteNumbers);
    let blob = new Blob([byteArray], {
      type: mimeType
    });
    window.navigator.msSaveOrOpenBlob(blob, tituloArchivo);
  } else {
    let byteCharacters = window.atob(binaryString);
    let byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    let byteArray = new Uint8Array(byteNumbers);
    let blob = new Blob([byteArray], {
      type: mimeType
    });

    let link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = tituloArchivo;
    link.click();
  }
}