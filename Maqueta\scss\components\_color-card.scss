/*
 ██████╗ ██████╗ ██╗      ██████╗ ██████╗      ██████╗ █████╗ ██████╗ ██████╗
██╔════╝██╔═══██╗██║     ██╔═══██╗██╔══██╗    ██╔════╝██╔══██╗██╔══██╗██╔══██╗
██║     ██║   ██║██║     ██║   ██║██████╔╝    ██║     ███████║██████╔╝██║  ██║
██║     ██║   ██║██║     ██║   ██║██╔══██╗    ██║     ██╔══██║██╔══██╗██║  ██║
╚██████╗╚██████╔╝███████╗╚██████╔╝██║  ██║    ╚██████╗██║  ██║██║  ██║██████╔╝
 ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚═╝  ╚═╝     ╚═════╝╚═╝  ╚═╝╚═╝  ╚═╝╚═════╝

*/

@include block('color-card'){
    background-color: $gray-100;
    border-radius: $border-radius;
    
    @include element('body'){
        padding: 1.2rem;
        border-radius: $border-radius;
    }
    @include element('header'){
        height: 190px;
        background-color: $gray-200;
        border-radius: $border-radius;
        padding: 1.2rem;
    }
}

@include block('contrast-checker'){
    text-align: center;

    p{
        margin-bottom: 0;
    }
    
    span{
        background-color: $gray-700;
        color: $white;
        font-size: $font-size-sm;
        text-transform: uppercase;
        padding: .2rem .3rem;
        border-radius: $border-radius;
    }
}