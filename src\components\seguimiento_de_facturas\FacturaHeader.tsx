/// <reference types="webpack/module" />

import React, { useContext } from "react";
import FacturaFormContext from "../contextProvider/counterContext";
import { ProveedorBuscador } from "../proveedores/ProveedorBuscador";
import { FacturaEmisor } from "./FacturaEmisor";
import { FacturaHeaderMData } from "./FacturaHeaderMData";
import { FacturaReceptor } from "./FacturaReceptor";
import { useProveedor } from "../contextProvider/datosProveedorContext";

export const FacturaHeader = () => {
  const { state, dispatch } = useProveedor();

  const _FacturaFormContext = useContext(FacturaFormContext)
  const _readOnly = _FacturaFormContext.onlyRead
  console.log("_readOnly:", _readOnly);
  const _ProveedorBuscadorJsx = (_readOnly) ? <></> : <ProveedorBuscador handler={dispatch} {...state} />

  return (
    <div className="invoice-header d-flex justify-content-center row">
      {_ProveedorBuscadorJsx}
      <div className="col">
        <FacturaEmisor />
      </div>
      <div className="col">
        <FacturaReceptor />
      </div>
      <div className="col">
        <FacturaHeaderMData />
      </div>
    </div>
  )
}