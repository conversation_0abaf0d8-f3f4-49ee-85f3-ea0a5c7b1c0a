/*
 
  ██████╗██╗   ██╗███████╗████████╗ ██████╗ ███╗   ███╗    ██╗   ██╗ █████╗ ██████╗ ██╗ █████╗ ██████╗ ██╗     ███████╗███████╗
 ██╔════╝██║   ██║██╔════╝╚══██╔══╝██╔═══██╗████╗ ████║    ██║   ██║██╔══██╗██╔══██╗██║██╔══██╗██╔══██╗██║     ██╔════╝██╔════╝
 ██║     ██║   ██║███████╗   ██║   ██║   ██║██╔████╔██║    ██║   ██║███████║██████╔╝██║███████║██████╔╝██║     █████╗  ███████╗
 ██║     ██║   ██║╚════██║   ██║   ██║   ██║██║╚██╔╝██║    ╚██╗ ██╔╝██╔══██║██╔══██╗██║██╔══██║██╔══██╗██║     ██╔══╝  ╚════██║
 ╚██████╗╚██████╔╝███████║   ██║   ╚██████╔╝██║ ╚═╝ ██║     ╚████╔╝ ██║  ██║██║  ██║██║██║  ██║██████╔╝███████╗███████╗███████║
  ╚═════╝ ╚═════╝ ╚══════╝   ╚═╝    ╚═════╝ ╚═╝     ╚═╝      ╚═══╝  ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚═╝  ╚═╝╚═════╝ ╚══════╝╚══════╝╚══════╝

*/

// Color Design Systems
/////////////////////////////////////////////////
$fun-blue: #1D428A;
$key-lime-pie: #BECD42;
$hippie-blue: #5B9FAA;
$yellow-sea: #FCAC00;
$milano-red: #C3250A;


$white:    #fff !default;
$gray-100: #F5F5F6 !default;
$gray-200: #ECEEEF !default;
$gray-300: #b7b7bb !default;
$gray-400: #8F8F94 !default;
$gray-500: #6B6C71 !default;
$gray-600: #4E4F55 !default;
$gray-700: #3A3F46 !default;
$gray-800: #23252A !default;
$gray-900: #14171A !default;
$black:    #151515 !default;

$grays: () !default;
// stylelint-disable-next-line scss/dollar-variable-default
$grays: map-merge(
  (
    "100": $gray-100,
    "200": $gray-200,
    "300": $gray-300,
    "400": $gray-400,
    "500": $gray-500,
    "600": $gray-600,
    "700": $gray-700,
    "800": $gray-800,
    "900": $gray-900
  ),
  $grays
);


$blue:    $fun-blue; //sistema
$red:     $milano-red; //sistema
$yellow:  $yellow-sea; //sistema
$green:   $key-lime-pie !default;
$cyan:    $hippie-blue; //sistema

$colors: () !default;
// stylelint-disable-next-line scss/dollar-variable-default
$colors: map-merge(
  (
    "blue":       $blue,
    "red":        $red,
    "yellow":     $yellow,
    "green":      $green,
    "cyan":       $cyan,
    "white":      $white,
    "gray":       $gray-600,
    "gray-dark":  $gray-800
  ),
  $colors
);

$primary:       $blue !default;
$secondary:     $gray-600 !default;
$success:       $green !default;
$info:          $cyan !default;
$warning:       $yellow !default;
$danger:        $red !default;
$light:         $gray-100 !default;
$dark:          $gray-800 !default;

$theme-colors: () !default;
// stylelint-disable-next-line scss/dollar-variable-default
$theme-colors: map-merge(
  (
    "primary":    red,
    "secondary":  $secondary,
    "success":    $success,
    "info":       $info,
    "warning":    $warning,
    "danger":     $danger,
    "light":      $light,
    "dark":       $dark,
    "fun-blue":   $fun-blue,
    "key-lime":   $key-lime-pie,
    "hippie-blue":$hippie-blue,
    "yellow-sea": $yellow-sea,
    "milano-red": $milano-red
  ),
  $theme-colors
);

$headings-color: $gray-800;
$link-color:     $blue;

// Font Family & Weight
$font-family-sans-serif:      "ContiSans", Roboto, "Helvetica Neue", Arial;
$font-family-base:            $font-family-sans-serif;
$headings-font-weight:        bold;
$enable-responsive-font-sizes: true;

// Formulario
$custom-control-indicator-checked-bg: $blue;
$custom-control-indicator-checked-border-color: $blue;

// Inputs & Buttons
$input-btn-focus-box-shadow:  0;
$input-bg: $gray-200;
$input-color:                           $gray-700;
$input-border-color:                    $gray-200;
$input-box-shadow:                      inset 0 1px 1px rgba($black, .075) !default;
$input-focus-border-color:              $blue;
$input-focus-box-shadow:                $input-btn-focus-box-shadow;
$btn-focus-box-shadow:                  $input-btn-focus-box-shadow;

$input-group-addon-bg:                  $gray-300 !important;
.input-group-text {
  background-color: $input-group-addon-bg;
  border: none !important;
}

// Nav pills
$nav-pills-link-active-bg:          $fun-blue;

// Modal
$modal-backdrop-bg:                 $white !important;
$modal-backdrop-opacity:            .75 !important;
$modal-header-border-color:         none !important;
$modal-footer-border-color:         none !important;

