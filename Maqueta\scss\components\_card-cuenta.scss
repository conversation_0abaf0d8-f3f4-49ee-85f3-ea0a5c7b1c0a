/*
 
  ██████╗ █████╗ ██████╗ ██████╗        ██████╗██╗   ██╗███████╗███╗   ██╗████████╗ █████╗ ███████╗
 ██╔════╝██╔══██╗██╔══██╗██╔══██╗      ██╔════╝██║   ██║██╔════╝████╗  ██║╚══██╔══╝██╔══██╗██╔════╝
 ██║     ███████║██████╔╝██║  ██║█████╗██║     ██║   ██║█████╗  ██╔██╗ ██║   ██║   ███████║███████╗
 ██║     ██╔══██║██╔══██╗██║  ██║╚════╝██║     ██║   ██║██╔══╝  ██║╚██╗██║   ██║   ██╔══██║╚════██║
 ╚██████╗██║  ██║██║  ██║██████╔╝      ╚██████╗╚██████╔╝███████╗██║ ╚████║   ██║   ██║  ██║███████║
  ╚═════╝╚═╝  ╚═╝╚═╝  ╚═╝╚═════╝        ╚═════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝   ╚═╝   ╚═╝  ╚═╝╚══════╝
  Estilos propios para card-cuentas                                                                                                 
 
*/

@include block('card-cuenta') {
    padding: $spacer;
    cursor: pointer;
    border-radius: 4px;
    transform: scale(0.9);
    background-color: $gray-150;
    transition:transform .2s ease-in;


    @include modifier('active') {
          transform: scale(1);
          border-bottom: 4px solid $blue;
          box-shadow: $box-shadow-sm;
    }
    
}

