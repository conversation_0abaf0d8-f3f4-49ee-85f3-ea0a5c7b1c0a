/*
███████╗ ██████╗ ██╗     ██╗ ██████╗██╗████████╗██╗   ██╗██████╗
██╔════╝██╔═══██╗██║     ██║██╔════╝██║╚══██╔══╝██║   ██║██╔══██╗
███████╗██║   ██║██║     ██║██║     ██║   ██║   ██║   ██║██║  ██║
╚════██║██║   ██║██║     ██║██║     ██║   ██║   ██║   ██║██║  ██║
███████║╚██████╔╝███████╗██║╚██████╗██║   ██║   ╚██████╔╝██████╔╝
╚══════╝ ╚═════╝ ╚══════╝╚═╝ ╚═════╝╚═╝   ╚═╝    ╚═════╝ ╚═════╝

*/ 
 

// Archivos Base Bootstrap
@import "bootstrap/functions";
@import "bootstrap/variables";
@import "bootstrap/mixins";

// Base & Vendor
// Fonts utilizados - Librerias
@import "base/body";
@import "base/font-face";
@import "base/icons";
@import "vendor/bemify/bemify";

// Variables propias sobreescribe el reboot
@import "bootstrap/custom-variables";
@import "bootstrap/reboot";

// Componentes bootstrap que utilizamos
@import "bootstrap/type";
@import "bootstrap/images";
@import "bootstrap/forms";
@import "bootstrap/custom-forms";
@import "bootstrap/buttons";
@import "bootstrap/grid";
@import "bootstrap/modal";
@import "bootstrap/close";
@import "bootstrap/card";
@import "bootstrap/transitions";
@import "bootstrap/dropdown";
@import "bootstrap/input-group";
@import "bootstrap/tables";
@import "bootstrap/spinners";
@import "bootstrap/badge";
@import "bootstrap/tooltip";

// SelectPicker
@import "base/select-picker-form-control";
@import "base/btn-input-file";

// Utilities
@import "bootstrap/utilities";

// Page & Layout
@import "pages/page-onboarding";
@import "pages/manifestacion";
@import "layout/header-secundario";
@import "layout/footer";

// Componentes con mixins propios
@import "components/labels";
@import "components/avatar";
@import "components/custom-modal";