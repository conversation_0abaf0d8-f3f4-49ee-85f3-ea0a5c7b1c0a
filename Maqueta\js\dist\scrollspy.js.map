{"version": 3, "file": "scrollspy.js", "sources": ["../src/scrollspy.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.4.1): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME               = 'scrollspy'\nconst VERSION            = '4.4.1'\nconst DATA_KEY           = 'bs.scrollspy'\nconst EVENT_KEY          = `.${DATA_KEY}`\nconst DATA_API_KEY       = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst Default = {\n  offset : 10,\n  method : 'auto',\n  target : ''\n}\n\nconst DefaultType = {\n  offset : 'number',\n  method : 'string',\n  target : '(string|element)'\n}\n\nconst Event = {\n  ACTIVATE      : `activate${EVENT_KEY}`,\n  SCROLL        : `scroll${EVENT_KEY}`,\n  LOAD_DATA_API : `load${EVENT_KEY}${DATA_API_KEY}`\n}\n\nconst ClassName = {\n  DROPDOWN_ITEM : 'dropdown-item',\n  DROPDOWN_MENU : 'dropdown-menu',\n  ACTIVE        : 'active'\n}\n\nconst Selector = {\n  DATA_SPY        : '[data-spy=\"scroll\"]',\n  ACTIVE          : '.active',\n  NAV_LIST_GROUP  : '.nav, .list-group',\n  NAV_LINKS       : '.nav-link',\n  NAV_ITEMS       : '.nav-item',\n  LIST_ITEMS      : '.list-group-item',\n  DROPDOWN        : '.dropdown',\n  DROPDOWN_ITEMS  : '.dropdown-item',\n  DROPDOWN_TOGGLE : '.dropdown-toggle'\n}\n\nconst OffsetMethod = {\n  OFFSET   : 'offset',\n  POSITION : 'position'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass ScrollSpy {\n  constructor(element, config) {\n    this._element       = element\n    this._scrollElement = element.tagName === 'BODY' ? window : element\n    this._config        = this._getConfig(config)\n    this._selector      = `${this._config.target} ${Selector.NAV_LINKS},` +\n                          `${this._config.target} ${Selector.LIST_ITEMS},` +\n                          `${this._config.target} ${Selector.DROPDOWN_ITEMS}`\n    this._offsets       = []\n    this._targets       = []\n    this._activeTarget  = null\n    this._scrollHeight  = 0\n\n    $(this._scrollElement).on(Event.SCROLL, (event) => this._process(event))\n\n    this.refresh()\n    this._process()\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  refresh() {\n    const autoMethod = this._scrollElement === this._scrollElement.window\n      ? OffsetMethod.OFFSET : OffsetMethod.POSITION\n\n    const offsetMethod = this._config.method === 'auto'\n      ? autoMethod : this._config.method\n\n    const offsetBase = offsetMethod === OffsetMethod.POSITION\n      ? this._getScrollTop() : 0\n\n    this._offsets = []\n    this._targets = []\n\n    this._scrollHeight = this._getScrollHeight()\n\n    const targets = [].slice.call(document.querySelectorAll(this._selector))\n\n    targets\n      .map((element) => {\n        let target\n        const targetSelector = Util.getSelectorFromElement(element)\n\n        if (targetSelector) {\n          target = document.querySelector(targetSelector)\n        }\n\n        if (target) {\n          const targetBCR = target.getBoundingClientRect()\n          if (targetBCR.width || targetBCR.height) {\n            // TODO (fat): remove sketch reliance on jQuery position/offset\n            return [\n              $(target)[offsetMethod]().top + offsetBase,\n              targetSelector\n            ]\n          }\n        }\n        return null\n      })\n      .filter((item) => item)\n      .sort((a, b) => a[0] - b[0])\n      .forEach((item) => {\n        this._offsets.push(item[0])\n        this._targets.push(item[1])\n      })\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    $(this._scrollElement).off(EVENT_KEY)\n\n    this._element       = null\n    this._scrollElement = null\n    this._config        = null\n    this._selector      = null\n    this._offsets       = null\n    this._targets       = null\n    this._activeTarget  = null\n    this._scrollHeight  = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    if (typeof config.target !== 'string') {\n      let id = $(config.target).attr('id')\n      if (!id) {\n        id = Util.getUID(NAME)\n        $(config.target).attr('id', id)\n      }\n      config.target = `#${id}`\n    }\n\n    Util.typeCheckConfig(NAME, config, DefaultType)\n\n    return config\n  }\n\n  _getScrollTop() {\n    return this._scrollElement === window\n      ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop\n  }\n\n  _getScrollHeight() {\n    return this._scrollElement.scrollHeight || Math.max(\n      document.body.scrollHeight,\n      document.documentElement.scrollHeight\n    )\n  }\n\n  _getOffsetHeight() {\n    return this._scrollElement === window\n      ? window.innerHeight : this._scrollElement.getBoundingClientRect().height\n  }\n\n  _process() {\n    const scrollTop    = this._getScrollTop() + this._config.offset\n    const scrollHeight = this._getScrollHeight()\n    const maxScroll    = this._config.offset +\n      scrollHeight -\n      this._getOffsetHeight()\n\n    if (this._scrollHeight !== scrollHeight) {\n      this.refresh()\n    }\n\n    if (scrollTop >= maxScroll) {\n      const target = this._targets[this._targets.length - 1]\n\n      if (this._activeTarget !== target) {\n        this._activate(target)\n      }\n      return\n    }\n\n    if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n      this._activeTarget = null\n      this._clear()\n      return\n    }\n\n    const offsetLength = this._offsets.length\n    for (let i = offsetLength; i--;) {\n      const isActiveTarget = this._activeTarget !== this._targets[i] &&\n          scrollTop >= this._offsets[i] &&\n          (typeof this._offsets[i + 1] === 'undefined' ||\n              scrollTop < this._offsets[i + 1])\n\n      if (isActiveTarget) {\n        this._activate(this._targets[i])\n      }\n    }\n  }\n\n  _activate(target) {\n    this._activeTarget = target\n\n    this._clear()\n\n    const queries = this._selector\n      .split(',')\n      .map((selector) => `${selector}[data-target=\"${target}\"],${selector}[href=\"${target}\"]`)\n\n    const $link = $([].slice.call(document.querySelectorAll(queries.join(','))))\n\n    if ($link.hasClass(ClassName.DROPDOWN_ITEM)) {\n      $link.closest(Selector.DROPDOWN).find(Selector.DROPDOWN_TOGGLE).addClass(ClassName.ACTIVE)\n      $link.addClass(ClassName.ACTIVE)\n    } else {\n      // Set triggered link as active\n      $link.addClass(ClassName.ACTIVE)\n      // Set triggered links parents as active\n      // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n      $link.parents(Selector.NAV_LIST_GROUP).prev(`${Selector.NAV_LINKS}, ${Selector.LIST_ITEMS}`).addClass(ClassName.ACTIVE)\n      // Handle special case when .nav-link is inside .nav-item\n      $link.parents(Selector.NAV_LIST_GROUP).prev(Selector.NAV_ITEMS).children(Selector.NAV_LINKS).addClass(ClassName.ACTIVE)\n    }\n\n    $(this._scrollElement).trigger(Event.ACTIVATE, {\n      relatedTarget: target\n    })\n  }\n\n  _clear() {\n    [].slice.call(document.querySelectorAll(this._selector))\n      .filter((node) => node.classList.contains(ClassName.ACTIVE))\n      .forEach((node) => node.classList.remove(ClassName.ACTIVE))\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = typeof config === 'object' && config\n\n      if (!data) {\n        data = new ScrollSpy(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(window).on(Event.LOAD_DATA_API, () => {\n  const scrollSpys = [].slice.call(document.querySelectorAll(Selector.DATA_SPY))\n  const scrollSpysLength = scrollSpys.length\n\n  for (let i = scrollSpysLength; i--;) {\n    const $spy = $(scrollSpys[i])\n    ScrollSpy._jQueryInterface.call($spy, $spy.data())\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = ScrollSpy._jQueryInterface\n$.fn[NAME].Constructor = ScrollSpy\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return ScrollSpy._jQueryInterface\n}\n\nexport default ScrollSpy\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "<PERSON><PERSON><PERSON>", "offset", "method", "target", "DefaultType", "Event", "ACTIVATE", "SCROLL", "LOAD_DATA_API", "ClassName", "DROPDOWN_ITEM", "DROPDOWN_MENU", "ACTIVE", "Selector", "DATA_SPY", "NAV_LIST_GROUP", "NAV_LINKS", "NAV_ITEMS", "LIST_ITEMS", "DROPDOWN", "DROPDOWN_ITEMS", "DROPDOWN_TOGGLE", "OffsetMethod", "OFFSET", "POSITION", "ScrollSpy", "element", "config", "_element", "_scrollElement", "tagName", "window", "_config", "_getConfig", "_selector", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "on", "event", "_process", "refresh", "autoMethod", "offsetMethod", "offsetBase", "_getScrollTop", "_getScrollHeight", "targets", "slice", "call", "document", "querySelectorAll", "map", "targetSelector", "<PERSON><PERSON>", "getSelectorFromElement", "querySelector", "targetBCR", "getBoundingClientRect", "width", "height", "top", "filter", "item", "sort", "a", "b", "for<PERSON>ach", "push", "dispose", "removeData", "off", "id", "attr", "getUID", "typeCheckConfig", "pageYOffset", "scrollTop", "scrollHeight", "Math", "max", "body", "documentElement", "_getOffsetHeight", "innerHeight", "maxScroll", "length", "_activate", "_clear", "offsetLength", "i", "isActiveTarget", "queries", "split", "selector", "$link", "join", "hasClass", "closest", "find", "addClass", "parents", "prev", "children", "trigger", "relatedTarget", "node", "classList", "contains", "remove", "_jQueryInterface", "each", "data", "TypeError", "scrollSpys", "scrollSpysLength", "$spy", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAUA;;;;;;EAMA,IAAMA,IAAI,GAAiB,WAA3B;EACA,IAAMC,OAAO,GAAc,OAA3B;EACA,IAAMC,QAAQ,GAAa,cAA3B;EACA,IAAMC,SAAS,SAAgBD,QAA/B;EACA,IAAME,YAAY,GAAS,WAA3B;EACA,IAAMC,kBAAkB,GAAGC,CAAC,CAACC,EAAF,CAAKP,IAAL,CAA3B;EAEA,IAAMQ,OAAO,GAAG;EACdC,EAAAA,MAAM,EAAG,EADK;EAEdC,EAAAA,MAAM,EAAG,MAFK;EAGdC,EAAAA,MAAM,EAAG;EAHK,CAAhB;EAMA,IAAMC,WAAW,GAAG;EAClBH,EAAAA,MAAM,EAAG,QADS;EAElBC,EAAAA,MAAM,EAAG,QAFS;EAGlBC,EAAAA,MAAM,EAAG;EAHS,CAApB;EAMA,IAAME,KAAK,GAAG;EACZC,EAAAA,QAAQ,eAAmBX,SADf;EAEZY,EAAAA,MAAM,aAAmBZ,SAFb;EAGZa,EAAAA,aAAa,WAAUb,SAAV,GAAsBC;EAHvB,CAAd;EAMA,IAAMa,SAAS,GAAG;EAChBC,EAAAA,aAAa,EAAG,eADA;EAEhBC,EAAAA,aAAa,EAAG,eAFA;EAGhBC,EAAAA,MAAM,EAAU;EAHA,CAAlB;EAMA,IAAMC,QAAQ,GAAG;EACfC,EAAAA,QAAQ,EAAU,qBADH;EAEfF,EAAAA,MAAM,EAAY,SAFH;EAGfG,EAAAA,cAAc,EAAI,mBAHH;EAIfC,EAAAA,SAAS,EAAS,WAJH;EAKfC,EAAAA,SAAS,EAAS,WALH;EAMfC,EAAAA,UAAU,EAAQ,kBANH;EAOfC,EAAAA,QAAQ,EAAU,WAPH;EAQfC,EAAAA,cAAc,EAAI,gBARH;EASfC,EAAAA,eAAe,EAAG;EATH,CAAjB;EAYA,IAAMC,YAAY,GAAG;EACnBC,EAAAA,MAAM,EAAK,QADQ;EAEnBC,EAAAA,QAAQ,EAAG;EAFQ,CAArB;EAKA;;;;;;MAMMC;;;EACJ,qBAAYC,OAAZ,EAAqBC,MAArB,EAA6B;EAAA;;EAC3B,SAAKC,QAAL,GAAsBF,OAAtB;EACA,SAAKG,cAAL,GAAsBH,OAAO,CAACI,OAAR,KAAoB,MAApB,GAA6BC,MAA7B,GAAsCL,OAA5D;EACA,SAAKM,OAAL,GAAsB,KAAKC,UAAL,CAAgBN,MAAhB,CAAtB;EACA,SAAKO,SAAL,GAAyB,KAAKF,OAAL,CAAa7B,MAAhB,SAA0BU,QAAQ,CAACG,SAAnC,UACG,KAAKgB,OAAL,CAAa7B,MADhB,SAC0BU,QAAQ,CAACK,UADnC,WAEG,KAAKc,OAAL,CAAa7B,MAFhB,SAE0BU,QAAQ,CAACO,cAFnC,CAAtB;EAGA,SAAKe,QAAL,GAAsB,EAAtB;EACA,SAAKC,QAAL,GAAsB,EAAtB;EACA,SAAKC,aAAL,GAAsB,IAAtB;EACA,SAAKC,aAAL,GAAsB,CAAtB;EAEAxC,IAAAA,CAAC,CAAC,KAAK+B,cAAN,CAAD,CAAuBU,EAAvB,CAA0BlC,KAAK,CAACE,MAAhC,EAAwC,UAACiC,KAAD;EAAA,aAAW,KAAI,CAACC,QAAL,CAAcD,KAAd,CAAX;EAAA,KAAxC;EAEA,SAAKE,OAAL;;EACA,SAAKD,QAAL;EACD;;;;;EAYD;WAEAC,UAAA,mBAAU;EAAA;;EACR,QAAMC,UAAU,GAAG,KAAKd,cAAL,KAAwB,KAAKA,cAAL,CAAoBE,MAA5C,GACfT,YAAY,CAACC,MADE,GACOD,YAAY,CAACE,QADvC;EAGA,QAAMoB,YAAY,GAAG,KAAKZ,OAAL,CAAa9B,MAAb,KAAwB,MAAxB,GACjByC,UADiB,GACJ,KAAKX,OAAL,CAAa9B,MAD9B;EAGA,QAAM2C,UAAU,GAAGD,YAAY,KAAKtB,YAAY,CAACE,QAA9B,GACf,KAAKsB,aAAL,EADe,GACQ,CAD3B;EAGA,SAAKX,QAAL,GAAgB,EAAhB;EACA,SAAKC,QAAL,GAAgB,EAAhB;EAEA,SAAKE,aAAL,GAAqB,KAAKS,gBAAL,EAArB;EAEA,QAAMC,OAAO,GAAG,GAAGC,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0B,KAAKlB,SAA/B,CAAd,CAAhB;EAEAc,IAAAA,OAAO,CACJK,GADH,CACO,UAAC3B,OAAD,EAAa;EAChB,UAAIvB,MAAJ;EACA,UAAMmD,cAAc,GAAGC,IAAI,CAACC,sBAAL,CAA4B9B,OAA5B,CAAvB;;EAEA,UAAI4B,cAAJ,EAAoB;EAClBnD,QAAAA,MAAM,GAAGgD,QAAQ,CAACM,aAAT,CAAuBH,cAAvB,CAAT;EACD;;EAED,UAAInD,MAAJ,EAAY;EACV,YAAMuD,SAAS,GAAGvD,MAAM,CAACwD,qBAAP,EAAlB;;EACA,YAAID,SAAS,CAACE,KAAV,IAAmBF,SAAS,CAACG,MAAjC,EAAyC;EACvC;EACA,iBAAO,CACL/D,CAAC,CAACK,MAAD,CAAD,CAAUyC,YAAV,IAA0BkB,GAA1B,GAAgCjB,UAD3B,EAELS,cAFK,CAAP;EAID;EACF;;EACD,aAAO,IAAP;EACD,KApBH,EAqBGS,MArBH,CAqBU,UAACC,IAAD;EAAA,aAAUA,IAAV;EAAA,KArBV,EAsBGC,IAtBH,CAsBQ,UAACC,CAAD,EAAIC,CAAJ;EAAA,aAAUD,CAAC,CAAC,CAAD,CAAD,GAAOC,CAAC,CAAC,CAAD,CAAlB;EAAA,KAtBR,EAuBGC,OAvBH,CAuBW,UAACJ,IAAD,EAAU;EACjB,MAAA,MAAI,CAAC7B,QAAL,CAAckC,IAAd,CAAmBL,IAAI,CAAC,CAAD,CAAvB;;EACA,MAAA,MAAI,CAAC5B,QAAL,CAAciC,IAAd,CAAmBL,IAAI,CAAC,CAAD,CAAvB;EACD,KA1BH;EA2BD;;WAEDM,UAAA,mBAAU;EACRxE,IAAAA,CAAC,CAACyE,UAAF,CAAa,KAAK3C,QAAlB,EAA4BlC,QAA5B;EACAI,IAAAA,CAAC,CAAC,KAAK+B,cAAN,CAAD,CAAuB2C,GAAvB,CAA2B7E,SAA3B;EAEA,SAAKiC,QAAL,GAAsB,IAAtB;EACA,SAAKC,cAAL,GAAsB,IAAtB;EACA,SAAKG,OAAL,GAAsB,IAAtB;EACA,SAAKE,SAAL,GAAsB,IAAtB;EACA,SAAKC,QAAL,GAAsB,IAAtB;EACA,SAAKC,QAAL,GAAsB,IAAtB;EACA,SAAKC,aAAL,GAAsB,IAAtB;EACA,SAAKC,aAAL,GAAsB,IAAtB;EACD;;;WAIDL,aAAA,oBAAWN,MAAX,EAAmB;EACjBA,IAAAA,MAAM,sBACD3B,OADC,MAED,OAAO2B,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAF/C,CAAN;;EAKA,QAAI,OAAOA,MAAM,CAACxB,MAAd,KAAyB,QAA7B,EAAuC;EACrC,UAAIsE,EAAE,GAAG3E,CAAC,CAAC6B,MAAM,CAACxB,MAAR,CAAD,CAAiBuE,IAAjB,CAAsB,IAAtB,CAAT;;EACA,UAAI,CAACD,EAAL,EAAS;EACPA,QAAAA,EAAE,GAAGlB,IAAI,CAACoB,MAAL,CAAYnF,IAAZ,CAAL;EACAM,QAAAA,CAAC,CAAC6B,MAAM,CAACxB,MAAR,CAAD,CAAiBuE,IAAjB,CAAsB,IAAtB,EAA4BD,EAA5B;EACD;;EACD9C,MAAAA,MAAM,CAACxB,MAAP,SAAoBsE,EAApB;EACD;;EAEDlB,IAAAA,IAAI,CAACqB,eAAL,CAAqBpF,IAArB,EAA2BmC,MAA3B,EAAmCvB,WAAnC;EAEA,WAAOuB,MAAP;EACD;;WAEDmB,gBAAA,yBAAgB;EACd,WAAO,KAAKjB,cAAL,KAAwBE,MAAxB,GACH,KAAKF,cAAL,CAAoBgD,WADjB,GAC+B,KAAKhD,cAAL,CAAoBiD,SAD1D;EAED;;WAED/B,mBAAA,4BAAmB;EACjB,WAAO,KAAKlB,cAAL,CAAoBkD,YAApB,IAAoCC,IAAI,CAACC,GAAL,CACzC9B,QAAQ,CAAC+B,IAAT,CAAcH,YAD2B,EAEzC5B,QAAQ,CAACgC,eAAT,CAAyBJ,YAFgB,CAA3C;EAID;;WAEDK,mBAAA,4BAAmB;EACjB,WAAO,KAAKvD,cAAL,KAAwBE,MAAxB,GACHA,MAAM,CAACsD,WADJ,GACkB,KAAKxD,cAAL,CAAoB8B,qBAApB,GAA4CE,MADrE;EAED;;WAEDpB,WAAA,oBAAW;EACT,QAAMqC,SAAS,GAAM,KAAKhC,aAAL,KAAuB,KAAKd,OAAL,CAAa/B,MAAzD;;EACA,QAAM8E,YAAY,GAAG,KAAKhC,gBAAL,EAArB;;EACA,QAAMuC,SAAS,GAAM,KAAKtD,OAAL,CAAa/B,MAAb,GACnB8E,YADmB,GAEnB,KAAKK,gBAAL,EAFF;;EAIA,QAAI,KAAK9C,aAAL,KAAuByC,YAA3B,EAAyC;EACvC,WAAKrC,OAAL;EACD;;EAED,QAAIoC,SAAS,IAAIQ,SAAjB,EAA4B;EAC1B,UAAMnF,MAAM,GAAG,KAAKiC,QAAL,CAAc,KAAKA,QAAL,CAAcmD,MAAd,GAAuB,CAArC,CAAf;;EAEA,UAAI,KAAKlD,aAAL,KAAuBlC,MAA3B,EAAmC;EACjC,aAAKqF,SAAL,CAAerF,MAAf;EACD;;EACD;EACD;;EAED,QAAI,KAAKkC,aAAL,IAAsByC,SAAS,GAAG,KAAK3C,QAAL,CAAc,CAAd,CAAlC,IAAsD,KAAKA,QAAL,CAAc,CAAd,IAAmB,CAA7E,EAAgF;EAC9E,WAAKE,aAAL,GAAqB,IAArB;;EACA,WAAKoD,MAAL;;EACA;EACD;;EAED,QAAMC,YAAY,GAAG,KAAKvD,QAAL,CAAcoD,MAAnC;;EACA,SAAK,IAAII,CAAC,GAAGD,YAAb,EAA2BC,CAAC,EAA5B,GAAiC;EAC/B,UAAMC,cAAc,GAAG,KAAKvD,aAAL,KAAuB,KAAKD,QAAL,CAAcuD,CAAd,CAAvB,IACnBb,SAAS,IAAI,KAAK3C,QAAL,CAAcwD,CAAd,CADM,KAElB,OAAO,KAAKxD,QAAL,CAAcwD,CAAC,GAAG,CAAlB,CAAP,KAAgC,WAAhC,IACGb,SAAS,GAAG,KAAK3C,QAAL,CAAcwD,CAAC,GAAG,CAAlB,CAHG,CAAvB;;EAKA,UAAIC,cAAJ,EAAoB;EAClB,aAAKJ,SAAL,CAAe,KAAKpD,QAAL,CAAcuD,CAAd,CAAf;EACD;EACF;EACF;;WAEDH,YAAA,mBAAUrF,MAAV,EAAkB;EAChB,SAAKkC,aAAL,GAAqBlC,MAArB;;EAEA,SAAKsF,MAAL;;EAEA,QAAMI,OAAO,GAAG,KAAK3D,SAAL,CACb4D,KADa,CACP,GADO,EAEbzC,GAFa,CAET,UAAC0C,QAAD;EAAA,aAAiBA,QAAjB,uBAA0C5F,MAA1C,YAAsD4F,QAAtD,gBAAwE5F,MAAxE;EAAA,KAFS,CAAhB;;EAIA,QAAM6F,KAAK,GAAGlG,CAAC,CAAC,GAAGmD,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0ByC,OAAO,CAACI,IAAR,CAAa,GAAb,CAA1B,CAAd,CAAD,CAAf;;EAEA,QAAID,KAAK,CAACE,QAAN,CAAezF,SAAS,CAACC,aAAzB,CAAJ,EAA6C;EAC3CsF,MAAAA,KAAK,CAACG,OAAN,CAActF,QAAQ,CAACM,QAAvB,EAAiCiF,IAAjC,CAAsCvF,QAAQ,CAACQ,eAA/C,EAAgEgF,QAAhE,CAAyE5F,SAAS,CAACG,MAAnF;EACAoF,MAAAA,KAAK,CAACK,QAAN,CAAe5F,SAAS,CAACG,MAAzB;EACD,KAHD,MAGO;EACL;EACAoF,MAAAA,KAAK,CAACK,QAAN,CAAe5F,SAAS,CAACG,MAAzB,EAFK;EAIL;;EACAoF,MAAAA,KAAK,CAACM,OAAN,CAAczF,QAAQ,CAACE,cAAvB,EAAuCwF,IAAvC,CAA+C1F,QAAQ,CAACG,SAAxD,UAAsEH,QAAQ,CAACK,UAA/E,EAA6FmF,QAA7F,CAAsG5F,SAAS,CAACG,MAAhH,EALK;;EAOLoF,MAAAA,KAAK,CAACM,OAAN,CAAczF,QAAQ,CAACE,cAAvB,EAAuCwF,IAAvC,CAA4C1F,QAAQ,CAACI,SAArD,EAAgEuF,QAAhE,CAAyE3F,QAAQ,CAACG,SAAlF,EAA6FqF,QAA7F,CAAsG5F,SAAS,CAACG,MAAhH;EACD;;EAEDd,IAAAA,CAAC,CAAC,KAAK+B,cAAN,CAAD,CAAuB4E,OAAvB,CAA+BpG,KAAK,CAACC,QAArC,EAA+C;EAC7CoG,MAAAA,aAAa,EAAEvG;EAD8B,KAA/C;EAGD;;WAEDsF,SAAA,kBAAS;EACP,OAAGxC,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0B,KAAKlB,SAA/B,CAAd,EACG6B,MADH,CACU,UAAC4C,IAAD;EAAA,aAAUA,IAAI,CAACC,SAAL,CAAeC,QAAf,CAAwBpG,SAAS,CAACG,MAAlC,CAAV;EAAA,KADV,EAEGwD,OAFH,CAEW,UAACuC,IAAD;EAAA,aAAUA,IAAI,CAACC,SAAL,CAAeE,MAAf,CAAsBrG,SAAS,CAACG,MAAhC,CAAV;EAAA,KAFX;EAGD;;;cAIMmG,mBAAP,0BAAwBpF,MAAxB,EAAgC;EAC9B,WAAO,KAAKqF,IAAL,CAAU,YAAY;EAC3B,UAAIC,IAAI,GAAGnH,CAAC,CAAC,IAAD,CAAD,CAAQmH,IAAR,CAAavH,QAAb,CAAX;;EACA,UAAMsC,OAAO,GAAG,OAAOL,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;EAEA,UAAI,CAACsF,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIxF,SAAJ,CAAc,IAAd,EAAoBO,OAApB,CAAP;EACAlC,QAAAA,CAAC,CAAC,IAAD,CAAD,CAAQmH,IAAR,CAAavH,QAAb,EAAuBuH,IAAvB;EACD;;EAED,UAAI,OAAOtF,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOsF,IAAI,CAACtF,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIuF,SAAJ,wBAAkCvF,MAAlC,QAAN;EACD;;EACDsF,QAAAA,IAAI,CAACtF,MAAD,CAAJ;EACD;EACF,KAfM,CAAP;EAgBD;;;;0BA1MoB;EACnB,aAAOlC,OAAP;EACD;;;0BAEoB;EACnB,aAAOO,OAAP;EACD;;;;;EAuMH;;;;;;;EAMAF,CAAC,CAACiC,MAAD,CAAD,CAAUQ,EAAV,CAAalC,KAAK,CAACG,aAAnB,EAAkC,YAAM;EACtC,MAAM2G,UAAU,GAAG,GAAGlE,KAAH,CAASC,IAAT,CAAcC,QAAQ,CAACC,gBAAT,CAA0BvC,QAAQ,CAACC,QAAnC,CAAd,CAAnB;EACA,MAAMsG,gBAAgB,GAAGD,UAAU,CAAC5B,MAApC;;EAEA,OAAK,IAAII,CAAC,GAAGyB,gBAAb,EAA+BzB,CAAC,EAAhC,GAAqC;EACnC,QAAM0B,IAAI,GAAGvH,CAAC,CAACqH,UAAU,CAACxB,CAAD,CAAX,CAAd;;EACAlE,IAAAA,SAAS,CAACsF,gBAAV,CAA2B7D,IAA3B,CAAgCmE,IAAhC,EAAsCA,IAAI,CAACJ,IAAL,EAAtC;EACD;EACF,CARD;EAUA;;;;;;EAMAnH,CAAC,CAACC,EAAF,CAAKP,IAAL,IAAaiC,SAAS,CAACsF,gBAAvB;EACAjH,CAAC,CAACC,EAAF,CAAKP,IAAL,EAAW8H,WAAX,GAAyB7F,SAAzB;;EACA3B,CAAC,CAACC,EAAF,CAAKP,IAAL,EAAW+H,UAAX,GAAwB,YAAM;EAC5BzH,EAAAA,CAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb;EACA,SAAO4B,SAAS,CAACsF,gBAAjB;EACD,CAHD;;;;;;;;"}