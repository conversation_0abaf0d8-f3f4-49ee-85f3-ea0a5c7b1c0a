import axios, { AxiosResponse } from "axios";
import EndPoint from "./EndPoint.Config";
import { getAmbienteContexto, getUrlBase } from "../utilities/contextInfo";
import { getApiProveedorFacturaHeaders } from "./contracts/ApiBpmPoliticasProcesosRequestHeaders";
import { Proveedor } from "../entities/Proveedor";
import { FacturaInsert } from "./contracts/FacturaInsert";
import { ErrorResponse } from "./contracts/ErrorResponse";
import { InsertFacturaSuccess } from "./contracts/InsertFacturaSuccess";
import { FacturaGetResponse } from "./contracts/FacturaGetResponse";
import { concatFilters } from "../utilities/ParsersTS";
import { Estados } from "../entities/Enums/estadosRespuesta";
import { DatosProvision } from "../entities/DatosProvision";
import { ModificarFactura } from "../entities/ModificarFactura";
import { BodyEstado } from "../entities/BodyEstado";

const Params =
{
  urlBase: getUrlBase(),
  ambiente: getAmbienteContexto(),
  request: axios.create({}),
  endpoints: EndPoint.apiProveedorFactura
}

export const getProovedor = async (id: string, tipoDocumento: string) => {
  console.log("Dentro de Get Proveedor", id, tipoDocumento);
  let _cacheControl = 'max-age=60*60*10, private'
  let _headers = await getApiProveedorFacturaHeaders(_cacheControl)
  let _sturlEndpoint = Params.endpoints.vUno.get.proveedor
  let filtroTipoDocumento = "?tipoDocumento=" + tipoDocumento;
  let urlEndpoint = _sturlEndpoint.replace('${codigoCliente}', id)
  urlEndpoint = urlEndpoint + filtroTipoDocumento;
  console.log("urlFinalMetodoGet", urlEndpoint)

  var config = {
    method: 'get',
    url: Params.urlBase + urlEndpoint,
    headers: _headers
  };
  let Promise = Params.request<Proveedor>(config)
    .then(function (response) {
      console.log("response get proveedor: ", response)

      if (response.status == Estados.estadoExitoso) {
        return response.data
      }
      else if (response.status == Estados.estadoSinContenido) {
        alert("Ocurrió un error con el contenido.");
        return null
      }

      if (response.status == Estados.estadoValidacionIncorrecta) {
        alert("Por favor. Verificar los datos.");
      }

      if (response.status == Estados.estadoNoAutorizado) {
        alert("Favor, refrescar la página y volver a intentar.");
      }

      if (response.status == Estados.estadoErrorServidores) {
        alert("Ocurrió un error.");
      }

    })
    .catch(function (error) {
      console.log("error getProveedor: ", error);
    });

  let result = await Promise;

  return result
}

export const postFactura = async (factura: FacturaInsert[]) => {
  console.log("el llamado a la api post factura, body: ", factura);
  const _headers = await getApiProveedorFacturaHeaders('');
  const _sturlEndpoint = Params.endpoints.vUno.post.facturas;
  let config = {
    method: 'post',
    url: Params.urlBase + _sturlEndpoint,
    headers: _headers
  };
  const promise = Params.request.post<any, AxiosResponse<InsertFacturaSuccess, ErrorResponse>>(config.url, factura, config)
    .then(function (response) {
      console.log("factura postFactura:", factura);
      console.log("response postFactura:", response);
      console.log("response.data postFactura:", response.data);
      localStorage.setItem("idLote", response.data.idLote);
      return response;
    })
    .catch(function (error) {
      console.log("Error catch postFactura:", error);
      return error;
    });

  const result = await promise

  return result

}

export const getFacturas = async (idLote?: number, numeroFactura?: string) => {
  console.log('ESTOY EN GET FACTURAS');
  console.log("idLote getFacturas:", idLote);
  localStorage.setItem("idLoteRelacion", idLote.toString());
  const _cacheControl = 'max-age=60*60*10, private'
  const _headers = await getApiProveedorFacturaHeaders(_cacheControl)
  const _sturlEndpoint = Params.endpoints.vUno.get.facturas
  const _filters = [
    (idLote != undefined && idLote != 0) ? 'idLote=' + idLote : '',
    (numeroFactura != undefined && numeroFactura != '') ? 'estado=' + numeroFactura : '',
  ]
  console.log("_filters getFacturas:", _filters);

  let _filtersURL = ''
  if (!_filters.every(element => { element === '' })) {
    console.log('entre aca en el filter')
    _filtersURL = concatFilters([..._filters])
    console.log("_filtersURL getFacturas:", _filtersURL);
  }

  const config = {
    method: 'get',
    url: Params.urlBase + _sturlEndpoint + _filtersURL,
    headers: _headers
  };

  console.log("URL GET FACTURA:", config.url);

  const Promise = Params.request<FacturaGetResponse[]>(config)
    .then(function (response) {
      console.log("response.data getFacturas:", response.data);
      localStorage.setItem("responseDataFactura", JSON.stringify(response.data));
      return response.data;
    })
    .catch(function (error) {
      console.log("Error catch getFacturas:", error);
    });

  const result = await Promise;

  return result

}

export const  putActualizarFactura = async (body: ModificarFactura, bodyEstado: BodyEstado, opcion: number) => {
  let _headers = await getApiProveedorFacturaHeaders('');
  let _sturlEndpoint = Params.endpoints.vUno.put.actualizarFactura;

  let config = {
    method: 'put',
    url: Params.urlBase + _sturlEndpoint,
    headers: _headers
  }

  console.log("body putActualizarFactura:", body);
  console.log("opcion putActualizarFactura:", opcion);

  let promise = Params.request.put<any, AxiosResponse<DatosProvision, ErrorResponse>>(config.url, opcion === 1 ? body : opcion === 2 ? bodyEstado : null, config)
    .then(function (response) {
      console.log("response putActualizarFactura:", response);
      return response;
    }).catch(function (error) {
      console.log("error catch putActualizarFactura:", error);
      return error;
    })
  let result = await promise;
  return result;
}

export const postMigracionProveedorFacturas = async () => {
  let idLote = localStorage.getItem("idLoteRelacion");
  console.log("idLote:", idLote);
  let _headers = await getApiProveedorFacturaHeaders('');
  let _sturlEndpoint = Params.endpoints.vUno.post.migracion;
  let urlEndpoint = _sturlEndpoint.replace('{idLote}', idLote);

  let _body = idLote;

  let config = {
    method: 'post',
    url: Params.urlBase + urlEndpoint,
    headers: _headers
  }

  const promise = Params.request.post<any, AxiosResponse<InsertFacturaSuccess, ErrorResponse>>(config.url, _body, config)
    .then(function (response) {
      response.data;
      console.log("response.data api postMigracionProveedorFacturas:", response.data);
      console.log("response.status:", response.status);
      return response.data;
    }).catch(function (error) {
      console.log("Error catch postMigracionProveedorFacturas:", error);
    })
  const result = await promise;
  return result;
}

export const getDestinoFactura = async () => {
  let _cacheControl = "max-age=60*60*10, private";
  let _headers = await getApiProveedorFacturaHeaders(_cacheControl);
  let _sturlEndpoint = Params.endpoints.vUno.get.destinoFactura;

  let config = {
    method: 'get',
    url: Params.urlBase + _sturlEndpoint,
    headers: _headers
  }

  let promise = Params.request<any, any>(config)
    .then(function (response) {
      return response.data;
    }).catch(function (error) {
      console.log("error catch getDestinoFactura:", error);
    });
  let result = await promise;
  return result;
}

export const getDestinoFacturaDos = async (codigo: number, tipoCodigo: number) => {
  let _cacheControl = "max-age=60*60*10, private";
  let _headers = await getApiProveedorFacturaHeaders(_cacheControl);
  let _sturlEndpoint = Params.endpoints.vUno.get.destinoFactura;
  let filtroUrl = "";
  if (tipoCodigo == 1) {
    filtroUrl = "?codigoArea=" + codigo;
  } else if (tipoCodigo == 2) {
    filtroUrl = "?codigoDepartamento=" + codigo;
  } else {
    filtroUrl = '';
  }

  let config = {
    method: 'get',
    url: Params.urlBase + _sturlEndpoint + filtroUrl,
    headers: _headers
  }

  console.log("config.url getDestinoFacturaDos:", config.url);

  let promise = Params.request<any, any>(config)
    .then(function (response) {
      return response.data;
    }).catch(function (error) {
      console.log("error catch getDestinoFactura:", error);
    });
  let result = await promise;
  return result;
}

