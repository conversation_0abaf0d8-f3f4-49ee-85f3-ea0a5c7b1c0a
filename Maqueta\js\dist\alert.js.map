{"version": 3, "file": "alert.js", "sources": ["../src/alert.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.4.1): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME                = 'alert'\nconst VERSION             = '4.4.1'\nconst DATA_KEY            = 'bs.alert'\nconst EVENT_KEY           = `.${DATA_KEY}`\nconst DATA_API_KEY        = '.data-api'\nconst JQUERY_NO_CONFLICT  = $.fn[NAME]\n\nconst Selector = {\n  DISMISS : '[data-dismiss=\"alert\"]'\n}\n\nconst Event = {\n  CLOSE          : `close${EVENT_KEY}`,\n  CLOSED         : `closed${EVENT_KEY}`,\n  CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n}\n\nconst ClassName = {\n  ALERT : 'alert',\n  FADE  : 'fade',\n  SHOW  : 'show'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Alert {\n  constructor(element) {\n    this._element = element\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  close(element) {\n    let rootElement = this._element\n    if (element) {\n      rootElement = this._getRootElement(element)\n    }\n\n    const customEvent = this._triggerCloseEvent(rootElement)\n\n    if (customEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._removeElement(rootElement)\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _getRootElement(element) {\n    const selector = Util.getSelectorFromElement(element)\n    let parent     = false\n\n    if (selector) {\n      parent = document.querySelector(selector)\n    }\n\n    if (!parent) {\n      parent = $(element).closest(`.${ClassName.ALERT}`)[0]\n    }\n\n    return parent\n  }\n\n  _triggerCloseEvent(element) {\n    const closeEvent = $.Event(Event.CLOSE)\n\n    $(element).trigger(closeEvent)\n    return closeEvent\n  }\n\n  _removeElement(element) {\n    $(element).removeClass(ClassName.SHOW)\n\n    if (!$(element).hasClass(ClassName.FADE)) {\n      this._destroyElement(element)\n      return\n    }\n\n    const transitionDuration = Util.getTransitionDurationFromElement(element)\n\n    $(element)\n      .one(Util.TRANSITION_END, (event) => this._destroyElement(element, event))\n      .emulateTransitionEnd(transitionDuration)\n  }\n\n  _destroyElement(element) {\n    $(element)\n      .detach()\n      .trigger(Event.CLOSED)\n      .remove()\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $element = $(this)\n      let data       = $element.data(DATA_KEY)\n\n      if (!data) {\n        data = new Alert(this)\n        $element.data(DATA_KEY, data)\n      }\n\n      if (config === 'close') {\n        data[config](this)\n      }\n    })\n  }\n\n  static _handleDismiss(alertInstance) {\n    return function (event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      alertInstance.close(this)\n    }\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document).on(\n  Event.CLICK_DATA_API,\n  Selector.DISMISS,\n  Alert._handleDismiss(new Alert())\n)\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME]             = Alert._jQueryInterface\n$.fn[NAME].Constructor = Alert\n$.fn[NAME].noConflict  = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Alert._jQueryInterface\n}\n\nexport default Alert\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "Selector", "DISMISS", "Event", "CLOSE", "CLOSED", "CLICK_DATA_API", "ClassName", "ALERT", "FADE", "SHOW", "<PERSON><PERSON>", "element", "_element", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "isDefaultPrevented", "_removeElement", "dispose", "removeData", "selector", "<PERSON><PERSON>", "getSelectorFromElement", "parent", "document", "querySelector", "closest", "closeEvent", "trigger", "removeClass", "hasClass", "_destroyElement", "transitionDuration", "getTransitionDurationFromElement", "one", "TRANSITION_END", "event", "emulateTransitionEnd", "detach", "remove", "_jQueryInterface", "config", "each", "$element", "data", "_handleDismiss", "alertInstance", "preventDefault", "on", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAUA;;;;;;EAMA,IAAMA,IAAI,GAAkB,OAA5B;EACA,IAAMC,OAAO,GAAe,OAA5B;EACA,IAAMC,QAAQ,GAAc,UAA5B;EACA,IAAMC,SAAS,SAAiBD,QAAhC;EACA,IAAME,YAAY,GAAU,WAA5B;EACA,IAAMC,kBAAkB,GAAIC,CAAC,CAACC,EAAF,CAAKP,IAAL,CAA5B;EAEA,IAAMQ,QAAQ,GAAG;EACfC,EAAAA,OAAO,EAAG;EADK,CAAjB;EAIA,IAAMC,KAAK,GAAG;EACZC,EAAAA,KAAK,YAAoBR,SADb;EAEZS,EAAAA,MAAM,aAAoBT,SAFd;EAGZU,EAAAA,cAAc,YAAWV,SAAX,GAAuBC;EAHzB,CAAd;EAMA,IAAMU,SAAS,GAAG;EAChBC,EAAAA,KAAK,EAAG,OADQ;EAEhBC,EAAAA,IAAI,EAAI,MAFQ;EAGhBC,EAAAA,IAAI,EAAI;EAHQ,CAAlB;EAMA;;;;;;MAMMC;;;EACJ,iBAAYC,OAAZ,EAAqB;EACnB,SAAKC,QAAL,GAAgBD,OAAhB;EACD;;;;;EAQD;WAEAE,QAAA,eAAMF,OAAN,EAAe;EACb,QAAIG,WAAW,GAAG,KAAKF,QAAvB;;EACA,QAAID,OAAJ,EAAa;EACXG,MAAAA,WAAW,GAAG,KAAKC,eAAL,CAAqBJ,OAArB,CAAd;EACD;;EAED,QAAMK,WAAW,GAAG,KAAKC,kBAAL,CAAwBH,WAAxB,CAApB;;EAEA,QAAIE,WAAW,CAACE,kBAAZ,EAAJ,EAAsC;EACpC;EACD;;EAED,SAAKC,cAAL,CAAoBL,WAApB;EACD;;WAEDM,UAAA,mBAAU;EACRtB,IAAAA,CAAC,CAACuB,UAAF,CAAa,KAAKT,QAAlB,EAA4BlB,QAA5B;EACA,SAAKkB,QAAL,GAAgB,IAAhB;EACD;;;WAIDG,kBAAA,yBAAgBJ,OAAhB,EAAyB;EACvB,QAAMW,QAAQ,GAAGC,IAAI,CAACC,sBAAL,CAA4Bb,OAA5B,CAAjB;EACA,QAAIc,MAAM,GAAO,KAAjB;;EAEA,QAAIH,QAAJ,EAAc;EACZG,MAAAA,MAAM,GAAGC,QAAQ,CAACC,aAAT,CAAuBL,QAAvB,CAAT;EACD;;EAED,QAAI,CAACG,MAAL,EAAa;EACXA,MAAAA,MAAM,GAAG3B,CAAC,CAACa,OAAD,CAAD,CAAWiB,OAAX,OAAuBtB,SAAS,CAACC,KAAjC,EAA0C,CAA1C,CAAT;EACD;;EAED,WAAOkB,MAAP;EACD;;WAEDR,qBAAA,4BAAmBN,OAAnB,EAA4B;EAC1B,QAAMkB,UAAU,GAAG/B,CAAC,CAACI,KAAF,CAAQA,KAAK,CAACC,KAAd,CAAnB;EAEAL,IAAAA,CAAC,CAACa,OAAD,CAAD,CAAWmB,OAAX,CAAmBD,UAAnB;EACA,WAAOA,UAAP;EACD;;WAEDV,iBAAA,wBAAeR,OAAf,EAAwB;EAAA;;EACtBb,IAAAA,CAAC,CAACa,OAAD,CAAD,CAAWoB,WAAX,CAAuBzB,SAAS,CAACG,IAAjC;;EAEA,QAAI,CAACX,CAAC,CAACa,OAAD,CAAD,CAAWqB,QAAX,CAAoB1B,SAAS,CAACE,IAA9B,CAAL,EAA0C;EACxC,WAAKyB,eAAL,CAAqBtB,OAArB;;EACA;EACD;;EAED,QAAMuB,kBAAkB,GAAGX,IAAI,CAACY,gCAAL,CAAsCxB,OAAtC,CAA3B;EAEAb,IAAAA,CAAC,CAACa,OAAD,CAAD,CACGyB,GADH,CACOb,IAAI,CAACc,cADZ,EAC4B,UAACC,KAAD;EAAA,aAAW,KAAI,CAACL,eAAL,CAAqBtB,OAArB,EAA8B2B,KAA9B,CAAX;EAAA,KAD5B,EAEGC,oBAFH,CAEwBL,kBAFxB;EAGD;;WAEDD,kBAAA,yBAAgBtB,OAAhB,EAAyB;EACvBb,IAAAA,CAAC,CAACa,OAAD,CAAD,CACG6B,MADH,GAEGV,OAFH,CAEW5B,KAAK,CAACE,MAFjB,EAGGqC,MAHH;EAID;;;UAIMC,mBAAP,0BAAwBC,MAAxB,EAAgC;EAC9B,WAAO,KAAKC,IAAL,CAAU,YAAY;EAC3B,UAAMC,QAAQ,GAAG/C,CAAC,CAAC,IAAD,CAAlB;EACA,UAAIgD,IAAI,GAASD,QAAQ,CAACC,IAAT,CAAcpD,QAAd,CAAjB;;EAEA,UAAI,CAACoD,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIpC,KAAJ,CAAU,IAAV,CAAP;EACAmC,QAAAA,QAAQ,CAACC,IAAT,CAAcpD,QAAd,EAAwBoD,IAAxB;EACD;;EAED,UAAIH,MAAM,KAAK,OAAf,EAAwB;EACtBG,QAAAA,IAAI,CAACH,MAAD,CAAJ,CAAa,IAAb;EACD;EACF,KAZM,CAAP;EAaD;;UAEMI,iBAAP,wBAAsBC,aAAtB,EAAqC;EACnC,WAAO,UAAUV,KAAV,EAAiB;EACtB,UAAIA,KAAJ,EAAW;EACTA,QAAAA,KAAK,CAACW,cAAN;EACD;;EAEDD,MAAAA,aAAa,CAACnC,KAAd,CAAoB,IAApB;EACD,KAND;EAOD;;;;0BAlGoB;EACnB,aAAOpB,OAAP;EACD;;;;;EAmGH;;;;;;;EAMAK,CAAC,CAAC4B,QAAD,CAAD,CAAYwB,EAAZ,CACEhD,KAAK,CAACG,cADR,EAEEL,QAAQ,CAACC,OAFX,EAGES,KAAK,CAACqC,cAAN,CAAqB,IAAIrC,KAAJ,EAArB,CAHF;EAMA;;;;;;EAMAZ,CAAC,CAACC,EAAF,CAAKP,IAAL,IAAyBkB,KAAK,CAACgC,gBAA/B;EACA5C,CAAC,CAACC,EAAF,CAAKP,IAAL,EAAW2D,WAAX,GAAyBzC,KAAzB;;EACAZ,CAAC,CAACC,EAAF,CAAKP,IAAL,EAAW4D,UAAX,GAAyB,YAAM;EAC7BtD,EAAAA,CAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb;EACA,SAAOa,KAAK,CAACgC,gBAAb;EACD,CAHD;;;;;;;;"}