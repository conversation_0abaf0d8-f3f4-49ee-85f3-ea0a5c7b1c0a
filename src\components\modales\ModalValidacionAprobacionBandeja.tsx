import React from 'react';
import { But<PERSON>, Modal } from 'react-bootstrap';

interface ModalPropsValidation {
  show: boolean;
  onClose: () => void;
  message: string;
}

const ModalValidacionAprobacionBandeja: React.FC<ModalPropsValidation> = ({ show, onClose, message }) => {
  return (
    <div>
      <Modal show={show} onHide={onClose} backdrop="static" keyboard={false}>
        <Modal.Header>
          <Modal.Title className='text-secondary'></Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p className='text-danger text-center font-weight-bold'>{message}</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="primary" onClick={onClose}>
            Aceptar
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  )
}

export default ModalValidacionAprobacionBandeja;