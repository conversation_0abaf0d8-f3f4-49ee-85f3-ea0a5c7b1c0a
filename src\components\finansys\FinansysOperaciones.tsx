import React, { useEffect } from 'react';

interface FinansysOperacionesProps {
  previewContainerId: string;
  defaultContainerId: string;
}

const FinansysOperaciones: React.FC<FinansysOperacionesProps> = ({
  previewContainerId,
  defaultContainerId
}) => {

  useEffect(() => {
    // Inicializar event listeners para elementos .tbl-item
    const handleItemClick = (event: Event) => {
      const target = event.currentTarget as HTMLElement;
      
      // Toggle clase selected
      target.classList.toggle('tbl-item--selected');
      
      // Obtener el nombre del usuario
      const nameElement = target.querySelector('.tbl-item__name');
      if (nameElement) {
        const username = nameElement.textContent || '';
        showDetailsView(username);
      }
    };

    // Agregar event listeners a todos los elementos .tbl-item
    const tblItems = Array.from(document.querySelectorAll('.tbl-item'));
    for (const item of tblItems) {
      item.addEventListener('click', handleItemClick);
    }

    // Cleanup function
    return () => {
      for (const item of tblItems) {
        item.removeEventListener('click', handleItemClick);
      }
    };
  }, []);

  const closePreview = () => {
    const previewDiv = document.getElementById(previewContainerId);
    if (previewDiv) {
      previewDiv.classList.remove('preview--show');
    }
    
    // Remover clase selected de todos los elementos
    for (const item of Array.from(document.querySelectorAll('.tbl-item'))) {
      item.classList.remove('tbl-item--selected');
    }

    showDefaultView();
  };

  const showDetailsView = (username: string) => {
    const defaultDiv = document.getElementById(defaultContainerId);
    const previewDiv = document.getElementById(previewContainerId);
    
    if (defaultDiv) {
      defaultDiv.classList.remove('default--show');
    }
    
    if (previewDiv) {
      previewDiv.classList.add('preview--show');
      previewDiv.innerHTML = generateOperacionHTML(username);
    }
  };

  const showDocumentsView = () => {
    const defaultDiv = document.getElementById(defaultContainerId);
    const previewDiv = document.getElementById(previewContainerId);
    
    if (defaultDiv) {
      defaultDiv.classList.remove('default--show');
    }
    
    if (previewDiv) {
      previewDiv.classList.add('preview--show');
      previewDiv.innerHTML = generateDocumentosHTML();
    }
  };

  const showDefaultView = () => {
    const defaultDiv = document.getElementById(defaultContainerId);
    if (defaultDiv) {
      defaultDiv.classList.add('default--show');
      defaultDiv.innerHTML = generateDefaultContent();
    }
  };

  const showLoaderView = () => {
    const defaultDiv = document.getElementById(defaultContainerId);
    if (defaultDiv) {
      defaultDiv.classList.add('default--show');
      defaultDiv.innerHTML = generateDefaultLoader();
    }
  };

  const showMultiView = () => {
    const defaultDiv = document.getElementById(defaultContainerId);
    const previewDiv = document.getElementById(previewContainerId);
    
    if (defaultDiv) {
      defaultDiv.classList.remove('default--show');
    }
    
    if (previewDiv) {
      previewDiv.classList.remove('preview--show');
    }
    
    if (defaultDiv) {
      defaultDiv.classList.add('default--show');
      defaultDiv.innerHTML = generateMultiSelect();
    }
  };

  const generateDefaultContent = () => {
    return `
      <div class="text-center">
        <i class="fa fa-file fa-2x mb-3"></i>
        <h5 class="mb-0">Seleccione una Operación</h5>
        <p>Los detalles se visualizan aqui</p>
      </div>
    `;
  };

  const generateDefaultLoader = () => {
    return `
      <div class="text-center">
        <i class="fa fa-sync-alt fa-spin fa-2x mb-3"></i>
        <p>Obteniendo datos...</p>
      </div>
    `;
  };

  const generateMultiSelect = (number: number = 2) => {
    return `
      <div class="text-center">
        <i class="fa fa-copy fa-2x mb-3"></i>
        <h5 class="mb-0">${number} Operaciones Seleccionadas</h5>
        <p><a href="#">Seleccionar todas</a> las operaciones</p>
        <div class="text-left px-5">
          <a href="#" class="d-block mb-2"><i class="fa fa-check"></i> Aprobar</a>
          <a href="#" class="d-block mb-2" data-toggle="modal" data-target="#rechazar"><i class="fa fa-times"></i> Rechazar</a>
        </div>
      </div>
    `;
  };

  const generateOperacionHTML = (username: string) => {
    return `
      <div class="px-4 py-3 d-flex justify-content-between bg-white sticky-top">
        <div class="d-flex align-items-center">
          <span onclick="closePreview()" class="pw-close">
            <span class="pw-close__bar-1"></span>
            <span class="pw-close__bar-2"></span>
          </span>
          <p class="ml-3 mb-0">Solicitud No. 5750131219</p>
        </div>
        <div class="btn-group">
          <a href="#" class="btn btn-outline-fun-blue" data-toggle="modal" data-target="#rechazar">Rechazar</a>
          <a href="#" class="btn btn-fun-blue">Aprobar</a>
        </div>
      </div>
      <div class="prw-cont__detail px-4">
        <div class="row mb-4">
          <div class="col-12 col-md-3">
            <figure class="avatar avatar--lg bg-key-lime">
              <span>SS</span>
            </figure>
            <div class="mt-2">
              <h5 class="mb-0">${username}</h5>
              <p class="mb-0">Clasificacion 1</p>
              <p>Cod.: 410241</p>
            </div>
            <div class="form-group">
              <button onclick="showDocumentsView()" class="btn btn-secondary">Ver documentos</button>
            </div>
          </div>
          <div class="col-12 col-md-9">
            <!-- Contenido de cliente aquí -->
          </div>
        </div>
      </div>
    `;
  };

  const generateDocumentosHTML = () => {
    return `
      <div class="px-4 py-3 d-flex justify-content-between bg-white sticky-top">
        <div class="d-flex align-items-center">
          <span onclick="closePreview()" class="pw-close">
            <span class="pw-close__bar-1"></span>
            <span class="pw-close__bar-2"></span>
          </span>
          <p class="ml-3 mb-0">Solicitud No. 5750131219</p>
        </div>
        <div class="btn-group">
          <a href="#" class="btn btn-outline-fun-blue">Rechazar</a>
          <a href="#" class="btn btn-fun-blue">Aprobar</a>
        </div>
      </div>
      <div class="prw-cont__detail px-4">
        <div class="pt-5 pb-3">
          <h4 class="text-center">Documentos</h4>
          <button onclick="showDetailsView()" class="btn btn-link"><i class="fa fa-chevron-left"></i> Operación</button>
        </div>
        <!-- Contenido de documentos aquí -->
      </div>
    `;
  };

  // Exponer funciones globalmente para compatibilidad con HTML existente
  useEffect(() => {
    (globalThis as any).closePreview = closePreview;
    (globalThis as any).showDetailsView = showDetailsView;
    (globalThis as any).showDocumentsView = showDocumentsView;
    (globalThis as any).showDefaultView = showDefaultView;
    (globalThis as any).showLoaderView = showLoaderView;
    (globalThis as any).showMultiView = showMultiView;
  }, []);

  return null; // Este componente no renderiza nada, solo maneja la lógica
};

export default FinansysOperaciones;
