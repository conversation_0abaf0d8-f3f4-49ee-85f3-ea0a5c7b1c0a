import { isNumber, isString } from "lodash"
import { Gravadas } from "./Gravadas"
import { IVA } from "./IVA"

export class Monto {
  constructor(monto: number, moneda?: string, exentas?: number) {
    this.total = monto
    this.iva = new IVA(monto)
    this.gravadas = new Gravadas(monto)
    if (isString(moneda)) {
      this.moneda = moneda
    }
    if (isNumber(exentas)) {
      this.exentas = exentas
    }
  }

  moneda?: string
  total: number
  iva: IVA
  gravadas: Gravadas
  exentas?: number
}