import React from 'react'
import { useProveedor } from '../contextProvider/datosProveedorContext'

const limpiarCamposFactura = () => {
  const { state } = useProveedor();
  state.nombre = "";
  state.nombreDos = "";
  state.apellido = "";
  state.apellidoDos = "";
  state.bandejaNombre = "";
  state.pais = "";
  state.bandejaPais = "";
  state.telefono = "";
  state.bandejaTelefono = "";
  state.numeroDocumento = "";
  state.estadoPerfilMensaje = "";
  state.DVTipoDocumento = "";
  state.bandejaNroDocumento = "";
  state.direccion = "";
  state.bandejaDireccion = "";
  state.ciudad = "";
  state.bandejaCiudad = "";
  state.telefono = "";
  state.bandejaTelefono = "";
  state.fechaCarga = "";
  state.bandejaFechaEmision = "";
  state.timbrado = [];
  state.bandejaTimbrado = "";
  state.fechaVencimiento = "";
  state.bandejaFacturaParteUno = "";
  state.bandejaFacturaParteDos = "";
  state.bandejaFacturaParteTres = "";
  state.bandejaProvision = "";
  state.bandejaTipoMoneda = "";
  state.bandejaTipoComprobante = "";
  state.descripcionAreaBandeja = "";
  state.descripcionDepartamentoBandeja = "";
  state.montoTotal = "";
  state.montoCondicionVenta = "";
  state.montoCinco = "";
  state.montoDiez = "";
  state.montoGravadasCinco = "";
  state.montoGravadasDiez = "";
  state.montoIvaCinco = "";
  state.montoIvaDiez = "";
  state.montoDescripcion = "";
  state.montoExentas = "";
  state.estadoPerfilMensaje = "";
  state.DVTipoDocumento = "";
  let arrayID: any = [];
  localStorage.setItem("arrayID", JSON.stringify(arrayID));

  return (
    <div></div>
  )
}

export default limpiarCamposFactura