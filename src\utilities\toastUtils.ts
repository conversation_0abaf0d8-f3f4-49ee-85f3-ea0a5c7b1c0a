import { Bounce, toast } from 'react-toastify';

export enum ToastType {
    Success = 'success',
    Error = 'error',
    Warning = 'warning',
    Info = 'info'
}

export const showToast = (message: string | JSX.Element, type: ToastType = ToastType.Info) => {
    toast[type](message, {
        style: { fontFamily: "ContiSans", whiteSpace: 'pre-line' },
        position: "top-right",
        autoClose: 8000,
        hideProgressBar: false,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
        theme: "light",
        transition: <PERSON><PERSON><PERSON>,
    });
};