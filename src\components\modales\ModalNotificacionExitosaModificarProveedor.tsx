import React from 'react'
import { But<PERSON>, Modal } from 'react-bootstrap';

interface ModalProps {
  abrir: boolean;
  cerrar: () => void;
}

const ModalNotificacionExitosaModificarProveedor: React.FC<ModalProps> = ({ abrir, cerrar }) => {
  return (
    <div>
      <Modal show={abrir} onHide={cerrar}>
        <Modal.Header>
          <Modal.Title className='text-secondary'></Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p className='text-center text-success font-weight-bold'>Los datos fueron modificados de manera exitosa.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={cerrar}>Cerrar</Button>
        </Modal.Footer>
      </Modal>
    </div>
  )
}

export default ModalNotificacionExitosaModificarProveedor