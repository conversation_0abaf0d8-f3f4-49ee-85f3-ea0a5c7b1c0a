import React, { useEffect, useState } from 'react';
import ReactDOM from 'react-dom';
import { init } from './init';
import { Button, Modal } from 'react-bootstrap';
import { injectStyle } from "react-toastify/dist/inject-style";

const App: React.FC = () => {
  const [showWarningModal, setShowWarningModal] = useState(false);
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [shouldReload, setShouldReload] = useState(false);

  init();

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setShowWarningModal(true);
    }, 8460000); // 1:21

    return () => clearTimeout(timeoutId);
  }, []);

  useEffect(() => {
    const timeoutId2 = setTimeout(() => {
      setShowUpdateModal(true);
    }, 9000000); // -> 1:30

    return () => clearTimeout(timeoutId2);
  }, []);

  const handleReloadConfirmation = (reload: any) => {
    if (reload) {
      window.location.reload();
    } else {
      setShowWarningModal(false);
    }
  };

  const handleUpdateConfirmation = (update: any) => {
    if (update) {
      setShouldReload(true);
      window.location.reload();
    } else {
      setShowUpdateModal(true);
    }
  };

  return (
    <>
      {/* Modal de advertencia de reinicio */}
      {showWarningModal && (
        <Modal show={showWarningModal} id="modalAdvertenciaDeReinicio" className='modal modalAdvertenciaDeReinicio' aria-labelledby="contained-modal-title-vcenter" centered>
          <Modal.Header className="d-flex justify-content-between">
            <Modal.Title></Modal.Title>
          </Modal.Header>
          <Modal.Body>
            "En 10 minutos excederá el tiempo recomendado para hacer una actualización técnica y de contenido de la Bandeja de Aprobación"
            <h6 className='my-2'>¿Quiere actualizar?</h6>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="primary" onClick={() => handleReloadConfirmation(true)}>SI</Button>
            <Button variant="primary" onClick={() => handleReloadConfirmation(false)}>NO</Button>
          </Modal.Footer>
        </Modal>)}
      {showUpdateModal && (
        <Modal show={showUpdateModal} id="modalReinicio" className='modal modalReinicio' aria-labelledby="contained-modal-title-vcenter" centered>
          <Modal.Header className="d-flex justify-content-between">
            <Modal.Title>Aviso de actualización</Modal.Title>
          </Modal.Header>
          <Modal.Body>Por recomendaciones técnicas se estará actualizando...</Modal.Body>
          <Modal.Footer>
            <Button variant="primary" onClick={() => handleUpdateConfirmation(true)}>Aceptar</Button>
          </Modal.Footer>
        </Modal>)}
    </>
  );
};

ReactDOM.render(<App />, document.getElementById('container'));
injectStyle();