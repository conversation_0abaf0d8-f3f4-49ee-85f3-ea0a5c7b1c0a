import EndPoint from "./EndPoint.Config";
import axios from "axios";
import { getAmbienteContexto, getUrlSPBase } from "../utilities/contextInfo";
import { idUsuariosSitios } from "./SPsitiosOBJ";

'use strict'

const Params =
{
  urlBase: getUrlSPBase(),
  ambiente: getAmbienteContexto(),
  request: axios.create({}),
  endpoints: EndPoint.apiRestSP
}

export const getUserIdSP = async (user: string, siteUrl: string) => {
  let _endpoint = Params.endpoints.siteUser

  const config = {
    method: 'get',
    url: siteUrl + _endpoint + encodeURIComponent(user) + "'",
    headers: { "Accept": "application/json; odata=verbose" }
  };

  let userID
  let Promise = Params.request<any, any>(config)
    .then(function (response) {
      userID = response.data.d.Id;
      console.log('ver User Id')
      console.log(userID)
      return userID
    })
    .catch(function (error) {
      console.log(error);
    });

  let result = await Promise;

  return result
}

export const getListadoMisTareas = async (siteCod: number, lista: string) => {
  const _strSiteCod = siteCod.toString()
  const userID = localStorage.getItem('idSPuserPerSite' + _strSiteCod)
  const _siteURL = getUrlSPBase() + idUsuariosSitios.find((element) => element.codProceso === siteCod).site
  const _url = _siteURL + "_api/web/lists/GetByTitle('" + lista + "')/items?$filter=" + "(Status eq 'No iniciada' or Status eq 'En curso') and AssignedTo eq " + userID + " " + " &$top=2000000000 &@target='" + _siteURL + "'"
  const config = {
    method: 'get',
    url: _url,
    headers: {
      "Accept": "application/json; odata=verbose",
      "content-Type": "application/json;odata=verbose"
    }
  };

  let listado
  let Promise = Params.request<any, any>(config)
    .then(function (response) {
      listado = response.data.d.results;
      return listado
    })
    .catch(function (error) {
      console.log(error);
    });

  let result = await Promise;

  return result

}

export const getTarea = async (siteCod: number, lista: string, id: string) => {
  const _strSiteCod = siteCod.toString()
  const userID = localStorage.getItem('idSPuserPerSite' + _strSiteCod)
  const _siteURL = getUrlSPBase() + idUsuariosSitios.find((element) => element.codProceso === siteCod).site
  const _url = _siteURL + "_api/web/lists/GetByTitle('" + lista + "')/items?$filter=" + "(Status eq 'No iniciada' or Status eq 'En curso')and ID eq " + id + " and AssignedTo eq " + userID + " " + " &$top=2000000000 &@target='" + _siteURL + "'"

  const config = {
    method: 'get',
    url: _url,
    headers: {
      "Accept": "application/json; odata=verbose",
      "content-Type": "application/json;odata=verbose"
    }
  };

  let listado
  let Promise = Params.request<any, any>(config)
    .then(function (response) {
      listado = response.data.d.results;
      console.log('ver listado')
      console.log(listado)
      console.log('unverListado')
      return listado
    })
    .catch(function (error) {
      console.log(error);
    });

  let result = await Promise;

  return result

}

