import { Ambiente } from "../entities/Enums/enumAmbientes";
import { getAmbienteContexto } from "../utilities/contextInfo";
import { getJWTToken } from "../utilities/getJWTToken";

export const API_CONFIG_DESA = {
    SUBSCRIPTION_KEY: '47a1892f6b294f70b669d3baaa075dc5',
    CLIENT_SECRET: 'ad97a622-c001-4bc4-830d-c6490c2cb9e7',
};

export const API_CONFIG_PROD = {
    SUBSCRIPTION_KEY: '2966f3b11c15480cb45c4aacb2f8965e',
    CLIENT_SECRET: 'ad97a622-c001-4bc4-830d-c6490c2cb9e7',
};

export type PrivilegeHeaders = {
    'Authorization': string,
    'Subscription-Key': string,
}

export async function getPrivilegeHeaders() {
    let _headers: PrivilegeHeaders;
    let _ambiente = getAmbienteContexto();
    let _jwtToken = await getJWTToken('internal');

    switch (_ambiente) {
        case Ambiente.dev:
            _headers =
            {
                'Authorization': 'Bearer ' + _jwtToken,
                'Subscription-Key': API_CONFIG_DESA.SUBSCRIPTION_KEY,
            }
            return _headers;

        case Ambiente.prod:
            _headers = {
                'Authorization': 'Bearer ' + _jwtToken,
                'Subscription-Key': API_CONFIG_PROD.SUBSCRIPTION_KEY,
            }
            return _headers;
    }
}