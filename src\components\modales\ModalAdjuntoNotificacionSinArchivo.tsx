import React from 'react';
import { <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap';

interface ModalProps {
  abrir: boolean;
  cerrar: () => void;
}

const ModalAdjuntoNotificacionSinArchivo: React.FC<ModalProps> = ({ abrir, cerrar }) => {
  return (
    <div>
      <Modal show={abrir} onHide={cerrar}>
        <Modal.Header>
          <Modal.Title className='text-secondary'></Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p className='text-center font-weight-bold text-danger'>No se ha cargado ningún archivo.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={cerrar}>Cerrar</Button>
        </Modal.Footer>
      </Modal>
    </div>
  )
}

export default ModalAdjuntoNotificacionSinArchivo