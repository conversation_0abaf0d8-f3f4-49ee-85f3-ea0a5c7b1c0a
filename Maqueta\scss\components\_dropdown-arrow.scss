/*
 
 ██████╗ ██████╗  ██████╗ ██████╗ ██████╗  ██████╗ ██╗    ██╗███╗   ██╗ █████╗ ██████╗ ██████╗  ██████╗ ██╗    ██╗
 ██╔══██╗██╔══██╗██╔═══██╗██╔══██╗██╔══██╗██╔═══██╗██║    ██║████╗  ██║██╔══██╗██╔══██╗██╔══██╗██╔═══██╗██║    ██║
 ██║  ██║██████╔╝██║   ██║██████╔╝██║  ██║██║   ██║██║ █╗ ██║██╔██╗ ██║███████║██████╔╝██████╔╝██║   ██║██║ █╗ ██║
 ██║  ██║██╔══██╗██║   ██║██╔═══╝ ██║  ██║██║   ██║██║███╗██║██║╚██╗██║██╔══██║██╔══██╗██╔══██╗██║   ██║██║███╗██║
 ██████╔╝██║  ██║╚██████╔╝██║     ██████╔╝╚██████╔╝╚███╔███╔╝██║ ╚████║██║  ██║██║  ██║██║  ██║╚██████╔╝╚███╔███╔╝
 ╚═════╝ ╚═╝  ╚═╝ ╚═════╝ ╚═╝     ╚═════╝  ╚═════╝  ╚══╝╚══╝ ╚═╝  ╚═══╝╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝  ╚══╝╚══╝ 
 Estilos propios para el arrow                                                                                                                  
 
*/

@include block('dropdown-arrow') {
    
    position: relative;

    &:before {
        border-bottom: 9px solid rgba(0, 0, 0, 0.2);
        border-left: 9px solid rgba(0, 0, 0, 0);
        border-right: 9px solid rgba(0, 0, 0, 0);
        content: "";
        display: inline-block;
        left: 5%; /* position */
        position: absolute;
        top: -8px;
    }

    &:after {
        border-bottom: 8px solid #FFFFFF;
        border-left: 9px solid rgba(0, 0, 0, 0);
        border-right: 9px solid rgba(0, 0, 0, 0);
        content: "";
        display: inline-block;
        left: 5%; /* position */
        position: absolute;
        top: -7px;
    }

    @include modifier('right') {
        &:before,
        &:after { left: 85%; }
    }


}
