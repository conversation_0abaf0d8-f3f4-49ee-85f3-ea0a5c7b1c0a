{"version": 3, "file": "button.js", "sources": ["../src/button.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.4.1): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME                = 'button'\nconst VERSION             = '4.4.1'\nconst DATA_KEY            = 'bs.button'\nconst EVENT_KEY           = `.${DATA_KEY}`\nconst DATA_API_KEY        = '.data-api'\nconst JQUERY_NO_CONFLICT  = $.fn[NAME]\n\nconst ClassName = {\n  ACTIVE : 'active',\n  BUTTON : 'btn',\n  FOCUS  : 'focus'\n}\n\nconst Selector = {\n  DATA_TOGGLE_CARROT   : '[data-toggle^=\"button\"]',\n  DATA_TOGGLES         : '[data-toggle=\"buttons\"]',\n  DATA_TOGGLE          : '[data-toggle=\"button\"]',\n  DATA_TOGGLES_BUTTONS : '[data-toggle=\"buttons\"] .btn',\n  INPUT                : 'input:not([type=\"hidden\"])',\n  ACTIVE               : '.active',\n  BUTTON               : '.btn'\n}\n\nconst Event = {\n  CLICK_DATA_API      : `click${EVENT_KEY}${DATA_API_KEY}`,\n  FOCUS_BLUR_DATA_API : `focus${EVENT_KEY}${DATA_API_KEY} ` +\n                          `blur${EVENT_KEY}${DATA_API_KEY}`,\n  LOAD_DATA_API       : `load${EVENT_KEY}${DATA_API_KEY}`\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Button {\n  constructor(element) {\n    this._element = element\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  toggle() {\n    let triggerChangeEvent = true\n    let addAriaPressed = true\n    const rootElement = $(this._element).closest(\n      Selector.DATA_TOGGLES\n    )[0]\n\n    if (rootElement) {\n      const input = this._element.querySelector(Selector.INPUT)\n\n      if (input) {\n        if (input.type === 'radio') {\n          if (input.checked &&\n            this._element.classList.contains(ClassName.ACTIVE)) {\n            triggerChangeEvent = false\n          } else {\n            const activeElement = rootElement.querySelector(Selector.ACTIVE)\n\n            if (activeElement) {\n              $(activeElement).removeClass(ClassName.ACTIVE)\n            }\n          }\n        } else if (input.type === 'checkbox') {\n          if (this._element.tagName === 'LABEL' && input.checked === this._element.classList.contains(ClassName.ACTIVE)) {\n            triggerChangeEvent = false\n          }\n        } else {\n          // if it's not a radio button or checkbox don't add a pointless/invalid checked property to the input\n          triggerChangeEvent = false\n        }\n\n        if (triggerChangeEvent) {\n          input.checked = !this._element.classList.contains(ClassName.ACTIVE)\n          $(input).trigger('change')\n        }\n\n        input.focus()\n        addAriaPressed = false\n      }\n    }\n\n    if (!(this._element.hasAttribute('disabled') || this._element.classList.contains('disabled'))) {\n      if (addAriaPressed) {\n        this._element.setAttribute('aria-pressed',\n          !this._element.classList.contains(ClassName.ACTIVE))\n      }\n\n      if (triggerChangeEvent) {\n        $(this._element).toggleClass(ClassName.ACTIVE)\n      }\n    }\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n\n      if (!data) {\n        data = new Button(this)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document)\n  .on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE_CARROT, (event) => {\n    let button = event.target\n\n    if (!$(button).hasClass(ClassName.BUTTON)) {\n      button = $(button).closest(Selector.BUTTON)[0]\n    }\n\n    if (!button || button.hasAttribute('disabled') || button.classList.contains('disabled')) {\n      event.preventDefault() // work around Firefox bug #1540995\n    } else {\n      const inputBtn = button.querySelector(Selector.INPUT)\n\n      if (inputBtn && (inputBtn.hasAttribute('disabled') || inputBtn.classList.contains('disabled'))) {\n        event.preventDefault() // work around Firefox bug #1540995\n        return\n      }\n\n      Button._jQueryInterface.call($(button), 'toggle')\n    }\n  })\n  .on(Event.FOCUS_BLUR_DATA_API, Selector.DATA_TOGGLE_CARROT, (event) => {\n    const button = $(event.target).closest(Selector.BUTTON)[0]\n    $(button).toggleClass(ClassName.FOCUS, /^focus(in)?$/.test(event.type))\n  })\n\n$(window).on(Event.LOAD_DATA_API, () => {\n  // ensure correct active class is set to match the controls' actual values/states\n\n  // find all checkboxes/readio buttons inside data-toggle groups\n  let buttons = [].slice.call(document.querySelectorAll(Selector.DATA_TOGGLES_BUTTONS))\n  for (let i = 0, len = buttons.length; i < len; i++) {\n    const button = buttons[i]\n    const input = button.querySelector(Selector.INPUT)\n    if (input.checked || input.hasAttribute('checked')) {\n      button.classList.add(ClassName.ACTIVE)\n    } else {\n      button.classList.remove(ClassName.ACTIVE)\n    }\n  }\n\n  // find all button toggles\n  buttons = [].slice.call(document.querySelectorAll(Selector.DATA_TOGGLE))\n  for (let i = 0, len = buttons.length; i < len; i++) {\n    const button = buttons[i]\n    if (button.getAttribute('aria-pressed') === 'true') {\n      button.classList.add(ClassName.ACTIVE)\n    } else {\n      button.classList.remove(ClassName.ACTIVE)\n    }\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Button._jQueryInterface\n$.fn[NAME].Constructor = Button\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Button._jQueryInterface\n}\n\nexport default Button\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "ClassName", "ACTIVE", "BUTTON", "FOCUS", "Selector", "DATA_TOGGLE_CARROT", "DATA_TOGGLES", "DATA_TOGGLE", "DATA_TOGGLES_BUTTONS", "INPUT", "Event", "CLICK_DATA_API", "FOCUS_BLUR_DATA_API", "LOAD_DATA_API", "<PERSON><PERSON>", "element", "_element", "toggle", "triggerChangeEvent", "addAriaPressed", "rootElement", "closest", "input", "querySelector", "type", "checked", "classList", "contains", "activeElement", "removeClass", "tagName", "trigger", "focus", "hasAttribute", "setAttribute", "toggleClass", "dispose", "removeData", "_jQueryInterface", "config", "each", "data", "document", "on", "event", "button", "target", "hasClass", "preventDefault", "inputBtn", "call", "test", "window", "buttons", "slice", "querySelectorAll", "i", "len", "length", "add", "remove", "getAttribute", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;EASA;;;;;;EAMA,IAAMA,IAAI,GAAkB,QAA5B;EACA,IAAMC,OAAO,GAAe,OAA5B;EACA,IAAMC,QAAQ,GAAc,WAA5B;EACA,IAAMC,SAAS,SAAiBD,QAAhC;EACA,IAAME,YAAY,GAAU,WAA5B;EACA,IAAMC,kBAAkB,GAAIC,CAAC,CAACC,EAAF,CAAKP,IAAL,CAA5B;EAEA,IAAMQ,SAAS,GAAG;EAChBC,EAAAA,MAAM,EAAG,QADO;EAEhBC,EAAAA,MAAM,EAAG,KAFO;EAGhBC,EAAAA,KAAK,EAAI;EAHO,CAAlB;EAMA,IAAMC,QAAQ,GAAG;EACfC,EAAAA,kBAAkB,EAAK,yBADR;EAEfC,EAAAA,YAAY,EAAW,yBAFR;EAGfC,EAAAA,WAAW,EAAY,wBAHR;EAIfC,EAAAA,oBAAoB,EAAG,8BAJR;EAKfC,EAAAA,KAAK,EAAkB,4BALR;EAMfR,EAAAA,MAAM,EAAiB,SANR;EAOfC,EAAAA,MAAM,EAAiB;EAPR,CAAjB;EAUA,IAAMQ,KAAK,GAAG;EACZC,EAAAA,cAAc,YAAgBhB,SAAhB,GAA4BC,YAD9B;EAEZgB,EAAAA,mBAAmB,EAAG,UAAQjB,SAAR,GAAoBC,YAApB,mBACSD,SADT,GACqBC,YADrB,CAFV;EAIZiB,EAAAA,aAAa,WAAgBlB,SAAhB,GAA4BC;EAJ7B,CAAd;EAOA;;;;;;MAMMkB;;;EACJ,kBAAYC,OAAZ,EAAqB;EACnB,SAAKC,QAAL,GAAgBD,OAAhB;EACD;;;;;EAQD;WAEAE,SAAA,kBAAS;EACP,QAAIC,kBAAkB,GAAG,IAAzB;EACA,QAAIC,cAAc,GAAG,IAArB;EACA,QAAMC,WAAW,GAAGtB,CAAC,CAAC,KAAKkB,QAAN,CAAD,CAAiBK,OAAjB,CAClBjB,QAAQ,CAACE,YADS,EAElB,CAFkB,CAApB;;EAIA,QAAIc,WAAJ,EAAiB;EACf,UAAME,KAAK,GAAG,KAAKN,QAAL,CAAcO,aAAd,CAA4BnB,QAAQ,CAACK,KAArC,CAAd;;EAEA,UAAIa,KAAJ,EAAW;EACT,YAAIA,KAAK,CAACE,IAAN,KAAe,OAAnB,EAA4B;EAC1B,cAAIF,KAAK,CAACG,OAAN,IACF,KAAKT,QAAL,CAAcU,SAAd,CAAwBC,QAAxB,CAAiC3B,SAAS,CAACC,MAA3C,CADF,EACsD;EACpDiB,YAAAA,kBAAkB,GAAG,KAArB;EACD,WAHD,MAGO;EACL,gBAAMU,aAAa,GAAGR,WAAW,CAACG,aAAZ,CAA0BnB,QAAQ,CAACH,MAAnC,CAAtB;;EAEA,gBAAI2B,aAAJ,EAAmB;EACjB9B,cAAAA,CAAC,CAAC8B,aAAD,CAAD,CAAiBC,WAAjB,CAA6B7B,SAAS,CAACC,MAAvC;EACD;EACF;EACF,SAXD,MAWO,IAAIqB,KAAK,CAACE,IAAN,KAAe,UAAnB,EAA+B;EACpC,cAAI,KAAKR,QAAL,CAAcc,OAAd,KAA0B,OAA1B,IAAqCR,KAAK,CAACG,OAAN,KAAkB,KAAKT,QAAL,CAAcU,SAAd,CAAwBC,QAAxB,CAAiC3B,SAAS,CAACC,MAA3C,CAA3D,EAA+G;EAC7GiB,YAAAA,kBAAkB,GAAG,KAArB;EACD;EACF,SAJM,MAIA;EACL;EACAA,UAAAA,kBAAkB,GAAG,KAArB;EACD;;EAED,YAAIA,kBAAJ,EAAwB;EACtBI,UAAAA,KAAK,CAACG,OAAN,GAAgB,CAAC,KAAKT,QAAL,CAAcU,SAAd,CAAwBC,QAAxB,CAAiC3B,SAAS,CAACC,MAA3C,CAAjB;EACAH,UAAAA,CAAC,CAACwB,KAAD,CAAD,CAASS,OAAT,CAAiB,QAAjB;EACD;;EAEDT,QAAAA,KAAK,CAACU,KAAN;EACAb,QAAAA,cAAc,GAAG,KAAjB;EACD;EACF;;EAED,QAAI,EAAE,KAAKH,QAAL,CAAciB,YAAd,CAA2B,UAA3B,KAA0C,KAAKjB,QAAL,CAAcU,SAAd,CAAwBC,QAAxB,CAAiC,UAAjC,CAA5C,CAAJ,EAA+F;EAC7F,UAAIR,cAAJ,EAAoB;EAClB,aAAKH,QAAL,CAAckB,YAAd,CAA2B,cAA3B,EACE,CAAC,KAAKlB,QAAL,CAAcU,SAAd,CAAwBC,QAAxB,CAAiC3B,SAAS,CAACC,MAA3C,CADH;EAED;;EAED,UAAIiB,kBAAJ,EAAwB;EACtBpB,QAAAA,CAAC,CAAC,KAAKkB,QAAN,CAAD,CAAiBmB,WAAjB,CAA6BnC,SAAS,CAACC,MAAvC;EACD;EACF;EACF;;WAEDmC,UAAA,mBAAU;EACRtC,IAAAA,CAAC,CAACuC,UAAF,CAAa,KAAKrB,QAAlB,EAA4BtB,QAA5B;EACA,SAAKsB,QAAL,GAAgB,IAAhB;EACD;;;WAIMsB,mBAAP,0BAAwBC,MAAxB,EAAgC;EAC9B,WAAO,KAAKC,IAAL,CAAU,YAAY;EAC3B,UAAIC,IAAI,GAAG3C,CAAC,CAAC,IAAD,CAAD,CAAQ2C,IAAR,CAAa/C,QAAb,CAAX;;EAEA,UAAI,CAAC+C,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAI3B,MAAJ,CAAW,IAAX,CAAP;EACAhB,QAAAA,CAAC,CAAC,IAAD,CAAD,CAAQ2C,IAAR,CAAa/C,QAAb,EAAuB+C,IAAvB;EACD;;EAED,UAAIF,MAAM,KAAK,QAAf,EAAyB;EACvBE,QAAAA,IAAI,CAACF,MAAD,CAAJ;EACD;EACF,KAXM,CAAP;EAYD;;;;0BA/EoB;EACnB,aAAO9C,OAAP;EACD;;;;;EAgFH;;;;;;;EAMAK,CAAC,CAAC4C,QAAD,CAAD,CACGC,EADH,CACMjC,KAAK,CAACC,cADZ,EAC4BP,QAAQ,CAACC,kBADrC,EACyD,UAACuC,KAAD,EAAW;EAChE,MAAIC,MAAM,GAAGD,KAAK,CAACE,MAAnB;;EAEA,MAAI,CAAChD,CAAC,CAAC+C,MAAD,CAAD,CAAUE,QAAV,CAAmB/C,SAAS,CAACE,MAA7B,CAAL,EAA2C;EACzC2C,IAAAA,MAAM,GAAG/C,CAAC,CAAC+C,MAAD,CAAD,CAAUxB,OAAV,CAAkBjB,QAAQ,CAACF,MAA3B,EAAmC,CAAnC,CAAT;EACD;;EAED,MAAI,CAAC2C,MAAD,IAAWA,MAAM,CAACZ,YAAP,CAAoB,UAApB,CAAX,IAA8CY,MAAM,CAACnB,SAAP,CAAiBC,QAAjB,CAA0B,UAA1B,CAAlD,EAAyF;EACvFiB,IAAAA,KAAK,CAACI,cAAN,GADuF;EAExF,GAFD,MAEO;EACL,QAAMC,QAAQ,GAAGJ,MAAM,CAACtB,aAAP,CAAqBnB,QAAQ,CAACK,KAA9B,CAAjB;;EAEA,QAAIwC,QAAQ,KAAKA,QAAQ,CAAChB,YAAT,CAAsB,UAAtB,KAAqCgB,QAAQ,CAACvB,SAAT,CAAmBC,QAAnB,CAA4B,UAA5B,CAA1C,CAAZ,EAAgG;EAC9FiB,MAAAA,KAAK,CAACI,cAAN,GAD8F;;EAE9F;EACD;;EAEDlC,IAAAA,MAAM,CAACwB,gBAAP,CAAwBY,IAAxB,CAA6BpD,CAAC,CAAC+C,MAAD,CAA9B,EAAwC,QAAxC;EACD;EACF,CApBH,EAqBGF,EArBH,CAqBMjC,KAAK,CAACE,mBArBZ,EAqBiCR,QAAQ,CAACC,kBArB1C,EAqB8D,UAACuC,KAAD,EAAW;EACrE,MAAMC,MAAM,GAAG/C,CAAC,CAAC8C,KAAK,CAACE,MAAP,CAAD,CAAgBzB,OAAhB,CAAwBjB,QAAQ,CAACF,MAAjC,EAAyC,CAAzC,CAAf;EACAJ,EAAAA,CAAC,CAAC+C,MAAD,CAAD,CAAUV,WAAV,CAAsBnC,SAAS,CAACG,KAAhC,EAAuC,eAAegD,IAAf,CAAoBP,KAAK,CAACpB,IAA1B,CAAvC;EACD,CAxBH;EA0BA1B,CAAC,CAACsD,MAAD,CAAD,CAAUT,EAAV,CAAajC,KAAK,CAACG,aAAnB,EAAkC,YAAM;EACtC;EAEA;EACA,MAAIwC,OAAO,GAAG,GAAGC,KAAH,CAASJ,IAAT,CAAcR,QAAQ,CAACa,gBAAT,CAA0BnD,QAAQ,CAACI,oBAAnC,CAAd,CAAd;;EACA,OAAK,IAAIgD,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGJ,OAAO,CAACK,MAA9B,EAAsCF,CAAC,GAAGC,GAA1C,EAA+CD,CAAC,EAAhD,EAAoD;EAClD,QAAMX,MAAM,GAAGQ,OAAO,CAACG,CAAD,CAAtB;EACA,QAAMlC,KAAK,GAAGuB,MAAM,CAACtB,aAAP,CAAqBnB,QAAQ,CAACK,KAA9B,CAAd;;EACA,QAAIa,KAAK,CAACG,OAAN,IAAiBH,KAAK,CAACW,YAAN,CAAmB,SAAnB,CAArB,EAAoD;EAClDY,MAAAA,MAAM,CAACnB,SAAP,CAAiBiC,GAAjB,CAAqB3D,SAAS,CAACC,MAA/B;EACD,KAFD,MAEO;EACL4C,MAAAA,MAAM,CAACnB,SAAP,CAAiBkC,MAAjB,CAAwB5D,SAAS,CAACC,MAAlC;EACD;EACF,GAbqC;;;EAgBtCoD,EAAAA,OAAO,GAAG,GAAGC,KAAH,CAASJ,IAAT,CAAcR,QAAQ,CAACa,gBAAT,CAA0BnD,QAAQ,CAACG,WAAnC,CAAd,CAAV;;EACA,OAAK,IAAIiD,EAAC,GAAG,CAAR,EAAWC,IAAG,GAAGJ,OAAO,CAACK,MAA9B,EAAsCF,EAAC,GAAGC,IAA1C,EAA+CD,EAAC,EAAhD,EAAoD;EAClD,QAAMX,OAAM,GAAGQ,OAAO,CAACG,EAAD,CAAtB;;EACA,QAAIX,OAAM,CAACgB,YAAP,CAAoB,cAApB,MAAwC,MAA5C,EAAoD;EAClDhB,MAAAA,OAAM,CAACnB,SAAP,CAAiBiC,GAAjB,CAAqB3D,SAAS,CAACC,MAA/B;EACD,KAFD,MAEO;EACL4C,MAAAA,OAAM,CAACnB,SAAP,CAAiBkC,MAAjB,CAAwB5D,SAAS,CAACC,MAAlC;EACD;EACF;EACF,CAzBD;EA2BA;;;;;;EAMAH,CAAC,CAACC,EAAF,CAAKP,IAAL,IAAasB,MAAM,CAACwB,gBAApB;EACAxC,CAAC,CAACC,EAAF,CAAKP,IAAL,EAAWsE,WAAX,GAAyBhD,MAAzB;;EACAhB,CAAC,CAACC,EAAF,CAAKP,IAAL,EAAWuE,UAAX,GAAwB,YAAM;EAC5BjE,EAAAA,CAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb;EACA,SAAOiB,MAAM,CAACwB,gBAAd;EACD,CAHD;;;;;;;;"}