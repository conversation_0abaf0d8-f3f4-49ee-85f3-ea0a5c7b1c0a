import axios, { AxiosResponse } from "axios";
import { Operacion } from "../entities/Operacion";
import { Rol } from "../entities/Rol";
import { getAmbienteContexto, getUrlBase } from "../utilities/contextInfo";
import { concatFilters } from "../utilities/ParsersTS";
import { getApiBpmPoliticasProcesosRequestHeaders, getApiProveedorFacturaHeaders } from "./contracts/ApiBpmPoliticasProcesosRequestHeaders";
import { DocumentoAdjuntoResponse } from "./contracts/DocumentoAdjuntoResponse";
import { ErrorResponse } from "./contracts/ErrorResponse";
import { MenuElementoResponse } from "./contracts/GetMenuElementosResponse";
import { InicioSeguimientoRequest } from "./contracts/InicioSeguimientoRequest";
import { InicioSeguimientoSuccess } from "./contracts/InicioSeguimientoSuccess";
import { GetAccionGeneradaResponse } from "./contracts/GetAccionGeneradaResponse";
import EndPoint from "./EndPoint.Config";
import { PutResponderAccionBody } from "./contracts/PutResponderAccion";
import { GetDatosAccionesResponse } from "./contracts/GetDatosAccionesResponse";
import { InsertAccionGeneradaBody } from "./contracts/InsertAccionGeneradaBody";
import { InsertAccionDatosBody } from "./contracts/InsertAccionDatosBody";
import { InsertAccionDatosSuccess } from "./contracts/InsertAccionDatosSuccess";
import { OperacionClienteProps } from "../entities/OperacionClienteProps";

type EstadoPorAcciones = {
  idCabecera: string;
  codEstado: string;
}

//config
const Params =
{
  urlBase: getUrlBase(),
  ambiente: getAmbienteContexto(),
  request: axios.create({}),
  endpoints: EndPoint.apiBpmPoliticaProcesos
}

//roles
export async function getRolesdeUsuario(usuario: string) {
  let data;
  let _cacheControl = 'max-age=60*60*10, private'
  let _headers = await getApiBpmPoliticasProcesosRequestHeaders(_cacheControl)
  let strURL = Params.endpoints.vUno.get.rolesPorUsuario
  let urlEndpoint = strURL.replace('${usuario}', usuario)

  let config =
  {
    method: 'get',
    url: Params.urlBase + urlEndpoint,
    headers: _headers,
    data: data
  }

  let fPromise = Params.request<Rol[]>(config)
    .then(function (response) {
      console.log("response.data getRolesdeUsuario:", response.data);
      return response.data
    })
    .catch(function (error) {
      console.log(error)
    })

  let result = await fPromise;

  return result;

}

//bpmMenu
export async function getMenuElementos(codigoElemento?: string, codigoRol?: string, codigoUsuario?: string) {
  let _cacheControl = 'max-age=60*60*10, private';
  let _headers = await getApiBpmPoliticasProcesosRequestHeaders(_cacheControl)
  let _sturlEndpoint = Params.endpoints.vUno.get.menuElementos

  var config = {
    method: 'get',
    url: Params.urlBase + _sturlEndpoint,
    headers: _headers
  };

  console.log("config menu: ", config);

  let Promise = Params.request<MenuElementoResponse>(config)
    .then(function (response) {
      console.log("response getMenuElementos:", response);
      return response.data
    })
    .catch(function (error) {
      console.log(error);
    });

  let result = await Promise;

  return result
}

//OperacionCabecera
export const getOperacionesCabecera = async (proceso: string, estado?: string, idOperacion?: string, usuario?: string, soloPrimario?: boolean, codigoRol?: string, valorAtributo?: string) => {
  console.time("getOperacionesCabecera");
  const _headers = await getApiBpmPoliticasProcesosRequestHeaders('');
  const _sturlEndpoint = Params.endpoints.vUno.get.operaciones
  let _urlEndpoint = _sturlEndpoint.replace('${proceso}', proceso)

  const _filters = [
    (idOperacion != undefined) ? 'idOperacion=' + idOperacion : '',
    (estado != undefined) ? 'estado=' + estado : '',
    (usuario != undefined) ? 'usuario=' + usuario : '',
    (soloPrimario != undefined) ? 'soloPrimario=' + soloPrimario : '',
    (codigoRol != undefined) ? 'codigoRol=' + codigoRol : '',
  ]

  console.log('filtros: ', _filters)
  let _filtersUrl: string

  if (!_filters.every(element => { element === '' })) {
    _filtersUrl = concatFilters(_filters)
  }

  const config = {
    method: 'get',
    url: Params.urlBase + _urlEndpoint + _filtersUrl,
    headers: _headers
  };

  console.log("config getOperacionesCabecera:", config);
  console.log("config.url getOperacionesCabecera:", config.url);

  let promise = Params.request<Operacion[]>(config)
    .then(function (response) {
      console.log("Response getOperacionesCabecera:", response);
      return response.data
    })
    .catch(function (error) {
      console.log("error promise getOperacionesCabecera:", error);
    });

  let result = await promise;

  console.timeEnd("getOperacionesCabecera");

  return result
}


export const postOperacionCabecera = async (body: InicioSeguimientoRequest) => {
  const _headers = await getApiBpmPoliticasProcesosRequestHeaders('')
  const _sturlEndpoint = Params.endpoints.vUno.post.inicioSeguimiento
  const config = {
    method: 'post',
    url: Params.urlBase + _sturlEndpoint,
    headers: _headers
  };
  const promise = Params.request.post<any, AxiosResponse<InicioSeguimientoSuccess, ErrorResponse>>(config.url, body, config)
    .then(function (response) {
      return response.data
    })
    .catch(function (error) {
      console.log(error);
      return error.response
    });

  const result = await promise

  return result
}

//Adjuntos

export const getListadoAdjuntos = async (idTipoDocumento: number, idOperacion: string) => {
  try {
    const _cacheControl = 'max-age=60*60*10, private';
    const _headers = await getApiBpmPoliticasProcesosRequestHeaders(_cacheControl);
    const _sturlEndpoint = Params.endpoints.vUno.get.listadoAdjuntos;
    let urlEndpoint = _sturlEndpoint.replace('${idTipoDocumento}', idTipoDocumento.toString());
    urlEndpoint = urlEndpoint.replace('${operacion}', idOperacion);

    console.log("urlEndpoint getListadoAdjuntos:", urlEndpoint);

    const config = {
      method: 'get',
      url: Params.urlBase + urlEndpoint,
      headers: _headers
    };

    const response = await Params.request<DocumentoAdjuntoResponse[]>(config);
    console.log("response.data getListadoAdjuntos:", response.data);

    return response.data;

  } catch (error) {
    console.error("Error en getListadoAdjuntos:", error);
    throw error;
  }
};

//Acciones

export const ResponderAccion = async (body: PutResponderAccionBody) => {
  const _headers = await getApiBpmPoliticasProcesosRequestHeaders('')
  const _sturlEndpoint = Params.endpoints.vUno.put.responderTarea
  const config = {
    method: 'put',
    url: Params.urlBase + _sturlEndpoint,
    headers: _headers
  };
  const promise = Params.request.put<any, AxiosResponse<{ id: string }, {
    errorType: 0,
    errorDescription: string
  }>>(config.url, body, config)
    .then(function (response) {
      console.log("ResponderAccion put response:", response);
      let resDataIdResponderAccion = response.data.id;
      localStorage.setItem("resDataIdResponderAccion", resDataIdResponderAccion);
      return response.data
    })
    .catch(function (error) {
      console.log(error);
      return error.response
    });

  const result = await promise

  return result
}

export const GetAccionGeneradas = async (codCabecera: string, codEstado: string) => {
  const _headers = await getApiBpmPoliticasProcesosRequestHeaders('')
  const _sturlEndpoint = Params.endpoints.vUno.get.accionesPorEstado
  let _urlEndpoint = _sturlEndpoint.replace('${idCabecera}', codCabecera)
  _urlEndpoint = _urlEndpoint.replace('${codEstado}', codEstado)

  var config = {
    method: 'get',
    url: Params.urlBase + _urlEndpoint,
    headers: _headers
  };

  let Promise = Params.request<GetAccionGeneradaResponse[]>(config)
    .then(function (response) {
      return response.data
    })
    .catch(function (error) {
      console.log(error);
    });

  let result = Promise;

  return result
}

export const GetDatosAcciones = async (codAccionGenerada: string, codAtributo: string) => {
  const _headers = await getApiBpmPoliticasProcesosRequestHeaders('')
  const _sturlEndpoint = Params.endpoints.vUno.get.datoAcciones
  let _urlEndpoint = _sturlEndpoint.replace('${idAccionGenerada}', codAccionGenerada)
  _urlEndpoint = _urlEndpoint.replace('${codAtributo}', codAtributo)

  var config = {
    method: 'get',
    url: Params.urlBase + _urlEndpoint,
    headers: _headers
  };

  let Promise = Params.request<GetDatosAccionesResponse[]>(config)
    .then(function (response) {
      console.log('get datos response')
      console.log(response)
      return response.data
    })
    .catch(function (error) {
      console.log(error);
    });

  let result = Promise;

  return result
}

export const postAccionGenerada = async (body: InsertAccionGeneradaBody) => {
  const _headers = await getApiBpmPoliticasProcesosRequestHeaders('')
  const _sturlEndpoint = Params.endpoints.vUno.post.insertarAccionGenerada
  const config = {
    method: 'post',
    url: Params.urlBase + _sturlEndpoint,
    headers: _headers
  };
  const promise = Params.request.post<any, AxiosResponse<InicioSeguimientoSuccess, ErrorResponse>>(config.url, body, config)
    .then(function (response) {
      console.log("response.data postAccionGenerada:", response.data);
      return response.data
    })
    .catch(function (error) {
      console.log("error catch postAccionGenerada:", error);
      return error.response
    });

  const result = await promise

  return result
}

export const postAccionDatos = async (body: InsertAccionDatosBody) => {
  const _headers = await getApiBpmPoliticasProcesosRequestHeaders('');
  const _sturlEndpoint = Params.endpoints.vUno.post.accionDatos;
  const config = {
    method: 'post',
    url: Params.urlBase + _sturlEndpoint,
    headers: _headers
  };

  const promise = Params.request.post<any, AxiosResponse<InsertAccionDatosSuccess, ErrorResponse>>(config.url, body, config)
    .then(function (response) {
      console.log("response postAccionDatos:", response);
      return response.data
    })
    .catch(function (error) {
      console.log("error catch postAccionDatos:", error);
      return error.response
    });

  const result = await promise

  return result
}

export const obtenerOperacion = async (codigoFiltro: any) => {
  let _cacheControl = 'max-age=60*60*10, private';
  let _headers = await getApiProveedorFacturaHeaders(_cacheControl);
  let _sturlEndpoint = Params.endpoints.vUno.get.operaciones;
  let urlEndpoint = _sturlEndpoint.replace('${proceso}', 'SEGFAC');
  codigoFiltro = `idOperacion=${codigoFiltro}`;

  let config = {
    method: "get",
    url: Params.urlBase + urlEndpoint + codigoFiltro,
    headers: _headers
  }

  console.log("config.url obtenerOperacion:", config.url);

  let promise = Params.request<OperacionClienteProps>(config)
    .then(function (response) {
      console.log("response.data obtenerOperacion:", response.data);
      let obtenerCodigoDepartamento: any = response.data.map((item: any) => item.atributos.find((item: any) => item.codigo === "ATRDEP")?.valor);
      console.log("CÓDIGO DEPARTAMENTO:", JSON.parse(obtenerCodigoDepartamento));
      return response.data;
    }).catch(function (error) {
      console.log("error obtenerOperacion:", error);
    });

  let result = await promise;
  return result;
}

export const getAccionesPorEstado = async (idCabecera: any, codEstado: any) => {
  let _cacheControl = 'max-age=60*60*10, private';
  let _headers = await getApiProveedorFacturaHeaders(_cacheControl);
  let _sturlEndpoint = Params.endpoints.vUno.get.accionesPorEstado;
  let urlEndpoint = _sturlEndpoint.replace('${idCabecera}', idCabecera);
  let estadoActual = urlEndpoint.replace('${codEstado}', codEstado);

  let config = {
    method: 'get',
    url: Params.urlBase + estadoActual,
    headers: _headers
  }

  console.log("config getAccionesPorEstado:", config);

  let promise = Params.request<EstadoPorAcciones>(config)
    .then(function (response) {
      console.log("response getAccionesPorEstado:", response);
      return response;
    }).catch(function (error) {
      console.log("Error getAccionesPorEstado:", error);
    });
  let result = await promise;
  return result;
}

