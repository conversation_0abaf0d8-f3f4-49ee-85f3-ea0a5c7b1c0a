import React from "react";

export interface DatosProv {
  nombre: string;
  apellido: string;
  nombreDos: string;
  apellidoDos: string;
  tipoPersona: string;
  tipoProveedor: string;
  pais: string;
  telefono: string;
  numeroDocumento: string;
  direccion: string;
  ciudad: string;
  timbrado: string;
  fechaCarga: string;
  fechaVencimiento: string;
  estado: string;
}

const DataApiProveedorContext = React.createContext({
  nombre: '', apellido: '', nombreDos: '',
  apellidoDos: '', tipoPersona: '', tipoProveedor: '', pais: '', telefono: '', numeroDocumento: '',
  direccion: '', ciudad: '', timbrado: '', fechaCarga: '', fechaVencimiento: '', estado: '',
  tipoDocumento: '', criticidad: '', declaracionJurada: '', fechaDeclaracionJurada: '',
  codigoCliente: '', numeroTimbrado: '', mes: '', anho: ''
});



if (!DataApiProveedorContext) {
  throw new Error("Error DataApiProveedorContext.");
}

export default DataApiProveedorContext;