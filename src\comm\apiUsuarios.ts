import EndPoint from "./EndPoint.Config";
import axios from "axios";
import { getAmbienteContexto, getUrlBase } from "../utilities/contextInfo";
import { getApiUsuarioHeaders } from "./contracts/ApiBpmPoliticasProcesosRequestHeaders";
import { GetUsuariosResponse } from "./contracts/GetUsuariosResponse";

const Params =
{
  urlBase: getUrlBase(),
  ambiente: getAmbienteContexto(),
  request: axios.create({}),
  endpoints: EndPoint.apiUsuario
}

export const getUsuario = async (usuarioDominio: string) => {
  let data;
  const _cacheControl = 'max-age=60*60*10, private'
  const _headers = await getApiUsuarioHeaders(_cacheControl)
  const strURL = Params.endpoints.vUno.get.usuario
  const urlEndpoint = strURL.replace('${usuarioDominio}', usuarioDominio)

  const config =
  {
    method: 'get',
    url: Params.urlBase + urlEndpoint,
    headers: _headers,
    data: data
  }

  let fPromise = Params.request<GetUsuariosResponse>(config)
    .then(function (response) {
      return response.data
    })
    .catch(function (error) {
      console.log(error)
    })

  let result = await fPromise;

  return result;


}