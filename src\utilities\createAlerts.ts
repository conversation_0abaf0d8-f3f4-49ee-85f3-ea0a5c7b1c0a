import Swal, { SweetAlertOptions } from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';

const MySwal = withReactContent(Swal);

class CreateAlerts {
    static showSuccess(title: string, text?: string) {
        MySwal.fire({
            title,
            text,
            icon: 'success',
        });
    }

    static showError(title: string, text?: string) {
        MySwal.fire({
            title,
            text,
            icon: 'error',
        });
    }

    static showWarning(title: string, text?: string) {
        MySwal.fire({
            title,
            text,
            icon: 'warning',
        });
    }

    static showInfo(title: string, text?: string) {
        MySwal.fire({
            title,
            text,
            icon: 'info',
        });
    }

    static showCustom(options: SweetAlertOptions) {
        MySwal.fire(options);
    }

    static showLoading({ titulo = 'Cargando...' }) {
        MySwal.fire({
            title: titulo,
            allowEscapeKey: false,
            allowOutsideClick: false,
            didOpen: () => {
                MySwal.showLoading()
            }
        })
    }

    static showConfirmation(title: string, text: string, onConfirm: () => void) {
        MySwal.fire({
            title,
            text,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#1d428a',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Confirmar',
            cancelButtonText: 'Cancelar',
            reverseButtons: true,
        }).then((result) => {
            if (result.isConfirmed) {
                onConfirm();
            }
        });
    }

    static closeAlert(){
        MySwal.close();
    }
}

export default CreateAlerts;