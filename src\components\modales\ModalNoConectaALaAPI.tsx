import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';

interface ModalAPIProps {
  mostrarModal: boolean;
  cerrarModal: () => void;
}

const ModalNoConectaALaAPI: React.FC<ModalAPIProps> = ({ mostrarModal, cerrarModal }) => {
  return (
    <div>
      <Modal show={mostrarModal} onHide={cerrarModal}>
        <Modal.Header>
          <Modal.Title className='text-secondary'></Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p className='text-danger text-center font-weight-bold'>No conecta a la base de datos.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="primary" onClick={cerrarModal}>
            Aceptar
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  )
}

export default ModalNoConectaALaAPI