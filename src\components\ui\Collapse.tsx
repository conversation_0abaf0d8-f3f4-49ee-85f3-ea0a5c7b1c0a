import React, { useState, useRef, useEffect, ReactNode } from 'react';

interface CollapseProps {
  children: ReactNode;
  isOpen?: boolean;
  onToggle?: (isOpen: boolean) => void;
  className?: string;
  duration?: number;
}

const Collapse: React.FC<CollapseProps> = ({
  children,
  isOpen = false,
  onToggle,
  className = '',
  duration = 300
}) => {
  const [isCollapsed, setIsCollapsed] = useState(!isOpen);
  const [height, setHeight] = useState<number | 'auto'>(isOpen ? 'auto' : 0);
  const contentRef = useRef<HTMLDivElement>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    if (isOpen !== !isCollapsed) {
      toggleCollapse();
    }
  }, [isOpen]);

  const toggleCollapse = () => {
    if (isTransitioning) return;

    setIsTransitioning(true);
    const newIsCollapsed = !isCollapsed;

    if (newIsCollapsed) {
      // Collapsing
      setHeight(contentRef.current?.scrollHeight || 0);
      
      // Force reflow
      if (contentRef.current) {
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        contentRef.current.offsetHeight;
      }
      
      setTimeout(() => {
        setHeight(0);
      }, 10);
    } else {
      // Expanding
      setHeight(contentRef.current?.scrollHeight || 'auto');
    }

    setIsCollapsed(newIsCollapsed);
    
    if (onToggle) {
      onToggle(!newIsCollapsed);
    }

    // End transition
    setTimeout(() => {
      setIsTransitioning(false);
      if (!newIsCollapsed) {
        setHeight('auto');
      }
    }, duration);
  };

  const getCollapseClasses = () => {
    let classes = 'collapse';
    
    if (!isCollapsed) {
      classes += ' show';
    }
    
    if (isTransitioning) {
      classes += ' collapsing';
    }
    
    return classes;
  };

  return (
    <div
      ref={contentRef}
      className={`${getCollapseClasses()} ${className}`}
      style={{
        height: height,
        overflow: 'hidden',
        transition: isTransitioning ? `height ${duration}ms ease` : 'none'
      }}
    >
      {children}
    </div>
  );
};

// Hook para usar con botones de toggle
export const useCollapse = (initialState: boolean = false) => {
  const [isOpen, setIsOpen] = useState(initialState);

  const toggle = () => setIsOpen(!isOpen);
  const open = () => setIsOpen(true);
  const close = () => setIsOpen(false);

  return {
    isOpen,
    toggle,
    open,
    close,
    setIsOpen
  };
};

// Componente wrapper para facilitar el uso
interface CollapsibleProps {
  trigger: ReactNode;
  children: ReactNode;
  defaultOpen?: boolean;
  className?: string;
  triggerClassName?: string;
}

export const Collapsible: React.FC<CollapsibleProps> = ({
  trigger,
  children,
  defaultOpen = false,
  className = '',
  triggerClassName = ''
}) => {
  const { isOpen, toggle } = useCollapse(defaultOpen);

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      toggle();
    }
  };

  return (
    <div className={className}>
      <button
        type="button"
        className={`collapse-trigger ${triggerClassName}`}
        onClick={toggle}
        onKeyDown={handleKeyDown}
        style={{ cursor: 'pointer', border: 'none', background: 'none', padding: 0 }}
        aria-expanded={isOpen}
      >
        {trigger}
      </button>
      <Collapse isOpen={isOpen}>
        {children}
      </Collapse>
    </div>
  );
};

export default Collapse;
