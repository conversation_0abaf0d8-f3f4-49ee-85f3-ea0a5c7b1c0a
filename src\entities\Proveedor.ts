export type Proveedor = {
  id: number
  // "timbrado2": string,
  "timbrado": {
    "numero": string,
    "fechaCarga": string,
    "fechaVencimiento": string,
    "estado": string,
    "usuarioCarga": string,
    // "fechaDeVencimiento": Date
  },

  "persona": {
    "codigoCliente": string,
    "tipoDeDocumento": string,
    "tipoDocumento": string,
    "numeroDocumento": string,
    "nombre": string,
    "nombreDos": string;
    "apellido": string,
    "apellidoDos": string,
    "tipoPersona": string,
    "telefono": string
  },

  "ubicacion": {
    "direccion": string,
    "ciudad": string,
    "pais": string
  },

  "estado": string,
  "tipoProveedor": string,

  "documentoPLD": string,
  "fechaPLD": string,
}


export function isProveedor(Proveedor: any): Proveedor is Proveedor {
  return (Proveedor as Proveedor) != null;
}