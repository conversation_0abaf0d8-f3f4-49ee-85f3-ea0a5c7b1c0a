export function getDisplayName() {
  let displayName = _spPageContextInfo.userDisplayName;
  return displayName.toString()
}

export function getUserName() {


  let _userName = _spPageContextInfo.userLoginName.toString();

  let _re = /[^\\]*$/g
  if (_userName.includes("bancontinental")) {
    _userName = _re.exec(_userName).toString()
  }
  return _userName
}

console.log("getUserName:", getUserName());

export const getWebUrlInfo = () => {
  let ctx = _spPageContextInfo

  return ctx
}

export const getSPWebAbsoluteUrl = () => {
  return _spPageContextInfo.webAbsoluteUrl
}