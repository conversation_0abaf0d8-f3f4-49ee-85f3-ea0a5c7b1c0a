{"version": 3, "file": "tab.js", "sources": ["../src/tab.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.4.1): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME               = 'tab'\nconst VERSION            = '4.4.1'\nconst DATA_KEY           = 'bs.tab'\nconst EVENT_KEY          = `.${DATA_KEY}`\nconst DATA_API_KEY       = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst Event = {\n  HIDE           : `hide${EVENT_KEY}`,\n  HIDDEN         : `hidden${EVENT_KEY}`,\n  SHOW           : `show${EVENT_KEY}`,\n  SHOWN          : `shown${EVENT_KEY}`,\n  CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n}\n\nconst ClassName = {\n  DROPDOWN_MENU : 'dropdown-menu',\n  ACTIVE        : 'active',\n  DISABLED      : 'disabled',\n  FADE          : 'fade',\n  SHOW          : 'show'\n}\n\nconst Selector = {\n  DROPDOWN              : '.dropdown',\n  NAV_LIST_GROUP        : '.nav, .list-group',\n  ACTIVE                : '.active',\n  ACTIVE_UL             : '> li > .active',\n  DATA_TOGGLE           : '[data-toggle=\"tab\"], [data-toggle=\"pill\"], [data-toggle=\"list\"]',\n  DROPDOWN_TOGGLE       : '.dropdown-toggle',\n  DROPDOWN_ACTIVE_CHILD : '> .dropdown-menu .active'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Tab {\n  constructor(element) {\n    this._element = element\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  // Public\n\n  show() {\n    if (this._element.parentNode &&\n        this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n        $(this._element).hasClass(ClassName.ACTIVE) ||\n        $(this._element).hasClass(ClassName.DISABLED)) {\n      return\n    }\n\n    let target\n    let previous\n    const listElement = $(this._element).closest(Selector.NAV_LIST_GROUP)[0]\n    const selector = Util.getSelectorFromElement(this._element)\n\n    if (listElement) {\n      const itemSelector = listElement.nodeName === 'UL' || listElement.nodeName === 'OL' ? Selector.ACTIVE_UL : Selector.ACTIVE\n      previous = $.makeArray($(listElement).find(itemSelector))\n      previous = previous[previous.length - 1]\n    }\n\n    const hideEvent = $.Event(Event.HIDE, {\n      relatedTarget: this._element\n    })\n\n    const showEvent = $.Event(Event.SHOW, {\n      relatedTarget: previous\n    })\n\n    if (previous) {\n      $(previous).trigger(hideEvent)\n    }\n\n    $(this._element).trigger(showEvent)\n\n    if (showEvent.isDefaultPrevented() ||\n        hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    if (selector) {\n      target = document.querySelector(selector)\n    }\n\n    this._activate(\n      this._element,\n      listElement\n    )\n\n    const complete = () => {\n      const hiddenEvent = $.Event(Event.HIDDEN, {\n        relatedTarget: this._element\n      })\n\n      const shownEvent = $.Event(Event.SHOWN, {\n        relatedTarget: previous\n      })\n\n      $(previous).trigger(hiddenEvent)\n      $(this._element).trigger(shownEvent)\n    }\n\n    if (target) {\n      this._activate(target, target.parentNode, complete)\n    } else {\n      complete()\n    }\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    this._element = null\n  }\n\n  // Private\n\n  _activate(element, container, callback) {\n    const activeElements = container && (container.nodeName === 'UL' || container.nodeName === 'OL')\n      ? $(container).find(Selector.ACTIVE_UL)\n      : $(container).children(Selector.ACTIVE)\n\n    const active = activeElements[0]\n    const isTransitioning = callback && (active && $(active).hasClass(ClassName.FADE))\n    const complete = () => this._transitionComplete(\n      element,\n      active,\n      callback\n    )\n\n    if (active && isTransitioning) {\n      const transitionDuration = Util.getTransitionDurationFromElement(active)\n\n      $(active)\n        .removeClass(ClassName.SHOW)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  _transitionComplete(element, active, callback) {\n    if (active) {\n      $(active).removeClass(ClassName.ACTIVE)\n\n      const dropdownChild = $(active.parentNode).find(\n        Selector.DROPDOWN_ACTIVE_CHILD\n      )[0]\n\n      if (dropdownChild) {\n        $(dropdownChild).removeClass(ClassName.ACTIVE)\n      }\n\n      if (active.getAttribute('role') === 'tab') {\n        active.setAttribute('aria-selected', false)\n      }\n    }\n\n    $(element).addClass(ClassName.ACTIVE)\n    if (element.getAttribute('role') === 'tab') {\n      element.setAttribute('aria-selected', true)\n    }\n\n    Util.reflow(element)\n\n    if (element.classList.contains(ClassName.FADE)) {\n      element.classList.add(ClassName.SHOW)\n    }\n\n    if (element.parentNode && $(element.parentNode).hasClass(ClassName.DROPDOWN_MENU)) {\n      const dropdownElement = $(element).closest(Selector.DROPDOWN)[0]\n\n      if (dropdownElement) {\n        const dropdownToggleList = [].slice.call(dropdownElement.querySelectorAll(Selector.DROPDOWN_TOGGLE))\n\n        $(dropdownToggleList).addClass(ClassName.ACTIVE)\n      }\n\n      element.setAttribute('aria-expanded', true)\n    }\n\n    if (callback) {\n      callback()\n    }\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $this = $(this)\n      let data = $this.data(DATA_KEY)\n\n      if (!data) {\n        data = new Tab(this)\n        $this.data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document)\n  .on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n    event.preventDefault()\n    Tab._jQueryInterface.call($(this), 'show')\n  })\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Tab._jQueryInterface\n$.fn[NAME].Constructor = Tab\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Tab._jQueryInterface\n}\n\nexport default Tab\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "Event", "HIDE", "HIDDEN", "SHOW", "SHOWN", "CLICK_DATA_API", "ClassName", "DROPDOWN_MENU", "ACTIVE", "DISABLED", "FADE", "Selector", "DROPDOWN", "NAV_LIST_GROUP", "ACTIVE_UL", "DATA_TOGGLE", "DROPDOWN_TOGGLE", "DROPDOWN_ACTIVE_CHILD", "Tab", "element", "_element", "show", "parentNode", "nodeType", "Node", "ELEMENT_NODE", "hasClass", "target", "previous", "listElement", "closest", "selector", "<PERSON><PERSON>", "getSelectorFromElement", "itemSelector", "nodeName", "makeArray", "find", "length", "hideEvent", "relatedTarget", "showEvent", "trigger", "isDefaultPrevented", "document", "querySelector", "_activate", "complete", "hiddenEvent", "shownEvent", "dispose", "removeData", "container", "callback", "activeElements", "children", "active", "isTransitioning", "_transitionComplete", "transitionDuration", "getTransitionDurationFromElement", "removeClass", "one", "TRANSITION_END", "emulateTransitionEnd", "dropdown<PERSON><PERSON>d", "getAttribute", "setAttribute", "addClass", "reflow", "classList", "contains", "add", "dropdownElement", "dropdownToggleList", "slice", "call", "querySelectorAll", "_jQueryInterface", "config", "each", "$this", "data", "TypeError", "on", "event", "preventDefault", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAUA;;;;;;EAMA,IAAMA,IAAI,GAAiB,KAA3B;EACA,IAAMC,OAAO,GAAc,OAA3B;EACA,IAAMC,QAAQ,GAAa,QAA3B;EACA,IAAMC,SAAS,SAAgBD,QAA/B;EACA,IAAME,YAAY,GAAS,WAA3B;EACA,IAAMC,kBAAkB,GAAGC,CAAC,CAACC,EAAF,CAAKP,IAAL,CAA3B;EAEA,IAAMQ,KAAK,GAAG;EACZC,EAAAA,IAAI,WAAoBN,SADZ;EAEZO,EAAAA,MAAM,aAAoBP,SAFd;EAGZQ,EAAAA,IAAI,WAAoBR,SAHZ;EAIZS,EAAAA,KAAK,YAAoBT,SAJb;EAKZU,EAAAA,cAAc,YAAWV,SAAX,GAAuBC;EALzB,CAAd;EAQA,IAAMU,SAAS,GAAG;EAChBC,EAAAA,aAAa,EAAG,eADA;EAEhBC,EAAAA,MAAM,EAAU,QAFA;EAGhBC,EAAAA,QAAQ,EAAQ,UAHA;EAIhBC,EAAAA,IAAI,EAAY,MAJA;EAKhBP,EAAAA,IAAI,EAAY;EALA,CAAlB;EAQA,IAAMQ,QAAQ,GAAG;EACfC,EAAAA,QAAQ,EAAgB,WADT;EAEfC,EAAAA,cAAc,EAAU,mBAFT;EAGfL,EAAAA,MAAM,EAAkB,SAHT;EAIfM,EAAAA,SAAS,EAAe,gBAJT;EAKfC,EAAAA,WAAW,EAAa,iEALT;EAMfC,EAAAA,eAAe,EAAS,kBANT;EAOfC,EAAAA,qBAAqB,EAAG;EAPT,CAAjB;EAUA;;;;;;MAMMC;;;EACJ,eAAYC,OAAZ,EAAqB;EACnB,SAAKC,QAAL,GAAgBD,OAAhB;EACD;;;;;EAQD;WAEAE,OAAA,gBAAO;EAAA;;EACL,QAAI,KAAKD,QAAL,CAAcE,UAAd,IACA,KAAKF,QAAL,CAAcE,UAAd,CAAyBC,QAAzB,KAAsCC,IAAI,CAACC,YAD3C,IAEA3B,CAAC,CAAC,KAAKsB,QAAN,CAAD,CAAiBM,QAAjB,CAA0BpB,SAAS,CAACE,MAApC,CAFA,IAGAV,CAAC,CAAC,KAAKsB,QAAN,CAAD,CAAiBM,QAAjB,CAA0BpB,SAAS,CAACG,QAApC,CAHJ,EAGmD;EACjD;EACD;;EAED,QAAIkB,MAAJ;EACA,QAAIC,QAAJ;EACA,QAAMC,WAAW,GAAG/B,CAAC,CAAC,KAAKsB,QAAN,CAAD,CAAiBU,OAAjB,CAAyBnB,QAAQ,CAACE,cAAlC,EAAkD,CAAlD,CAApB;EACA,QAAMkB,QAAQ,GAAGC,IAAI,CAACC,sBAAL,CAA4B,KAAKb,QAAjC,CAAjB;;EAEA,QAAIS,WAAJ,EAAiB;EACf,UAAMK,YAAY,GAAGL,WAAW,CAACM,QAAZ,KAAyB,IAAzB,IAAiCN,WAAW,CAACM,QAAZ,KAAyB,IAA1D,GAAiExB,QAAQ,CAACG,SAA1E,GAAsFH,QAAQ,CAACH,MAApH;EACAoB,MAAAA,QAAQ,GAAG9B,CAAC,CAACsC,SAAF,CAAYtC,CAAC,CAAC+B,WAAD,CAAD,CAAeQ,IAAf,CAAoBH,YAApB,CAAZ,CAAX;EACAN,MAAAA,QAAQ,GAAGA,QAAQ,CAACA,QAAQ,CAACU,MAAT,GAAkB,CAAnB,CAAnB;EACD;;EAED,QAAMC,SAAS,GAAGzC,CAAC,CAACE,KAAF,CAAQA,KAAK,CAACC,IAAd,EAAoB;EACpCuC,MAAAA,aAAa,EAAE,KAAKpB;EADgB,KAApB,CAAlB;EAIA,QAAMqB,SAAS,GAAG3C,CAAC,CAACE,KAAF,CAAQA,KAAK,CAACG,IAAd,EAAoB;EACpCqC,MAAAA,aAAa,EAAEZ;EADqB,KAApB,CAAlB;;EAIA,QAAIA,QAAJ,EAAc;EACZ9B,MAAAA,CAAC,CAAC8B,QAAD,CAAD,CAAYc,OAAZ,CAAoBH,SAApB;EACD;;EAEDzC,IAAAA,CAAC,CAAC,KAAKsB,QAAN,CAAD,CAAiBsB,OAAjB,CAAyBD,SAAzB;;EAEA,QAAIA,SAAS,CAACE,kBAAV,MACAJ,SAAS,CAACI,kBAAV,EADJ,EACoC;EAClC;EACD;;EAED,QAAIZ,QAAJ,EAAc;EACZJ,MAAAA,MAAM,GAAGiB,QAAQ,CAACC,aAAT,CAAuBd,QAAvB,CAAT;EACD;;EAED,SAAKe,SAAL,CACE,KAAK1B,QADP,EAEES,WAFF;;EAKA,QAAMkB,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,UAAMC,WAAW,GAAGlD,CAAC,CAACE,KAAF,CAAQA,KAAK,CAACE,MAAd,EAAsB;EACxCsC,QAAAA,aAAa,EAAE,KAAI,CAACpB;EADoB,OAAtB,CAApB;EAIA,UAAM6B,UAAU,GAAGnD,CAAC,CAACE,KAAF,CAAQA,KAAK,CAACI,KAAd,EAAqB;EACtCoC,QAAAA,aAAa,EAAEZ;EADuB,OAArB,CAAnB;EAIA9B,MAAAA,CAAC,CAAC8B,QAAD,CAAD,CAAYc,OAAZ,CAAoBM,WAApB;EACAlD,MAAAA,CAAC,CAAC,KAAI,CAACsB,QAAN,CAAD,CAAiBsB,OAAjB,CAAyBO,UAAzB;EACD,KAXD;;EAaA,QAAItB,MAAJ,EAAY;EACV,WAAKmB,SAAL,CAAenB,MAAf,EAAuBA,MAAM,CAACL,UAA9B,EAA0CyB,QAA1C;EACD,KAFD,MAEO;EACLA,MAAAA,QAAQ;EACT;EACF;;WAEDG,UAAA,mBAAU;EACRpD,IAAAA,CAAC,CAACqD,UAAF,CAAa,KAAK/B,QAAlB,EAA4B1B,QAA5B;EACA,SAAK0B,QAAL,GAAgB,IAAhB;EACD;;;WAID0B,YAAA,mBAAU3B,OAAV,EAAmBiC,SAAnB,EAA8BC,QAA9B,EAAwC;EAAA;;EACtC,QAAMC,cAAc,GAAGF,SAAS,KAAKA,SAAS,CAACjB,QAAV,KAAuB,IAAvB,IAA+BiB,SAAS,CAACjB,QAAV,KAAuB,IAA3D,CAAT,GACnBrC,CAAC,CAACsD,SAAD,CAAD,CAAaf,IAAb,CAAkB1B,QAAQ,CAACG,SAA3B,CADmB,GAEnBhB,CAAC,CAACsD,SAAD,CAAD,CAAaG,QAAb,CAAsB5C,QAAQ,CAACH,MAA/B,CAFJ;EAIA,QAAMgD,MAAM,GAAGF,cAAc,CAAC,CAAD,CAA7B;EACA,QAAMG,eAAe,GAAGJ,QAAQ,IAAKG,MAAM,IAAI1D,CAAC,CAAC0D,MAAD,CAAD,CAAU9B,QAAV,CAAmBpB,SAAS,CAACI,IAA7B,CAA/C;;EACA,QAAMqC,QAAQ,GAAG,SAAXA,QAAW;EAAA,aAAM,MAAI,CAACW,mBAAL,CACrBvC,OADqB,EAErBqC,MAFqB,EAGrBH,QAHqB,CAAN;EAAA,KAAjB;;EAMA,QAAIG,MAAM,IAAIC,eAAd,EAA+B;EAC7B,UAAME,kBAAkB,GAAG3B,IAAI,CAAC4B,gCAAL,CAAsCJ,MAAtC,CAA3B;EAEA1D,MAAAA,CAAC,CAAC0D,MAAD,CAAD,CACGK,WADH,CACevD,SAAS,CAACH,IADzB,EAEG2D,GAFH,CAEO9B,IAAI,CAAC+B,cAFZ,EAE4BhB,QAF5B,EAGGiB,oBAHH,CAGwBL,kBAHxB;EAID,KAPD,MAOO;EACLZ,MAAAA,QAAQ;EACT;EACF;;WAEDW,sBAAA,6BAAoBvC,OAApB,EAA6BqC,MAA7B,EAAqCH,QAArC,EAA+C;EAC7C,QAAIG,MAAJ,EAAY;EACV1D,MAAAA,CAAC,CAAC0D,MAAD,CAAD,CAAUK,WAAV,CAAsBvD,SAAS,CAACE,MAAhC;EAEA,UAAMyD,aAAa,GAAGnE,CAAC,CAAC0D,MAAM,CAAClC,UAAR,CAAD,CAAqBe,IAArB,CACpB1B,QAAQ,CAACM,qBADW,EAEpB,CAFoB,CAAtB;;EAIA,UAAIgD,aAAJ,EAAmB;EACjBnE,QAAAA,CAAC,CAACmE,aAAD,CAAD,CAAiBJ,WAAjB,CAA6BvD,SAAS,CAACE,MAAvC;EACD;;EAED,UAAIgD,MAAM,CAACU,YAAP,CAAoB,MAApB,MAAgC,KAApC,EAA2C;EACzCV,QAAAA,MAAM,CAACW,YAAP,CAAoB,eAApB,EAAqC,KAArC;EACD;EACF;;EAEDrE,IAAAA,CAAC,CAACqB,OAAD,CAAD,CAAWiD,QAAX,CAAoB9D,SAAS,CAACE,MAA9B;;EACA,QAAIW,OAAO,CAAC+C,YAAR,CAAqB,MAArB,MAAiC,KAArC,EAA4C;EAC1C/C,MAAAA,OAAO,CAACgD,YAAR,CAAqB,eAArB,EAAsC,IAAtC;EACD;;EAEDnC,IAAAA,IAAI,CAACqC,MAAL,CAAYlD,OAAZ;;EAEA,QAAIA,OAAO,CAACmD,SAAR,CAAkBC,QAAlB,CAA2BjE,SAAS,CAACI,IAArC,CAAJ,EAAgD;EAC9CS,MAAAA,OAAO,CAACmD,SAAR,CAAkBE,GAAlB,CAAsBlE,SAAS,CAACH,IAAhC;EACD;;EAED,QAAIgB,OAAO,CAACG,UAAR,IAAsBxB,CAAC,CAACqB,OAAO,CAACG,UAAT,CAAD,CAAsBI,QAAtB,CAA+BpB,SAAS,CAACC,aAAzC,CAA1B,EAAmF;EACjF,UAAMkE,eAAe,GAAG3E,CAAC,CAACqB,OAAD,CAAD,CAAWW,OAAX,CAAmBnB,QAAQ,CAACC,QAA5B,EAAsC,CAAtC,CAAxB;;EAEA,UAAI6D,eAAJ,EAAqB;EACnB,YAAMC,kBAAkB,GAAG,GAAGC,KAAH,CAASC,IAAT,CAAcH,eAAe,CAACI,gBAAhB,CAAiClE,QAAQ,CAACK,eAA1C,CAAd,CAA3B;EAEAlB,QAAAA,CAAC,CAAC4E,kBAAD,CAAD,CAAsBN,QAAtB,CAA+B9D,SAAS,CAACE,MAAzC;EACD;;EAEDW,MAAAA,OAAO,CAACgD,YAAR,CAAqB,eAArB,EAAsC,IAAtC;EACD;;EAED,QAAId,QAAJ,EAAc;EACZA,MAAAA,QAAQ;EACT;EACF;;;QAIMyB,mBAAP,0BAAwBC,MAAxB,EAAgC;EAC9B,WAAO,KAAKC,IAAL,CAAU,YAAY;EAC3B,UAAMC,KAAK,GAAGnF,CAAC,CAAC,IAAD,CAAf;EACA,UAAIoF,IAAI,GAAGD,KAAK,CAACC,IAAN,CAAWxF,QAAX,CAAX;;EAEA,UAAI,CAACwF,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIhE,GAAJ,CAAQ,IAAR,CAAP;EACA+D,QAAAA,KAAK,CAACC,IAAN,CAAWxF,QAAX,EAAqBwF,IAArB;EACD;;EAED,UAAI,OAAOH,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOG,IAAI,CAACH,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAII,SAAJ,wBAAkCJ,MAAlC,QAAN;EACD;;EACDG,QAAAA,IAAI,CAACH,MAAD,CAAJ;EACD;EACF,KAfM,CAAP;EAgBD;;;;0BAzKoB;EACnB,aAAOtF,OAAP;EACD;;;;;EA0KH;;;;;;;EAMAK,CAAC,CAAC8C,QAAD,CAAD,CACGwC,EADH,CACMpF,KAAK,CAACK,cADZ,EAC4BM,QAAQ,CAACI,WADrC,EACkD,UAAUsE,KAAV,EAAiB;EAC/DA,EAAAA,KAAK,CAACC,cAAN;;EACApE,EAAAA,GAAG,CAAC4D,gBAAJ,CAAqBF,IAArB,CAA0B9E,CAAC,CAAC,IAAD,CAA3B,EAAmC,MAAnC;EACD,CAJH;EAMA;;;;;;EAMAA,CAAC,CAACC,EAAF,CAAKP,IAAL,IAAa0B,GAAG,CAAC4D,gBAAjB;EACAhF,CAAC,CAACC,EAAF,CAAKP,IAAL,EAAW+F,WAAX,GAAyBrE,GAAzB;;EACApB,CAAC,CAACC,EAAF,CAAKP,IAAL,EAAWgG,UAAX,GAAwB,YAAM;EAC5B1F,EAAAA,CAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb;EACA,SAAOqB,GAAG,CAAC4D,gBAAX;EACD,CAHD;;;;;;;;"}