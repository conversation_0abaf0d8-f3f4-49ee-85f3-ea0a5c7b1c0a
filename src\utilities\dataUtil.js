export const esValido = (valor) => {
    if (valor === null || valor === undefined) {
        return false;
    }

    if(typeof valor == 'undefined') return false;

    if (typeof valor === 'number') {
        return !isNaN(valor) && isFinite(valor);
    }

    if (typeof valor === 'string') {
        return valor.trim().length > 0;
    }

    if (Array.isArray(valor)) {
        return valor.length > 0;
    }

    if (typeof valor === 'object') {
        return Object.keys(valor).length > 0;
    }

    return false;
}

export const trimTexto = (texto) => {
    return texto.trim();
}

export const esNumerico = (texto) => {
    return /^\d+$/.test(texto);
}

export const formatMonto = (number) => {
    let numStr = number.toString();
    let formattedStr = numStr.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
    return formattedStr;
}

export const quitarCerosIzquierda = (texto) => {
    if (!isNaN(texto)) {
      return texto.replace(/^0+/g, "");
    } else {
      return texto;
    }
};

export const datosAtributos = (data, atributos) => {
    for (const cliente of data) {
      if (cliente.atributo.atributo === atributos) {
        return cliente.valor;
      }
    }
}