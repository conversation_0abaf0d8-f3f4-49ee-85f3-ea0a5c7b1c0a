/// <reference types="webpack/module" />

import React, { useContext, useMemo, useState, useRef, useEffect } from "react";
import { FacturaGetResponse } from "../../comm/contracts/FacturaGetResponse";
import FacturaFormContext from "../contextProvider/counterContext";
import { useProveedor } from "../contextProvider/datosProveedorContext";
import { getAmbienteContexto, getUrlBase } from "../../utilities/contextInfo";
import axios from "axios";
import EndPoint from "../../comm/EndPoint.Config";
import { getApiProveedorFacturaHeaders } from "../../comm/contracts/ApiBpmPoliticasProcesosRequestHeaders";
import { Estados } from "../../entities/Enums/estadosRespuesta";
import ModalFacturaNotificacionRegistrada from "../modales/ModalFacturaNotificacionRegistrada";

type RegistroFactura = {
  tipoDocumento: string;
  numeroDocumento: string;
  timbrado: string;
  numeroFactura: string;
}

interface Timbrado {
  id: number;
  idProveedor: number;
  numero: string;
  estado: string;
  fechaVencimiento: string;
}

export const FacturaHeaderMData = () => {

  const { state } = useProveedor();

  const _FacturaFormContext = useContext(FacturaFormContext)
  const [numUno, setNumUno] = useState('')
  const [numDos, setNumDos] = useState('')
  const [numTres, setNumTres] = useState('')
  const [timbrado, setTimbrado] = useState('');
  const [timbradoSelected, setTimbradoSelected] = useState<Timbrado | any>(null);
  const [primerTimbradoState, setPrimerTimbradoState] = useState<Timbrado | any>(null);
  const [mensajeTimbrado, setMensajeTimbrado] = useState('');
  const [provision, setProvision] = useState(false)
  const [excepcionFactura, setExcepcionFactura] = useState(false)
  const [isDisabledState, setIsDisabledState] = useState(false)
  const _readOnly = _FacturaFormContext.onlyRead
  const _idObj: any = {
    datePicker: 'datePicker' + _FacturaFormContext.counter,
    timbradoInput: 'timbrado-input' + _FacturaFormContext.counter,
    numFacturaInput: "Numero-de-factura-input" + _FacturaFormContext.counter,
    provisionCheck: "provisionCheck" + _FacturaFormContext.counter,
    revisionCheck: "revisionCheck" + _FacturaFormContext.counter,
    monedaState: "monendaState" + _FacturaFormContext.counter
  }

  const [dato, setDato] = useState("");
  const [estadoTimbrado, setEstadoTimbrado] = useState("Timbrado vencido");
  const [abrirModalNotificacionFactura, setAbrirModalNotificacionFactura] = useState(false);

  const [timbradoBandeja, setTimbradoBandeja] = useState(state.bandejaTimbrado);

  const numDosRef = useRef<HTMLInputElement>(null);
  const numTresRef = useRef<HTMLInputElement>(null);

  const checkExcepcionFactura = (event: React.ChangeEvent<HTMLInputElement>) => {
    setExcepcionFactura(event.target.checked);
  };

  let _facturaInputValues = {
    timbrado: '',
    numFactura: {
      parte1: '',
      parte2: '',
      parte3: ''
    },
    provisionValue: false,
    monedaState: <><option selected>PYG</option>
      <option>USD</option></>,
    isDisabled: false,
    dateEmision: '',
    dateVencimiento: ''
  }

  if (_readOnly) {
    useMemo(() => {
      const _factura: any = _FacturaFormContext.factura as FacturaGetResponse
      setIsDisabledState(true)
      setTimbrado(_factura.timbrado)
      _facturaInputValues.provisionValue = (_factura.provision === 'S') ? true : false
      setProvision(_facturaInputValues.provisionValue)
      _facturaInputValues.monedaState = (_factura.monto.moneda === 'PYG') ? <><option selected>PYG</option>
        <option>USD</option></> : <><option >PYG</option>
        <option selected>USD</option></>
      setNumUno(_factura.numeroFactura.idFacturaParteUno)
      setNumDos(_factura.numeroFactura.idFacturaParteDos)
      setNumTres(_factura.numeroFactura.idFacturaParteTres)
    }, [])
  }

  let currentDate = new Date();
  let day = currentDate.getDate();
  let month = currentDate.getMonth() + 1;
  let year = currentDate.getFullYear();

  let fechaActual = `${year}-${month < 10 ? "0" + month : month}-${day < 10 ? "0" + day : day}`;
  state.fechaActual = fechaActual;

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawDate = e.target.value;
    const parts = rawDate.split('-');
    if (parts.length === 3) {
      const year: any = parts[0]
      const month: any = parts[2]
      const day: any = parts[1]
      if (!isNaN(day) && !isNaN(month) && !isNaN(year)) {
        const formatDate = `${month}-${day}-${year}`;
        console.log("formatDate:", formatDate);
        if (rawDate) {
          setEstadoTimbrado(estadoTimbrado);
        }
        setDato(formatDate);
      } else {
        setDato(rawDate);
      }
    } else {
      setDato(rawDate);
    }
  }

  const Params = {
    urlBase: getUrlBase(),
    ambiente: getAmbienteContexto(),
    request: axios.create({}),
    endpoints: EndPoint.apiProveedorFactura
  }

  const getRegistroFactura = async () => {
    let _cacheControl = 'max-age=60*60*10, private';
    let _headers = await getApiProveedorFacturaHeaders(_cacheControl);
    let _sturlEndpoint = Params.endpoints.vUno.get.proveedorRegistroFactura;
    let urlEndpoint = _sturlEndpoint.replace('${tipoDocumento}', state.tipoDocumento).replace('${numeroDocumento}', state.numeroDocumento).replace('${timbrado}', `${timbrado}`).replace('${numeroFactura}', numUno + numDos + state.numTres);

    let config = {
      method: 'get',
      url: Params.urlBase + urlEndpoint,
      headers: _headers
    }

    console.log("URL getRegistroFactura:", config.url);

    let promise = Params.request<RegistroFactura>(config)
      .then(function (response) {
        if (response.status === Estados.estadoExitoso) {
          console.log("response.data getRegistroFactura:", response.data);
        }
        console.log("response.status getRegistroFactura:", response.status);
        state.responseStatusRegistroFactura = response.status.toString();
        return response.data;
      }).catch(function (error) {
        console.log("error catch getRegistroFactura:", error);
        return error;
      })
    let result = await promise;
    return result;
  }

  const handleInputChange = (e: any) => {
    setTimbradoBandeja(e.target.value);
  }

  const handleModalNotificacionFactura = () => setAbrirModalNotificacionFactura(true);
  const cerrarModalNotificacionFactura = () => setAbrirModalNotificacionFactura(false);

  // LPAD
  function lpad(input: string, length: number): string {
    return input.padStart(length, '0');
  }

  function soloNumeros(event: any) {
    const key = event.key;
    if (!/[0-9]/.test(key) && key !== "ArrowLeft" && key !== "ArrowRight" && key !== "Delete" && key !== "Backspace" && key !== "Tab") {
      event.preventDefault();
    }
  }

  const autocompletadoCeroUno = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Tab") {
      e.preventDefault();
      const paddedValue = lpad(numUno, 3);
      setNumUno(paddedValue);
      if (numDosRef.current) {
        numDosRef.current.focus();
      }
    }
    soloNumeros(e);
  }

  const autocompletadoCeroDos = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Tab") {
      e.preventDefault();
      const paddedValue = lpad(numDos, 3);
      setNumDos(paddedValue);
      if (numTresRef.current) {
        numTresRef.current.focus();
      }
    }
    soloNumeros(e);
  }

  const autocompletadoCeroTres = async (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Tab") {
      e.preventDefault();
      const paddedValue = lpad(numTres, 7);
      setNumTres(paddedValue);
      await getRegistroFactura();
      let valorSelect = state.timbrado.some((item: any) => item.numero === timbrado);
      if (state.responseStatusRegistroFactura === Estados.estadoExitoso.toString() && valorSelect) {
        handleModalNotificacionFactura();
        console.log("La Factura se encuentra registrada.");
      } else {
        console.log("No coincide con la lista de timbrados.");
      }

      if (state.responseStatusRegistroFactura === Estados.estadoSinContenido.toString()) {
        console.log("Factura no registrada.");
      }
    }
    soloNumeros(e);
  }

  localStorage.setItem("datoFechaEmision", dato);
  localStorage.setItem("numUno", numUno);
  localStorage.setItem("numDos", numDos);
  localStorage.setItem("numTres", numTres);

  state.numTres = numTres;

  useEffect(() => {
    setProvision(state.bandejaProvision === "S");
  }, [state.bandejaProvision]);

  useEffect(() => {
    if (state.timbrado.length > 0) {
      const firstValue = state.timbrado.map((item: any) => item.numero)[0];
      console.log("firstValue:", firstValue);
      const primerTimbradoFechaVencimiento = state.timbrado.map((item: any) => item.fechaVencimiento)[0];
      setTimbrado(firstValue);
      setTimbradoSelected(firstValue);
      setPrimerTimbradoState(firstValue);
      if (primerTimbradoFechaVencimiento < state.fechaActual) {
        setPrimerTimbradoState(`Timbrado ${firstValue} vencido.`);
        setMensajeTimbrado(`Timbrado ${firstValue} vencido.`);
      } else {
        setTimbrado("");
        setTimbradoSelected("");
        setPrimerTimbradoState("");
        setMensajeTimbrado("");
      }
    } else {
      setTimbrado("");
      setTimbradoSelected("");
      setPrimerTimbradoState("");
      setMensajeTimbrado("");
    }
  }, [state.timbrado]);

  const selectTimbradoChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setTimbrado(e.target.value);
    console.log("Timbrado Seleccionado:", e.target.value);

    const timbradoEncontrado: any = state.timbrado.find((item: any) => item.numero === e.target.value);
    if (timbradoEncontrado) {
      setTimbradoSelected(timbradoEncontrado.fechaVencimiento);
      if (timbradoEncontrado.fechaVencimiento < state.fechaActual) {
        setTimbradoSelected(timbradoEncontrado.fechaVencimiento);
        setMensajeTimbrado(`Timbrado ${e.target.value} vencido.`);
        console.log(`Timbrado ${e.target.value} vencido.`);
      } else {
        setMensajeTimbrado("");
        console.log(`Timbrado ${e.target.value} activo.`);
      }
    }
  }

  localStorage.setItem("timbradoSelected", timbradoSelected);
  let variableTimbradoSelected = localStorage.getItem("timbradoSelected");
  console.log("variableTimbradoSelected:", variableTimbradoSelected.slice(0, 10));
  localStorage.setItem("primerTimbradoState", primerTimbradoState);
  console.log("dato:", dato);

  return (
    <>
      <div className="invoice-date">
        <br />
        <div className="d-flex flex-column align-items-center">
          <label htmlFor="emision">Fecha de emisión</label>
          {
            state.bandejaFechaEmision ? <input type="date" id={_idObj.datePicker + 'emision'} lang="es" className="form-control" value={state.bandejaFechaEmision} onChange={handleDateChange} disabled />
              : <input type="date" id={_idObj.datePicker + 'emision'} lang="es" className="form-control" defaultValue={dato || state.bandejaFechaEmision} onChange={handleDateChange} />
          }
        </div>
        <br />
        <div className="invoice-detail text-center">
          <div className="d-flex flex-column align-items-center">
            <label>Timbrado</label>
            {
              !state.bandejaTimbrado ?
                (<select name={_idObj.timbradoInput} id={_idObj.timbradoInput + 'field'} disabled={isDisabledState} defaultValue="" className="custom-select" onChange={selectTimbradoChange}>
                  {state.timbrado?.map((item: any) => <option key={item.numero} value={item.numero}>{item.numero}</option>)}
                </select>)
                :
                (<select name={_idObj.timbradoInput} id={_idObj.timbradoInput + 'field'} disabled={isDisabledState} className="custom-select" value={timbradoBandeja} onChange={handleInputChange}>
                  <option value={timbradoBandeja}>{timbradoBandeja}</option>
                </select>)
            }
            {
              timbradoSelected || primerTimbradoState < state.fechaActual && !state.bandejaTimbrado ? <small className="text-danger font-weight-bold mt-1">{mensajeTimbrado}</small> : ""
            }
          </div>
          <div className="input-group mb-3 mt-3 d-flex flex-column justify-content-center align-items-center">
            <label>Número de Factura</label>
            <div className="row">
              {
                state.bandejaFacturaParteUno ? <div>{state.bandejaFacturaParteUno}<span>-</span></div>
                  :
                  <input type="text" value={numUno} onChange={(e) => setNumUno(e.target.value)} onKeyDown={autocompletadoCeroUno} minLength={0} maxLength={3} className="form-control bg-light col-md-3" placeholder="Numero" aria-label="# Factura Parte 1"
                    aria-describedby={_idObj.numFacturaInput} id={_idObj.numFacturaInput + 'parte1'} />
              }
              {
                state.bandejaFacturaParteDos ? <div>{state.bandejaFacturaParteDos}<span>-</span></div>
                  :
                  <input type="text" value={numDos} onChange={(e) => setNumDos(e.target.value)} onKeyDown={autocompletadoCeroDos} minLength={0} maxLength={3} className="form-control bg-light col-md-3" placeholder="de" aria-label="# Factura Parte  2"
                    aria-describedby={_idObj.numFacturaInput} id={_idObj.numFacturaInput + 'parte2'} ref={numDosRef} />
              }
              {
                state.bandejaFacturaParteTres ? <div>{state.bandejaFacturaParteTres}</div>
                  :
                  <input type="text" value={numTres} onChange={(e) => setNumTres(e.target.value)} onKeyDown={autocompletadoCeroTres} minLength={0} maxLength={7} className="form-control bg-light col-md-6" placeholder="Factura" aria-label="# Factura Parte  3"
                    aria-describedby={_idObj.numFacturaInput} id={_idObj.numFacturaInput + 'parte3'} ref={numTresRef} />
              }
            </div>
          </div>
          <form className="row">
            <div className="col">
              <div className="d-flex">
                {
                  state.bandejaProvision ?
                    <input className="form-check-input" type="checkbox" checked={state.bandejaProvision === "S" ? true : provision} disabled onChange={() => { setProvision(!provision) }} value="" id={_idObj.provisionCheck} />
                    :
                    <input className="form-check-input" type="checkbox" checked={state.bandejaProvision === "S" ? true : provision} onChange={() => { setProvision(!provision) }} value="" id={_idObj.provisionCheck} />
                }
                <label className="form-check-label text-left" htmlFor={_idObj.provisionCheck}>
                  Es provision
                </label>
              </div>
              <div className="mt-3 d-flex">
                <input className="form-check-input" type="checkbox" checked={state.estadoPerfilMensaje === "SI" || state.DVEstadoDocumento === "VENCIDO" ? true : false} onChange={checkExcepcionFactura} defaultValue="false" id="facturaExcepcionCheck" />
                <label className="form-check-label text-left" htmlFor="facturaExcepcionCheck">Excepciones</label>
                <input type="hidden" id="facturaExcepcionHidden" value={state.estadoPerfilMensaje === "SI" || state.DVEstadoDocumento === "VENCIDO" ? "S" : "N"} />
              </div>
            </div>
            <div className="col">
              <label htmlFor={_idObj.monedaState}>Moneda</label>
              {
                state.bandejaTipoMoneda ? <select id={_idObj.monedaState} className="form-control" name="currency" defaultValue={state.bandejaTipoMoneda} disabled>
                  {_facturaInputValues.monedaState}
                </select>
                  :
                  <select id={_idObj.monedaState} className="form-control" name="currency" defaultValue={""}>
                    {_facturaInputValues.monedaState}
                  </select>
              }
            </div>
          </form>
        </div>
      </div>
      {<ModalFacturaNotificacionRegistrada abrir={abrirModalNotificacionFactura} cerrar={cerrarModalNotificacionFactura} />}
    </>
  )

}