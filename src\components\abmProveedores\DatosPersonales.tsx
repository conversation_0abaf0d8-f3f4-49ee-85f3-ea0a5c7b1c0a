import React, { useContext } from 'react';
import DataApiProveedorContext from '../contextProvider/dataProveedorContext';

const DatosPersonales = () => {

  const _dataApiProveedorContext = useContext(DataApiProveedorContext);

  return (
    <div className='mt-2'>
      <div className='card d-flex flex-column flex-wrap m-2' id='datos-personales'>
        <div className='card-body d-flex flex-column flex-wrap'>
          <div className='d-flex flex-column align-items-center flex-wrap'>
            <div className='d-flex align-items-center justify-content-center flex-wrap' style={{ width: '90%' }}>
              <div className='d-flex'>
                <input type="checkbox" className='mx-2' />
                <div className='d-flex' style={{ width: '95%' }}>Formulario de Declaración Jurada PDL/FT</div>
              </div>
              <div className='d-flex'>
                <input type='date' className='mx-2' defaultValue="" />
              </div>
            </div>
            <div className='w-100'>
              <div className='d-flex justify-content-center align-items-center my-4 flex-wrap'>
                <div className='d-flex flex-column flex-wrap mx-4 w-50 row'>
                  <div className='d-flex flex-column my-4 col'>
                    <label className='mx-2'>Nombre 1</label>
                    <input type='text' className='form-control' defaultValue={_dataApiProveedorContext.nombre} />
                  </div>
                  <div className='d-flex flex-column col'>
                    <label className='mx-2'>Apellido 1</label>
                    <input type='text' className='form-control' defaultValue={_dataApiProveedorContext.apellido} />
                  </div>
                </div>
                <div className='d-flex flex-column flex-wrap mx-4 w-50 row'>
                  <div className='d-flex flex-column my-4 col'>
                    <label className='mx-2'>Nombre 2</label>
                    <input type='text' className='form-control' defaultValue={_dataApiProveedorContext.nombreDos} />
                  </div>
                  <div className='d-flex flex-column col'>
                    <label className='mx-2'>Apellido 2</label>
                    <input type='text' className='form-control' defaultValue={_dataApiProveedorContext.apellidoDos} />
                  </div>
                </div>
              </div>
            </div>
            <div className='d-flex justify-content-center' style={{ width: '82%' }}>
              <label className='mx-2'>Dirección</label>
              <input type='text' className='mw-100' style={{ width: '100%' }} defaultValue={_dataApiProveedorContext.direccion} />
            </div>
            <div className='w-100'>
              <div className='d-flex justify-content-center align-items-center my-4 row'>
                <div className='d-flex justify-content-center my-2 mx-4 col'>
                  <label className='mx-2'>Teléfono</label>
                  <input type='text' className='mx-1' defaultValue={_dataApiProveedorContext.telefono} />
                </div>
                <div className='d-flex justify-content-center my-2 mx-4 col'>
                  <label className='mx-2'>Ciudad</label>
                  <input type='text' className='mx-1' defaultValue={_dataApiProveedorContext.ciudad} />
                </div>
              </div>
            </div>
          </div>
          <div className='row'>
            <div className='col'>
              <div className='mb-3 d-flex justify-content-center'>
                <label className='ml-2'>Tipo Pers.</label>
                <select className='form-select form-select-lg mx-2' aria-label=".form-select-lg example" defaultValue={_dataApiProveedorContext.tipoPersona}>
                  <option value="JURIDICA">JURIDICA</option>
                  <option value="F">F</option>
                </select>
              </div>
              <div className='mb-3 d-flex justify-content-center'>
                <label className='ml-2'>Tipo Proveedor</label>
                <select className='form-select form-select-lg mx-2' aria-label=".form-select-lg example" defaultValue={_dataApiProveedorContext.tipoProveedor}>
                  <option value="" className='text-start'>[Seleccione]</option>
                  <option value="PROVEEDOR-1">PROVEEDOR-1</option>
                  <option value="PROVEEDOR-2">PROVEEDOR-2</option>
                  <option value="null">null</option>
                </select>
              </div>
              <div className='mb-3 d-flex justify-content-center'>
                <label className='ml-2'>Citricidad</label>
                <select className='form-select form-select-lg mx-2' aria-label=".form-select-lg example" defaultValue="">
                  <option value=""></option>
                  <option value="Opción-1">Opción-1</option>
                  <option value="Opción-2">Opción-2</option>
                </select>
              </div>
            </div>
            <div className='col'>
              <div className='m-2 d-flex justify-content-center'>
                <button className='btn btn-primary mx-2'>Guardar</button>
                <button className='btn btn-secondary'>Cancelar</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DatosPersonales;