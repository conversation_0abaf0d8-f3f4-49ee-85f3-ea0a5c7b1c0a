/// <reference types="webpack/module" />
import React, { useEffect, useState } from 'react';
import alerta from './../../utilities/createAlerts';
import { Formik, Field, Form, FormikHelpers } from 'formik';
import {
    Card, CardBody, CardTitle,
    Button, Row, Col, CardText, Dropdown, DropdownToggle, DropdownMenu, DropdownItem
} from 'reactstrap';
import './InicioDeSeguimientoFormStyle.css';
import _privilegeServices from '../../services/privilegeServices';
import _clientesServices from '../../services/clientesServices';
import { getUsuario } from '../../comm/apiUsuarios';
import * as sp from '../../utilities/_spPageContextInfo';
import ClienteAtributosModal from './modals/ClienteAtributosModal';
import { ToastType, showToast } from '../../utilities/toastUtils';
import { insertarEnListaSharePoint } from "./services/sharepointService";
import { esValido, trimTexto, datosAtributos, formatMonto } from '../../utilities/dataUtil';
interface RuleData {
    idRegla: number;
    descripcion: string;
    mensajeDeError: string;
    clienteCumple: boolean;
}
interface DropdownOption {
    id: number;
    value: string;
    label: string;
}

interface FormValues {
    searchInput: string;
}

export const InicioDeSeguimientoForm: React.FC<{}> = () => {
    const [rules, setRules] = useState<RuleData[]>([]);
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const [tipoSeguimientoOptions, setTipoSeguimientoOptions] = useState<DropdownOption[]>([
        { id: 18, value: 'Inclusión', label: 'Inclusión ' },
        { id: 19, value: 'Exclusión', label: 'Exclusión' }
    ]);
    const [tipoSeguimiento, setTipoSeguimiento] = useState<DropdownOption>(tipoSeguimientoOptions[0]);
    const [datosUsuario, setDatosUsuario] = useState<any>({});
    const [ldapUser, setLdapUser] = useState('');
    const [codClienteBusqueda, setCodClienteBusqueda] = useState('');
    const [clienteExistente, setClienteExistente] = useState(false);
    const [infoCliente, setInfoCliente] = useState<any>({});
    const [analizandoCliente, setAnalizandoCliente] = useState(false);
    const [mostrarBotonValidarReglas, setMostrarBotonValidarReglas] = useState(false);
    const [datosBasicosCliente, setDatosBasicosCliente] = useState<any>({});
    const [showModal, setShowModal] = useState(false);
    const toggleDropdown = () => setDropdownOpen(prevState => !prevState);
    const [estado] = useState(1);
    const [selectedRadio, setSelectedRadio] = useState("Codigo");
    const handleVerAtributos = () => {
        setShowModal(true);
    };
    const handleCloseModal = () => {
        setShowModal(false);
    };
    useEffect(() => {
        async function fetchUsuario() {
            try {
                const _nombreUsuario = sp.getUserName().toString();
                setLdapUser(_nombreUsuario);
                const usuarioContexto = await getUsuario(_nombreUsuario);
                setDatosUsuario(usuarioContexto);
            } catch (error) {
                showToast(
                    <div style={{ padding: '8px' }}>
                        <strong>Ha ocurrido un error durante la consulta de datos del usuario.</strong>
                    </div>,
                    ToastType.Error
                );
            }
        }
        fetchUsuario();
    }, []);

    const iniciarSeguimiento = async () => {
        alerta.showConfirmation('¿Estás seguro de iniciar el seguimiento?', '', async () => {
            try {
                alerta.showLoading({});

                /*if(!esValido(datosUsuario)) {
                    showToast(
                        <div style={{ padding: '8px' }}>
                            <strong>Error al iniciar seguimiento.</strong>
                            <strong>Ha ocurrido un error durante la consulta de datos del usuario.</strong>
                        </div>,
                        ToastType.Error
                    );
                    return false;
                }*/
                
                const data = {
                    usuarioIniciante: {
                        tresLetras: datosUsuario.codigo,
                        ldap: ldapUser
                        
                    },
                    codigoCliente: codClienteBusqueda,
                    codigoProducto: 7,
                    codigoSubProducto: tipoSeguimiento.id,
                    codigoSucursal: datosUsuario?.sucursal?.codigo,                    
                    codigoBanca: 17,
                    idSharePoint: 0,
                    generaTc: true,
                    tipoSolicitud: "INCLUSION"
                };

                let responseSeguimiento = await new _privilegeServices().iniciarSeguimiento(data);
                
                if (esValido(responseSeguimiento)) {
                    showToast(
                        <div style={{ padding: '8px' }}>
                            <strong>Se ha iniciado el seguimiento correctamente.</strong><br></br>
                            <strong>Operación: {responseSeguimiento?.codigoOperacion}</strong>
                        </div>,
                        ToastType.Success
                    );
                    
                    let insertarSharepoint = await insertarEnListaSharePoint(responseSeguimiento, data);

                    if(esValido(insertarSharepoint)) {
                        showToast(
                            <div style={{ padding: '8px' }}>
                                <strong>Respuesta.</strong><br></br>
                                <strong>Operación: {insertarSharepoint}</strong>
                            </div>,
                            ToastType.Info
                        );
                    }

                    const sharepointDato = {                        
                        producto: 7,
                        subproducto:  tipoSeguimiento.id,
                        operacion: responseSeguimiento?.codigoOperacion,
                        sharedpointId: insertarSharepoint,
                    };

                    let insertarIdSharepoint = await new _privilegeServices().insertarSharepointId(sharepointDato);
                }
            } catch (error) {
                console.error('Error al iniciar el seguimiento:', error);
                showToast(
                    <div style={{ padding: '8px' }}>
                        <strong>Ha ocurrido un error al iniciar el seguimiento</strong>
                    </div>,
                    ToastType.Error
                );
                alerta.closeAlert();
            } finally {
                alerta.closeAlert();
            }
        });
    };
    const validarReglas = async () => {
        alerta.showConfirmation('¿Estás seguro de re-validar las reglas?', '', async () => {
            alerta.showLoading({ titulo: 'Revalidando reglas...'});
            try {
                const requestRevalidar = await new _privilegeServices().evaluarClienteMotorDecisiones(infoCliente?.cliente?.codigoCliente);
                
                if (esValido(requestRevalidar)) {
                    showToast(
                        <div style={{ padding: '8px' }}>
                            <strong>Validación exitosa.</strong>
                        </div>,
                        ToastType.Success
                    );

                    const textoBusqueda = await (selectedRadio === 'Documento'
                        ? infoCliente?.cliente?.documento?.numeroDocumento
                        : infoCliente?.cliente?.codigoCliente
                    );

                    if (esValido(textoBusqueda)) {
                        let values : FormValues = { searchInput: textoBusqueda };
                        const formikHelpers = { setSubmitting: () => values } as unknown as FormikHelpers<FormValues>;
                        await buscarCliente(values, formikHelpers);
                    }
                }
                alerta.closeAlert();
            } catch (error) {
                alerta.closeAlert();
                showToast(
                    <div style={{ padding: '8px' }}>
                        <strong>Error al intentar validar las reglas</strong>
                    </div>,
                    ToastType.Error
                );
            }
        });
    };
    
    const resetBusquedaCliente = () => {
        setDatosBasicosCliente(null);
        setInfoCliente(null);
        setClienteExistente(false);
        setCodClienteBusqueda('');
        setRules([]);
        setMostrarBotonValidarReglas(false);
        setTipoSeguimientoOptions([]);
        setTipoSeguimiento(null);
    };

    const validarEntrada = (input: string): boolean => {
        const numericRegex = /^[0-9]*$/;
        return input.trim() !== '' && numericRegex.test(input);
    };

    const buscarClientePorDni = async (dni: string) => {
        return await new _privilegeServices().getClientePrivilegePorDni(trimTexto(dni)) ?? {};
    };

    const buscarClientePorCodigo = async (codigoCliente: string) => {
        return await new _privilegeServices().getClientePrivilegePorCodigo(trimTexto(codigoCliente)) ?? {};
    };

    const obtenerDatosBasicosCliente = async (dni: string) => {
        return await new _clientesServices().getDatosBasicosPorNroDocumento(trimTexto(dni)) ?? {};
    };

    const obtenerReglasCliente = async (codigoCliente: string) => {
        return await new _privilegeServices().getReglasPrivilege(codigoCliente) ?? {};
    };

    const manejarClienteExistente = async (existeCliente: any) => {
        let numeroDocumento = existeCliente?.cliente?.documento?.numeroDocumento;
        const resDatosBasicosCliente = await obtenerDatosBasicosCliente(numeroDocumento);
        
        if (esValido(resDatosBasicosCliente)) {
            setDatosBasicosCliente(resDatosBasicosCliente);
            setInfoCliente(existeCliente);
            setClienteExistente(true);
            setCodClienteBusqueda(existeCliente.cliente.codigoCliente);
            const estadoCliente = existeCliente.estado.descripcion;

            if (estadoCliente.toUpperCase() === 'PRIVILEGE') {
                setTipoSeguimientoOptions([{ id: 19, value: 'Exclusión', label: 'Exclusión' }]);
                setTipoSeguimiento({ id: 19, value: 'Exclusión', label: 'Exclusión' });
            } else {
                setTipoSeguimientoOptions([{ id: 18, value: 'Inclusión', label: 'Inclusión' }]);
                setTipoSeguimiento({ id: 18, value: 'Inclusión', label: 'Inclusión' });
            }

            const reglas = await obtenerReglasCliente(existeCliente.cliente.codigoCliente);
            setMostrarBotonValidarReglas(true);

            if (esValido(reglas)) {
                setRules(reglas);
            } else {
                showToast(
                    <div style={{ padding: '8px' }}>
                        <strong>No se encontraron reglas para el cliente</strong>
                    </div>,
                    ToastType.Warning
                );
                setRules([]);
            }
        } else {
            setMostrarBotonValidarReglas(false);
        }
        alerta.closeAlert();
    };

    const buscarCliente = async (values: FormValues, { setSubmitting }: FormikHelpers<FormValues>) => {
        resetBusquedaCliente();

        if (validarEntrada(values.searchInput)) {
            setAnalizandoCliente(true);
            alerta.showLoading({});
            
            const existeCliente = await (selectedRadio === 'Documento'
                ? buscarClientePorDni(values.searchInput)
                : buscarClientePorCodigo(values.searchInput));            

            if (esValido(existeCliente)) {
                await manejarClienteExistente(existeCliente);
            } else {
                showToast(
                    <div style={{ padding: '8px' }}>
                        <strong>El cliente no existe.</strong>
                    </div>,
                    ToastType.Warning
                );
            }
            alerta.closeAlert();
            setAnalizandoCliente(false);
        } else {
            showToast(
                <div style={{ padding: '8px' }}>
                    <strong>El valor ingresado no es valido.</strong>
                </div>,
                ToastType.Warning
            );
        }
        setSubmitting(false);
        alerta.closeAlert();
    };


    return (
        <>
            <div className="title-section" style={{ marginBottom: '0px' }}>
                <h2>Inicio de Seguimiento Privilege</h2>
            </div>
            <span style={{ marginRight: 10, marginBottom: 10 }}></span>
            <div className="row">
                <div className="col" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <div className="card mt-2" style={{ border: '1px solid rgb(223 223 223)', width: '485px', margin: '0 auto', boxShadow: 'rgba(0, 0, 0, 0.16) 0px 10px 36px 0px, rgba(0, 0, 0, 0.06) 0px 0px 0px 1px' }}>
                        <div className="card-header" style={{ backgroundColor: 'transparent', border: 'none' }}>
                            <label htmlFor="busquedaCliente" style={{ fontWeight: 'bold', textTransform: 'uppercase', fontSize: '16px', textAlign: 'center' }}>Buscar Cliente</label>
                        </div>
                        <div className="card-body" style={{ border: 'none', paddingTop: '5px', paddingLeft: '16px', paddingBottom: '4px' }}>
                            <Formik
                                initialValues={{ searchInput: '' }}
                                onSubmit={buscarCliente}
                            >
                                {({ isSubmitting, values }) => (
                                    <Form>
                                        <div className="radio-container">
                                            <label>
                                            
                                            <input
                                                type="radio"
                                                name="searchType"
                                                value="Codigo"
                                                checked={selectedRadio === "Codigo"}
                                                onChange={() => setSelectedRadio("Codigo")}
                                            />
                                              Código
                                            </label>
                                            <label>
                                            <input
                                                type="radio"
                                                name="searchType"
                                                value="Documento"
                                                checked={selectedRadio === "Documento"}
                                                onChange={() => setSelectedRadio("Documento")}
                                            />
                                              Documento
                                            </label>
                                        </div>
                                        
                                        <div className="form-group" style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                                            <Field
                                                type="text"
                                                name="searchInput"
                                                className="form-control"
                                                placeholder="Ingrese el código o documento.-"
                                                style={{ flex: '1', marginRight: '10px' }}
                                                value={values?.searchInput}
                                                defaultValue=""
                                                searchType={selectedRadio}
                                            />
                                            <Button type="submit" style={{ backgroundColor: '#1d428a', color: 'white' }} disabled={isSubmitting}>
                                                Buscar
                                            </Button>
                                        </div>
                                    </Form>
                                )}
                            </Formik>
                            <Row className="justify-content-between align-items-center">
                                <Col className="text-center">
                                    {clienteExistente && mostrarBotonValidarReglas && (
                                        <Button type="button" style={{ backgroundColor: 'rgb(150 152 162 / 97%)', color: 'white', marginRight: '0px', marginBottom: '8px', marginTop: '20px' }} onClick={validarReglas}>
                                            <i className="fas fa-project-diagram"></i>
                                            <strong className='ml-2'>Re-Validar Reglas</strong>
                                        </Button>
                                    )}
                                </Col>
                            </Row>
                        </div>
                    </div>
                </div>
            </div>
            {clienteExistente && (
                <div className="col mt-4" style={{ width: '100%', paddingLeft: '0px', paddingRight: '0px' }}>
                    <div className="card" style={{ padding: '18px', borderRadius: '14px', border: '1px solid #9b9b9b', boxShadow: 'rgba(0, 0, 0, 0.2) 0px 20px 30px' }}>
                        <div className="row align-items-center">
                            <div className="col-auto">
                                <i className="fas fa-portrait fa-4x" style={{ color: '#4d79d1' }}></i>
                            </div>
                            <div className="col" style={{ padding: '0px' }}>
                                <span>Nombre:</span> <br />
                                <span style={{ marginTop: '4px', fontWeight: 'bold' }}>
                                    {datosBasicosCliente?.primerNombre} {datosBasicosCliente?.segundoNombre} {datosBasicosCliente?.primerApellido} {datosBasicosCliente?.segundoApellido}
                                </span>
                            </div>
                            <div className="col" style={{ textAlign: 'right' }}>
                                <Button type="button" style={{ backgroundColor: '#1d428a', color: 'white' }} onClick={handleVerAtributos}>
                                    <i className="fas fa-eye"></i> Ver atributos ({infoCliente?.cliente?.detalles.length})
                                </Button>
                                <ClienteAtributosModal show={showModal} onHide={handleCloseModal} datosCliente={infoCliente?.cliente?.detalles} />
                            </div>
                        </div>
                        <div className="row mt-3">
                            <div className="col-4">
                                <span>Estado del cliente: </span>
                            </div>
                            <div className="col">
                                <span style={{ marginTop: '4px', fontWeight: 'bold' }}>
                                    {(infoCliente?.estado?.descripcion === 'NUEVO' || infoCliente?.estado?.descripcion === 'PENDIENTE') ? 'PROSPECTO' : infoCliente?.estado?.descripcion}
                                </span>
                            </div>
                        </div>
                        <div className="row">
                            <div className="col-4">
                                <span>
                                    Promedio de depósito:
                                </span>
                            </div>
                            <div className="col">
                                <span style={{ marginTop: '4px', fontWeight: 'bold' }}>
                                   {formatMonto(datosAtributos(infoCliente.cliente.detalles, "DEPOSITO"))}
                                </span>
                            </div>
                        </div>
                        <div className="row">
                            <div className="col-4">
                                <span>
                                    Ingresos:
                                </span>
                            </div>
                            <div className="col">
                                <span style={{ marginTop: '4px', fontWeight: 'bold' }}>
                                {formatMonto(datosAtributos(infoCliente.cliente.detalles, "INGRESOS"))}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            )}
            {clienteExistente && rules.length > 0 && (
                <div className="empty-section mt-4" style={{ borderRadius: '8px', boxShadow: 'rgba(0, 0, 0, 0.2) 0px 20px 30px', border: '1px solid #9b9b9b', backgroundColor: 'white' }}>
                    <h4 className="mb-3" style={{ textAlign: 'center' }}>Reglas</h4>
                    <div id="style-4" className="cards-section force-overflow scroll-bar" style={{ marginLeft: '0px', maxHeight: '600px', overflowY: 'auto', borderRadius: '12px', backgroundColor: 'rgb(73 73 73 / 12%)' }}>
                        <Row>
                            {rules.map((rule) => (
                                <Col md="4" sm="12" key={rule?.idRegla}>
                                    <Card className={`mb-4 ${rule?.clienteCumple ? 'bg-success' : 'bg-danger'}`} style={{ boxShadow: 'rgba(6, 24, 44, 0.4) 0px 0px 0px 2px, rgba(6, 24, 44, 0.65) 0px 4px 6px -1px, rgba(255, 255, 255, 0.08) 0px 1px 0px inset' }}>
                                        <CardBody>
                                            <CardTitle>{rule?.descripcion}</CardTitle>
                                            <CardText>{rule?.mensajeDeError}</CardText>
                                            <div style={{ textAlign: 'right' }}>
                                                <span className="validation-indicator" style={{ fontSize: '17px' }}>
                                                    {rule?.clienteCumple ? <i className="fas fa-check-circle"></i> : <i className="fas fa-times-circle"></i>}
                                                </span>
                                                <strong style={{ fontSize: '17px' }}>
                                                    {rule?.clienteCumple ? ' Cumple' : ' No cumple'}
                                                </strong>
                                            </div>
                                        </CardBody>
                                    </Card>
                                </Col>
                            ))}
                        </Row>
                    </div>
                    <div className="empty-section mt-4" style={{ borderRadius: '8px', border: '1px solid #9b9b9b' }}>
                        <Row className="align-items-center justify-content-end">
                            <Col>
                                <p style={{ fontWeight: 'bold', display: 'inline-block', marginRight: '10px' }}>Tipo de seguimiento:</p>
                                <Dropdown isOpen={dropdownOpen} toggle={toggleDropdown} style={{ display: 'inline-block' }}>
                                    <DropdownToggle caret>
                                        {tipoSeguimiento.label}
                                    </DropdownToggle>
                                    <DropdownMenu>
                                        {tipoSeguimientoOptions.map(option => (
                                            <DropdownItem
                                                key={option.id}
                                                onClick={() => setTipoSeguimiento(option)}
                                                   disabled={(estado === 1 && option.label === 'Exclusión') || (estado === 3 && option.label === 'Inclusión')}>
                                                {option.label}
                                            </DropdownItem>
                                        ))}
                                    </DropdownMenu>
                                </Dropdown>
                            </Col>
                            <Col style={{ textAlign: 'center' }}>
                                <Button type="button" style={{ backgroundColor: '#1d428a', color: 'white' }} onClick={iniciarSeguimiento} disabled={analizandoCliente}>
                                    Iniciar Seguimiento
                                </Button>
                            </Col>
                        </Row>
                    </div>
                </div>
            )}
            {(clienteExistente && infoCliente?.estado?.descripcion?.toUpperCase() == 'PRIVILEGE' && (rules.length === 0 || rules == null)) && (
                <div className="empty-section mt-4" style={{ borderRadius: '8px', border: '1px solid #9b9b9b' }}>
                    <Row className="align-items-center justify-content-end">
                        <Col>
                            <p style={{ fontWeight: 'bold' }}>Tipo de seguimiento:</p>
                            <Dropdown isOpen={dropdownOpen} toggle={toggleDropdown}>
                                <DropdownToggle caret>
                                    {tipoSeguimiento.label}
                                </DropdownToggle>
                                <DropdownMenu>
                                    {tipoSeguimientoOptions.map(option => (
                                        <DropdownItem key={option.id} onClick={() => setTipoSeguimiento(option)}>{option.label}</DropdownItem>
                                    ))}
                                </DropdownMenu>
                            </Dropdown>
                        </Col>
                    </Row>
                </div>
            )}
            {(clienteExistente && infoCliente?.estado?.descripcion?.toUpperCase() != 'PRIVILEGE' && (rules.length === 0 || rules == null)) && (
                <div className="empty-section mt-4" style={{ borderRadius: '8px', border: '1px solid #9b9b9b' }}>
                    <Row className="align-items-center justify-content-end">
                        <Col>
                            <p style={{ fontWeight: 'bold' }}>Tipo de seguimiento:</p>
                            <Dropdown isOpen={dropdownOpen} toggle={toggleDropdown}>
                                <DropdownToggle caret>
                                    {tipoSeguimiento.label}
                                </DropdownToggle>
                                <DropdownMenu>
                                    {tipoSeguimientoOptions.map(option => (
                                        <DropdownItem key={option.id} onClick={() => setTipoSeguimiento(option)}>{option.label}</DropdownItem>
                                    ))}
                                </DropdownMenu>
                            </Dropdown>
                        </Col>
                        {<Col style={{ textAlign: 'center' }}>
                            <Button type="button" style={{ backgroundColor: '#1d428a', color: 'white' }} onClick={iniciarSeguimiento} disabled={analizandoCliente}>
                                Iniciar Seguimiento
                            </Button>
                        </Col>}
                    </Row>
                </div>
            )}
        </>
    );
};
export default InicioDeSeguimientoForm;