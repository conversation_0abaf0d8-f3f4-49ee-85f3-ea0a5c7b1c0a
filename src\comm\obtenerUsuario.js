import axios from 'axios';

export async function obtenerUsuario(userName) {

  const url = `${_spPageContextInfo.webAbsoluteUrl}/_api/web/siteusers(@v)?@v='${encodeURIComponent("i:0#.w|bancontinental\\" + userName)}'`;

  console.log("url obtenerUsuario:", url);

  try {
    const response = await axios({
      method: 'get',
      url: url,
      headers: {
        'Accept': 'application/json;odata=verbose',
        'Content-Type': 'application/json;odata=verbose'
      }
    });
    console.log("response obtenerUsuario:", response);
    localStorage.setItem("respuestaEstadoUsuario", JSON.stringify(response.status));
    localStorage.setItem("respuestaIdUsuario", JSON.stringify(response.data.d.Id));
    return response;
  } catch (error) {
    console.log('Error catch obtenerUsuario:', error);
    console.log('Status Error:', error.response.status);
    localStorage.setItem("respuestaEstadoUsuario", JSON.stringify(error.response.status));
    return (
      <div className='modal' tabIndex={-1} id="modalValidacion">
        <div className='modal-dialog'>
          <div className='modal-content'>
            <div className='modal-header'>
              <h5 className='modal-title'></h5>
              <button className='btn-close' data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div className='modal-body'>
              <p className='text-danger font-weight-bold'>{`No se encuentra el usuario: ${userName}`}</p>
            </div>
            <div className='modal-footer'>
              <button className='btn btn-secondary' data-bs-dismiss="modal">Aceptar</button>
            </div>
          </div>
        </div>
      </div>)
  }
}



