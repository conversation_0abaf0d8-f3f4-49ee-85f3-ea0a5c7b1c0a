import { getAmbienteContexto } from "../../utilities/contextInfo";
import { Ambiente } from "../../entities/Enums/enumAmbientes";
import { getJWTToken } from "../../utilities/getJWTToken";

export type ApiRecursosHumanosUtilitarios = {
  Authorization: string,
  'Subscription-Key': string,
  'Content-Type': string,
  Accept: string,
  'Cache-Control': string
}

export async function getApiRecursosHumanosUtilitarios(cache: string) {
  let _apiBpmRecursosHumanosUtilitarios: ApiRecursosHumanosUtilitarios;
  let _ambiente = getAmbienteContexto();
  let _jwtToken = await getJWTToken('internal_metiri');

  switch (_ambiente) {
    case Ambiente.dev:
      _apiBpmRecursosHumanosUtilitarios = {
        Authorization: 'Bearer ' + _jwtToken,
        "Subscription-Key": '5c5a22191bbe4abc9d9571803e4d8aac',
        "Content-Type": 'application/json',
        Accept: 'application/json',
        'Cache-Control': cache
      }
      return _apiBpmRecursosHumanosUtilitarios;

    case Ambiente.prod:
      _apiBpmRecursosHumanosUtilitarios = {
        Authorization: 'Bearer ' + _jwtToken,
        "Subscription-Key": '',
        "Content-Type": 'application/json',
        Accept: 'application/json',
        'Cache-Control': cache
      }
      return _apiBpmRecursosHumanosUtilitarios;
  }
}



