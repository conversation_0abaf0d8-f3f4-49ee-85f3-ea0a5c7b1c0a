/// <reference types="webpack/module" />

import React from "react";
import { createRoot } from "react-dom/client";
import { ModalPortal } from "../ModalPortal";
import { FormConfirmarOperacion } from "./FormConfirmarOperacion";
import { DatosProvider } from "../contextProvider/datosProveedorContext";

export const BtnDecision = (decision: string, idTarea: string, codOperacion: string) => {

  console.log('esta es mi tarea?')
  console.log(idTarea)

  const confirmacionPopupCall = () => {
    const idEl = 'ModalAdjuntos'
    const popUpJSX = <DatosProvider><FormConfirmarOperacion decision={decision} idTarea={idTarea} codOperacion={codOperacion} /></DatosProvider>
    const node = document.createElement("div");
    node.id = 'ModalAdjuntos'
    let _root = document.getElementById('container')
    _root.appendChild(node)
    const _modalEl = document.getElementById('ModalAdjuntos')
    const _rootModal = createRoot(_modalEl)
    _rootModal.render(<ModalPortal jsx={popUpJSX} idEL={idEl}></ModalPortal>)
  }

  return (<><a href="#" onClick={(e) => confirmacionPopupCall()} data-toggle="modal" data-target="#filtrosAvanzados1" className="btn btn-outline-fun-blue">{decision}</a></>)
}