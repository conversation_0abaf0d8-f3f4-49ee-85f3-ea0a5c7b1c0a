import { isEmpty, isNull, isUndefined } from "lodash";

export const concatFilters = (filters: any[]) => {
  let retorno: string
  let _esPrimero = true
  filters.forEach(element => {
    if (_esPrimero) {
      retorno = '?'
      _esPrimero = false
      if (!(isNull(element) || isUndefined(element) || isEmpty(element) || element === '')) {
        retorno = retorno + element
      }
      return;
    }
    if (isNull(element) || isUndefined(element) || isEmpty(element) || element === '') {
      return
    }
    else {
      if (retorno === '?') {
        retorno = retorno + element
      }
      else {
        retorno = retorno + '&' + element
      }
    }
  });
  return retorno
}

export const concatSPQueryFilters = () => {

}