import React, { useState, useEffect } from "react";
import { Formik, Field, Form, FormikHelpers } from 'formik';
import "./AdicionalesPrivilegeStyle.css";
import { getUsuario } from '../../comm/apiUsuarios';
import alerta from './../../utilities/createAlerts';
import { esValido, trimTexto } from "../../utilities/dataUtil";
import _privilegeServices from '../../services/privilegeServices';
import { showToast, ToastType } from "../../utilities/toastUtils";
import _clientesServices from '../../services/clientesServices';
import "./AdicionalesPrivilegeStyle.css";
import { getUserName } from '../../utilities/_spPageContextInfo'
import { insertarEnListaSharePoint } from "./services/sharepointService";

const AdicionalesPrivilege: React.FC<{}> = () => {
  //const [tab, setTab] = useState("Pendientes");
  const validarEntrada = (input: string): boolean => {
    const numericRegex = /^[0-9]*$/;
    return input.trim() !== '' && numericRegex.test(input);
  };
  const [tab, setTab] = useState("Pendiente"); // Estado actual del filtro
  const [filteredSolicitudes, setFilteredSolicitudes] = useState([]); // Solicitudes filtradas
  const [desdeFecha, setDesdeFecha] = useState(""); // Filtro de fecha inicial
  const [hastaFecha, setHastaFecha] = useState(""); // Filtro de fecha final

  const [tipoCliente, setTipoCliente] = useState('Cliente');
  const [datosBasicosCliente, setDatosBasicosCliente] = useState<any>({});
  const [clienteExistente, setClienteExistente] = useState(false);
  const [analizandoCliente, setAnalizandoCliente] = useState(false);
  const [selectedRadio, setSelectedRadio] = useState("Codigo");
  const [selectedRadioAd, setSelectedRadioAd] = useState("Codigo");
  const [infoCliente, setInfoCliente] = useState<any>({});
  const [infoClienteAdicional, setInfoClienteAdicional] = useState<any>({});
  const [mostrarAdicionalBlock, setMostrarAdicionalBlock] = useState(false);

  const buscarClientePorDni = async (dni: string) => {
    return await new _privilegeServices().getClientePrivilegePorDni(trimTexto(dni)) ?? {};
  };
  const [selectedItems, setSelectedItems] = useState<string[]>([]); // Nuevo estado para controlar las selecciones
  const [usuarioCodigo, setUsuarioCodigo] = useState<string | null>(null);
  const [datosUsuario, setDatosUsuario] = useState<any>({});
  const [ldapUser, setLdapUser] = useState('');
  

  useEffect(() => {
    const obtenerUsuarioCodigo = async () => {
      const usuario = getUserName();
      setLdapUser(usuario.toString());
      const usuarioInfo = (await getUsuario(usuario)) as GetUsuariosResponse;
      setUsuarioCodigo(usuarioInfo.codigo);
      setDatosUsuario(usuarioInfo);
    };

    obtenerUsuarioCodigo();
  }, []);

  const buscarClientePorCodigo = async (codigoCliente: string) => {
    return await new _privilegeServices().getClientePrivilegePorCodigo(trimTexto(codigoCliente)) ?? {};
  };

  const obtenerDatosBasicosCliente = async (dni: string) => {
    return await new _clientesServices().getDatosBasicosPorNroDocumento(trimTexto(dni)) ?? {};
  };
  interface FormValues {
    searchInput: string;
  }
  // Mapa de códigos para los estados
  const estadoCodes: { [key: string]: string } = {
    Pendiente: "2",
    Rechazado: "4",
    Aprobado: "6",
  };
  const buscarCliente = async (values: FormValues, { setSubmitting }: FormikHelpers<FormValues>, tipoCliente: string) => {
    if (validarEntrada(values.searchInput)) {
      setAnalizandoCliente(true);
      alerta.showLoading({});
      const existeCliente = await (selectedRadio === 'Documento'
        ? buscarClientePorDni(values.searchInput)
        : buscarClientePorCodigo(values.searchInput));
      if (!esValido(existeCliente)) {
        showToast(
          <div style={{ padding: '8px' }}>
            <strong>El valor insertado no pertenece a una cuenta de cliente Privilege o no tiene marca Privilege.</strong>
          </div>,
          ToastType.Warning

        );
      } else {
        let numeroDocumento = existeCliente?.cliente?.documento?.numeroDocumento;
        const resDatosBasicosCliente = await obtenerDatosBasicosCliente(numeroDocumento);
        if (esValido(resDatosBasicosCliente)) {
          setDatosBasicosCliente(resDatosBasicosCliente);
          setInfoCliente(existeCliente);
          setClienteExistente(true);
        }
      }
      alerta.closeAlert();
      setAnalizandoCliente(false);
    } else {
      showToast(
        <div style={{ padding: '8px' }}>
          <strong>El valor ingresado no es valido.</strong>
        </div>,
        ToastType.Warning
      );
    }
    setSubmitting(false);
    alerta.closeAlert();
  };
  const [selectedSolicitudes, setSelectedSolicitudes] = useState<string[]>([]);

  const handleSelection = (e: React.ChangeEvent<HTMLInputElement>, solicitud: any) => {
    const id = solicitud.id; // Usar ID único para identificar la solicitud
    if (e.target.checked) {
      setSelectedItems((prev) => [...prev, id]); // Agregar el ID al estado
    } else {
      setSelectedItems((prev) => prev.filter((item) => item !== id)); // Eliminar el ID del estado
    }
  };
  const handleTabChange = (estado: string) => {
    setTab(estado); // Cambia el estado seleccionado
    listarSolicitudesPrivilege(estadoCodes[estado]); // Carga las solicitudes para el estado correspondiente
  };


  const listarSolicitudesPrivilege = async (estado: string) => {
    try {
      const response = await new _privilegeServices().getClientePrivilegesSolicitudes(trimTexto(estado));
      console.log("Respuesta del backend para estado:", estado, response);

      if (response) {
        const capitalizeFirstLetter = (text: string | null | undefined): string =>
          text ? text.charAt(0).toUpperCase() + text.slice(1).toLowerCase() : 'N/A';

        // Usamos Promise.all para procesar en paralelo cada item
        const filteredSolicitudes = await Promise.all(
          response.map(async (item: any) => {
            // Llamada asíncrona para buscar info del cliente
            const { success, data } = await buscarClienteAdicional(item.codigoCliente, false);

            const nombre1 = [
              (data?.primerNombre || "").trim(),
              (data?.segundoNombre || "").trim(),
            ].filter(Boolean).join(" ")
              + ", "
              + [
                (data?.primerApellido || "").trim(),
                (data?.segundoApellido || "").trim(),
              ].filter(Boolean).join(" ");


            // Retornar el objeto final de la solicitud
            return {
              id: item.codigoCliente,
              nombre: nombre1, // Este es el nombre completo
              ci: item.documento?.numeroDocumento?.replace(/^0+/, '') || 'N/A',
              estado: item.estado?.descripcion || 'N/A',
              fechaCarga: new Date(item.fecha).toLocaleDateString('es-ES'),
              estadoSolicitud: item.estado?.descripcion || 'N/A',
              fechaResolucion: item.fechaAutoriza
                ? new Date(item.fechaAutoriza).toLocaleString('es-ES')
                : null,
              accionSolicitud: item.accion || 'N/A', //nuevo
            };
          })
        );

        setFilteredSolicitudes(filteredSolicitudes);
      } else {
        setFilteredSolicitudes([]);
      }
    } catch (error) {
      console.error("Error al listar solicitudes privilege:", error);
      setFilteredSolicitudes([]);
    }
  };

  const buscarClienteAdicional = async (codigoCliente: string, esCedula: boolean) => {
    alerta.showLoading({});
    try {
      console.log("buscarClienteAdicional", codigoCliente, esCedula);
      const clienteAdicional = await new _privilegeServices().getClientePrivilegeAdicional(trimTexto(codigoCliente), esCedula);
      if (esValido(clienteAdicional)) {
        alerta.closeAlert();
        return {
          success: true,
          data: clienteAdicional,
        };
      } else {
        showToast("El cliente adicional no existe.", ToastType.Warning);
        return {
          success: false,
          data: null,
        };
      }
    } catch (error) {
      console.error("Error al buscar cliente adicional:", error);
      showToast("Error al buscar el cliente adicional.", ToastType.Error);
      return {
        success: false,
        data: null,
      };
    } finally {
      alerta.closeAlert();
    }
  };
  const [emitirTC, setEmitirTC] = useState(false);
  const [tipoSolicitud, setTipoSolicitud] = useState(false);

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setEmitirTC(event.target.checked); // Actualiza el estado con el valor del checkbox
  };

  const handleCheckboxChangeTipoSol = (event: React.ChangeEvent<HTMLInputElement>) => {
    setTipoSolicitud(event.target.checked); // Actualiza el estado con el valor del checkbox
  };

  interface GetUsuariosResponse {
    codigo: string;
    nombre: string;
    sucursal: {
      codigo: string;
      descripcion: string;
    };
    departamento: {
      codigo: number;
      descripcion: string;
    };
    correo: string;
    nivel: number;
  }


  const handleGuardar = async () => {
    try {

      const usuario = getUserName();
      console.log("Usuario obtenido con getUserName: ", usuario);

      const usuarioInfo = (await getUsuario(usuario)) as GetUsuariosResponse;
      const usuarioCodigo = usuarioInfo.codigo;
      console.log("Código de usuario obtenido: ", usuarioCodigo);

      const accion = Boolean(tipoSolicitud); // Asegura que sea true o false
      console.log("Acción definida (inclusión o exclusión): ", accion);

      let datoCorrecto: boolean = true;

      if (infoCliente.cliente.documento.numeroDocumento.trim().replace(/^0+/, "") ===
      infoClienteAdicional.documento.trim().replace(/^0+/, "")) {
        showToast(
          "El cliente adicional no puede ser el mismo que el títular",
          ToastType.Error
        );
        console.log("El cliente adicional no puede ser el mismo que el títular");
        datoCorrecto = false;
      }

      if (datoCorrecto == true)
      {
          if (accion == false) {
            // Inclusión (Guardado actual)
            const GeneraTc = emitirTC;

            const datos = {
              CodigoPrivilege: infoCliente?.cliente?.codigoCliente, // Código del cliente
              GeneraTc, // Estado de Generar TC
              UserCarga: usuarioCodigo, // Usuario que realiza la carga
            };

            const CodigoClienteAdicional = infoClienteAdicional.codigoCliente.trim();
            console.log("Datos de inclusión a guardar: ", datos, CodigoClienteAdicional);

            const response = await new _privilegeServices().putInclusionAdicionalPrivilege(CodigoClienteAdicional, datos);

            if (response) {
              showToast("Datos guardados correctamente como Privilege Adicional.", ToastType.Success);
              console.log("Seguimiento iniciado correctamente.");

              const match = response.match(/Operación:\s*([A-Za-z0-9]+)/);
              const operacion = match[1]; // Captura el número de operación

              await insertarSharepoint(operacion);

            } else {
              showToast("Error al guardar los datos de inclusión.", ToastType.Error);
            }
          } else {
            // Exclusión
            const datosExclusion = {
              UserCarga: usuarioCodigo,
            };

            const CodigoClienteEx = infoClienteAdicional.codigoCliente.trim();
            console.log("Datos de exclusión a procesar: ", datosExclusion, CodigoClienteEx);

            const response = await new _privilegeServices().putExclusionAdicionalPrivilege(CodigoClienteEx, datosExclusion);

            if (response) {
              showToast("Cliente desmarcado correctamente como Privilege Adicional.", ToastType.Success);
              console.log("Seguimiento iniciado correctamente.");

              const match = response.match(/Operación:\s*([A-Za-z0-9]+)/);
              const operacion = match[1]; // Captura el número de operación

              await insertarSharepoint(operacion);

            } else {
              showToast("Error al desmarcar el cliente como Privilege Adicional.", ToastType.Error);
            }
          }
        }
    } catch (error) {
      console.error("Error inesperado:", error);
      showToast("Error inesperado durante la operación.", ToastType.Error);
    }
  };

  const handleCancelar = async () => {
    setEmitirTC(false);
    setTipoSolicitud(false);
  };

  const insertarSharepoint = async (operacion: string) => {
    return new Promise(async (resolve, reject) => {
        try {
            alerta.showLoading({}); // Mostrar "Cargando"

            showToast(
                <div style={{ padding: '8px' }}>
                    <strong>Operación: {operacion}</strong>
                </div>,
                ToastType.Success
            );

            const element = {
                codigoOperacion: operacion, // Código de operación generado previamente
            };

            if (!infoClienteAdicional || !infoClienteAdicional.codigoCliente) {
                throw new Error("No se encontró el código del cliente.");
            }

            const datac = {
                codigoSubProducto: 22, // Código del subproducto
                codigoCliente: infoClienteAdicional.codigoCliente.trim(), // Código del cliente
            };

            const insertarSharepoint = await insertarEnListaSharePoint(element, datac);

            if (esValido(insertarSharepoint)) {
                showToast(
                    <div style={{ padding: '8px' }}>
                        <strong>Respuesta.</strong><br />
                        <strong>Operación: {insertarSharepoint}</strong>
                    </div>,
                    ToastType.Info
                );
            }

            const sharepointDato = {
                producto: 7,
                subproducto: 22,
                operacion: operacion,
                sharedpointId: insertarSharepoint,
            };

            await new _privilegeServices().insertarSharepointId(sharepointDato);

            resolve(true); // Éxito

        } catch (error) {
            console.error('Error al iniciar el seguimiento:', error);
            showToast(
                <div style={{ padding: '8px' }}>
                    <strong>Ha ocurrido un error al iniciar el seguimiento</strong>
                </div>,
                ToastType.Error
            );
            resolve(false); // Falla

        } finally {
            alerta.closeAlert(); // Cerrar explícitamente después de completar el proceso
        }
    });
};

  // Función para convertir fechaCarga a objeto Date
  const parseFechaCarga = (fechaCarga: string) => {
    const [day, month, year] = fechaCarga.split("-"); // Divide dd-mm-yyyy
    return new Date(`${year}-${month}-${day}`); // Convierte a yyyy-mm-dd y crea Date
  };
  // Estado para controlar el texto de búsqueda y las solicitudes filtradas
  const [searchInput, setSearchInput] = useState(""); // Control del texto de búsqueda

  // Función para manejar la búsqueda
  const handleSearch = () => {
    let results = [...filteredSolicitudes];
    const lowerCaseInput = searchInput.trim().toLowerCase();

    if (lowerCaseInput === "") {
      setFilteredSolicitudes(results);
    } else {
      // Ojo: Este .find te devolverá solo el PRIMER elemento que coincida
      // Si quieres todas las coincidencias, usa .filter
      results = results.filter((solicitud) => {
        console.log("Comparando con solicitud:", solicitud);

        const idCliente = solicitud.id
          ? solicitud.id.toString().toLowerCase()
          : "";
        const ciCliente = solicitud.ci
          ? solicitud.ci.toString().toLowerCase()
          : "";

        return (
          idCliente.includes(lowerCaseInput) ||
          ciCliente.includes(lowerCaseInput)
        );
      });

      setFilteredSolicitudes(results);
    }
  };

  const handleFilter = () => {
    // Empieza con todas las solicitudes cargadas (filteredSolicitudes)
    let results = [...filteredSolicitudes];

    // Filtrar por rango de fechas
    if (desdeFecha) {
      const desdeFechaDate = new Date(desdeFecha); // Convierte desdeFecha a Date
      results = results.filter((solicitud) => {
        const fechaCargaDate = new Date(solicitud.fecha); // Convierte fechaCarga a Date
        return fechaCargaDate >= desdeFechaDate; // Compara las fechas
      });
    }

    if (hastaFecha) {
      const hastaFechaDate = new Date(hastaFecha); // Convierte hastaFecha a Date
      results = results.filter((solicitud) => {
        const fechaCargaDate = new Date(solicitud.fecha); // Convierte fechaCarga a Date
        return fechaCargaDate <= hastaFechaDate; // Compara las fechas
      });
    }

    // Actualiza el estado con las solicitudes filtradas por fecha
    setFilteredSolicitudes(results);
  };

  return (
    <div className="contenedor-principal">
      {/* Bandeja */}
      <div className="bandeja">
        <h2 className="titulo-bandeja" style={{ marginBottom: '0px' }}>Mi Bandeja</h2>

        {/* Filtros de botones */}
        <div className="filtros">
          <button
            className={tab === "Pendiente" ? "activo" : ""}
            onClick={() => handleTabChange("Pendiente")}
          >
            Pendientes
          </button>
          <button
            className={tab === "Aprobado" ? "activo" : ""}
            onClick={() => handleTabChange("Aprobado")}
          >
            Aprobados
          </button>
          <button
            className={tab === "Rechazado" ? "activo" : ""}
            onClick={() => handleTabChange("Rechazado")}
          >
            Rechazados
          </button>
        </div>


        <div className="filtro-fechas">
          <label>Fecha</label>
          <div className="filtro-fechas-desde">
            <span>Desde:</span>
            <input
              type="date"
              value={desdeFecha || ""} // Asegura que el valor sea una cadena vacía si no hay fecha
              onChange={(e) => {
                setDesdeFecha(e.target.value || ""); // Si el valor es vacío, establece ""
                handleFilter();
              }}
            />
          </div>
          <div className="filtro-fechas-hasta">
            <span>Hasta:</span>
            <input
              type="date"
              value={hastaFecha || ""} // Asegura que el valor sea una cadena vacía si no hay fecha
              onChange={(e) => {
                setHastaFecha(e.target.value || ""); // Si el valor es vacío, establece ""
                handleFilter();
              }}
            />
          </div>
        </div>

        {/* Buscador */}
        <div className="busqueda">
          <input
            type="text"
            placeholder="Buscar por C.I. o Código del cliente"
            className="input-busqueda"
            value={searchInput} // Control del valor del campo de texto
            onChange={(e) => setSearchInput(e.target.value)} // Actualizar estado al cambiar texto
          />
          <button className="btn-customAdicional" onClick={handleSearch}>
            Buscar
          </button>
        </div>

        <div>
          <div className="lista-solicitudes">
            <strong>Solicitudes</strong>
            <div>
              {filteredSolicitudes.length > 0 ? (
                filteredSolicitudes.map((solicitud, index) => (
                  <div
                    key={`${solicitud.id}-${index}`} // Clave única
                    className={`solicitud-item ${solicitud.estadoSolicitud.toLowerCase()}`}
                  >
                    <div className="checkbox-container">
                      <input
                        type="checkbox"
                        id={`solicitud-${solicitud.id}`} // ID único
                        className="checkbox-seleccion"
                        checked={selectedItems.includes(solicitud.id)} // Verifica selección
                        onChange={(e) => handleSelection(e, solicitud)} // Manejador de selección
                      />
                      <label htmlFor={`solicitud-${solicitud.id}`}></label>
                    </div>
                    <div>
                      <p>
                        <strong>{solicitud.id} - {solicitud.nombre}</strong>
                      </p>
                      <p>
                        C.I.: {solicitud.ci} | Estado: {solicitud.estado} | Acción: {solicitud.accionSolicitud}
                      </p>
                      <p>Fecha de carga: {solicitud.fechaCarga}</p>
                    </div>
                    <div className="estado-solicitud">
                    <div className="icono-estado">
                      {solicitud.estadoSolicitud === "Pendiente" && (
                        <span className="icono pendiente">⌛</span>  // Reloj de arena
                      )}
                      {(solicitud.estadoSolicitud === "Aprobado" || solicitud.estadoSolicitud === "ADICIONAL") && (
                        <span className="icono aprobado">✅</span>  // Checkmark en caja
                      )}
                      {solicitud.estadoSolicitud === "Rechazado" && (
                        <span className="icono rechazado">❌</span>  // Equis roja
                      )}
                    </div>
                      <div className="estado-fecha">
                        <span
                          className={`texto-estado ${solicitud.estadoSolicitud.toLowerCase() === "adicional" ? "aprobado" : solicitud.estadoSolicitud.toLowerCase()
                            }`}
                        >
                          {solicitud.estadoSolicitud === "PENDIENTE"
                            ? "Autorización Pendiente"
                            : solicitud.estadoSolicitud === "ADICIONAL"
                              ? "Aprobado"
                              : solicitud.estadoSolicitud}
                        </span>
                        {solicitud.fechaResolucion && (
                          <span className="fecha-estado">{solicitud.fechaResolucion}</span>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <p>No se encontraron resultados.</p>
              )}
            </div>
          </div>

        </div>

      </div>

      {/* Gestión */}
      <div className="gestion">
        <h2 className="titulo-gestion">Gestión de Pedido</h2>
        <div className="descripcionAdicional">
          <strong>Privilege Titular</strong>
          <p>Marcá el tipo de dato y completá el campo con los datos del Titular Privilege</p>
        </div>

        <div className="busqueda">
          <Formik
            initialValues={{ searchInput: '' }}
            onSubmit={(values, helpers) => buscarCliente(values, helpers, 'Cliente')}>
            {({ isSubmitting }) => (
              <Form>
                <div className="radio-container">
                  <label className="radio-label">
                    <Field
                      type="radio"
                      name="searchType"
                      value="Codigo"
                      checked={selectedRadio === 'Codigo'}
                      onChange={() => setSelectedRadio('Codigo')}
                    />
                    Código de Cliente
                  </label>
                  <label className="radio-label">
                    <Field
                      type="radio"
                      name="searchType"
                      value="Documento"
                      checked={selectedRadio === 'Documento'}
                      onChange={() => setSelectedRadio('Documento')}
                    />
                    Nro. Documento
                  </label>
                </div>

                <div className="input-busqueda">
                  <Field
                    type="text"
                    name="searchInput"
                    placeholder={`Ingrese el ${selectedRadio === 'Codigo' ? 'Código de Cliente' : 'Nro. Documento'}`}
                    className="input-custom"
                  />
                  <button
                    type="submit"
                    className="btn-customAdicional"
                    disabled={isSubmitting}
                  >
                    Buscar
                  </button>
                </div>
              </Form>
            )}


          </Formik>
        </div>

        {clienteExistente && datosBasicosCliente && (
          <>
            <div className="datos-cliente">
              <div className="avatar">
                {datosBasicosCliente.primerNombre.slice(0, 1)}
                {datosBasicosCliente.primerApellido.slice(0, 1)}
              </div>
              <div className="info-cliente">
                <p>
                  {datosBasicosCliente.primerNombre}, {datosBasicosCliente.segundoNombre}{" "}
                  {datosBasicosCliente.primerApellido}{" "}
                  {datosBasicosCliente.segundoApellido}
                </p>
                <div className="linea-horizontal">
                  <p>
                    <span>Cód.:</span> {infoCliente?.cliente?.codigoCliente || "N/A"}
                  </p>
                  <p>
                    <span>C.I.:</span> {infoCliente?.cliente?.documento?.numeroDocumento || "N/A"}
                  </p>
                  <p>
                    <span>Estado:</span> {infoCliente?.estado?.descripcion?.toUpperCase() || "N/A"}
                  </p>
                  <p>
                    <span>Oficial asignado:</span> {infoCliente?.cliente?.estado || "N/A"}
                  </p>
                </div>
              </div>
            </div>
            <div className="cliente-adicional">
              <div className="descripcionAdicional">
                <strong>Cliente Adicional</strong>
                <p>Selecciona el tipo de búsqueda y completa el campo para iniciar el proceso de búsqueda</p>
              </div>
              <div className="busqueda">
                <Formik
                  initialValues={{ searchInput: '' }} // El valor inicial debe coincidir con lo esperado
                  onSubmit={async (values, { setSubmitting }) => {
                    try {
                      const codigoCliente = values.searchInput.trim(); // Extraer el input
                      const esDocumento = selectedRadioAd === 'Documento'; // Determinar el booleano basado en la selección
                      if (codigoCliente) {
                        const response = await buscarClienteAdicional(codigoCliente, esDocumento); // Llamar al método con ambos valores
                        if (response.success) {
                          setInfoClienteAdicional(response.data); // Actualizar estado con los datos obtenidos
                          setMostrarAdicionalBlock(true); // Actualizar la bandera a `true`
                          console.log(response);
                        } else {
                          setMostrarAdicionalBlock(false); // Actualizar la bandera a `false`
                          showToast("Cliente no encontrado.", ToastType.Warning);
                        }
                      } else {
                        setMostrarAdicionalBlock(false); // Actualizar la bandera a `false`
                        showToast("Por favor ingrese un código de cliente válido.", ToastType.Warning);
                      }
                    } catch (error) {
                      console.error("Error al buscar cliente adicional:", error);
                      setMostrarAdicionalBlock(false); // Actualizar la bandera a `false`
                    } finally {
                      setSubmitting(false); // Finalizar el estado de carga
                    }
                  }}
                >

                  {({ isSubmitting }) => (
                    <Form>
                      <div className="radio-container">
                        <label className="radio-label">
                          <Field
                            type="radio"
                            name="searchType"
                            value="Codigo"
                            checked={selectedRadioAd === 'Codigo'}
                            onChange={() => setSelectedRadioAd('Codigo')}
                          />
                          Código de Cliente
                        </label>
                        <label className="radio-label">
                          <Field
                            type="radio"
                            name="searchType"
                            value="Documento"
                            checked={selectedRadioAd === 'Documento'}
                            onChange={() => setSelectedRadioAd('Documento')}
                          />
                          Nro. Documento
                        </label>
                      </div>
                      <div className="input-busqueda-adicional">
                        <Field
                          type="text"
                          name="searchInput"
                          placeholder={`Ingrese el ${selectedRadioAd === 'Codigo' ? 'Código de Cliente' : 'Nro. Documento'
                            }`}
                          className="input-custom"
                        />
                        <button
                          type="submit"
                          className="btn-custom2"
                          disabled={isSubmitting}
                        >
                          Buscar
                        </button>
                      </div>
                      {mostrarAdicionalBlock && (
                      <div className="adicional-block"> {/*nuevo*/}
                        {infoClienteAdicional && (
                          <div className="resultado-cliente">
                            <div className="linea-separadora"></div> {/* Línea superior */}
                            <div className="cliente-adicional-info">
                              <div className="avatar">
                                {infoClienteAdicional.primerNombre?.trim().slice(0, 1)}
                                {infoClienteAdicional.primerApellido?.trim().slice(0, 1) || "N/A"}
                              </div>
                              <div className="detalles-cliente">
                                <p className="nombre-cliente">
                                  {infoClienteAdicional.primerNombre?.trim()},{" "}
                                  {infoClienteAdicional.segundoNombre?.trim() || "N/A"}{" "}
                                  {infoClienteAdicional.primerApellido?.trim() || "N/A"}{" "}
                                  {infoClienteAdicional.segundoApellido?.trim() || "N/A"}
                                </p>
                                <div className="resultado-titulos">
                                  <div><strong>Cód.:</strong></div>
                                  <div><strong>C.I.:</strong></div>
                                  <div><strong>Estado:</strong></div>
                                  <div><strong>Marca:</strong></div>
                                  <div><strong>Oficial asignado:</strong></div>
                                </div>
                                <div className="resultado-datos">
                                  <div>{infoClienteAdicional.codigoCliente || "N/A"}</div>
                                  <div>{infoClienteAdicional.documento?.trim()?.replace(/^0+/, "") || "N/A"}</div>
                                  <div>N/A</div> {/* Estado: Placeholder para el futuro */}
                                  <div>N/A</div> {/* Marca: Placeholder para el futuro */}
                                  <div>{infoClienteAdicional.oficial || "N/A"}</div>
                                </div>
                              </div>
                            </div>
                            <div className="linea-separadora"></div> {/* Línea inferior */}
                          </div>

                        )}

                        <div className="checkbox-container">
                          <div className="checkbox-item">
                            <input 
                              type="checkbox" 
                              id="emitirTC" 
                              checked={emitirTC} 
                              onChange={handleCheckboxChange}
                            />
                            <label htmlFor="emitirTC">  Emitir TC Adicional</label>
                          </div>
                          
                          <div className="checkbox-item">
                            <input 
                              type="checkbox" 
                              id="tipoSolicitud" 
                              checked={tipoSolicitud} 
                              onChange={handleCheckboxChangeTipoSol}
                            />
                            <label htmlFor="tipoSolicitud">  Desmarcar como Privilege Adicional</label>
                          </div>
                        </div>

                        <div className="botones-container">
                          <button type="button" className="btn-cancelar" onClick={handleCancelar}>
                            Cancelar
                          </button>
                          <button type="submit" className="btn-guardar" onClick={handleGuardar}>
                            Guardar
                          </button>

                        </div>

                      </div>
                      )}

                    </Form>
                  )}
                </Formik>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default AdicionalesPrivilege;