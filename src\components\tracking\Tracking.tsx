import React, { useState, useEffect } from 'react';
import { getAmbienteContexto, getUrlBase } from '../../utilities/contextInfo';
import axios from 'axios';
import EndPoint from '../../comm/EndPoint.Config';
import { getApiProveedorFacturaHeaders } from '../../comm/contracts/ApiBpmPoliticasProcesosRequestHeaders';
import { Acciones } from '../../entities/Acciones';
import { JerarquiaDeEstado } from '../../entities/JerarquiaDeEstado';
import { DatosProvider, useProveedor } from '../contextProvider/datosProveedorContext';
import VerAutorizantes from '../verAutorizantes/VerAutorizantes';

const Tracking = () => {

  const { state } = useProveedor();

  let jsxEl: JSX.Element[];
  const [jsx, setJSX] = useState(jsxEl);

  const Params = {
    urlBase: getUrlBase(),
    ambiente: getAmbienteContexto(),
    request: axios.create(),
    endpoints: EndPoint.apiBpmPoliticaProcesos
  }

  let operacionNro = localStorage.getItem("operacionNumero");

  const getAcciones = async () => {
    let _cacheControl = 'max-age=60*60*10, private';
    let _headers = await getApiProveedorFacturaHeaders(_cacheControl);
    let _sturlEndpoint = Params.endpoints.vUno.get.acciones;
    let urlEndpoint = _sturlEndpoint.replace("${idCabecera}", `${state.operacionClienteNumero || state.numeroOperacionCliente}`);

    let config = {
      method: "get",
      url: Params.urlBase + urlEndpoint,
      headers: _headers
    }

    let promise = Params.request<Acciones>(config)
      .then(function (response) {
        return response.data;
      }).catch(function (error) {
        console.log("Error getTracking:", error);
      });

    let result = await promise;
    return result;
  }

  const getJSX = async (data: Acciones) => {

    let jsx = data.map((item) => {

      const fechaOriginal = new Date(item.fechaHoraAccion);
      const dia = fechaOriginal.getDate().toString().padStart(2, '0');
      const mes = (fechaOriginal.getMonth() + 1).toString().padStart(2, '0');
      const anio = fechaOriginal.getFullYear();
      const hora = item.fechaHoraAccion.slice(11, 19);
      const fechaFormateada = `${dia}/${mes}/${anio} ${hora}`;

      if (item.estado == "ESTPEN") {
        if (item.codigoAccion == "SF0002") {
          console.log("ESTADO PENDIENTE:", item.estado);
          console.log("CODIGO ACCION:", item.codigoAccion);
          console.log("ACCION:", item.accion);
          localStorage.setItem("itemCodigoAccion", item.codigoAccion);
          let ca = localStorage.getItem("itemCodigoAccion");
          console.log("ca:", ca);
        } else {
          let ca = localStorage.getItem("itemCodigoAccion");
          console.log("ca:", ca);
          console.log("ESTADO PENDIENTE:", item.estado);
          console.log("CODIGO ACCION:", item.codigoAccion);
          console.log("ACCION:", item.accion);
          localStorage.setItem("itemCodigoAccion", null);
          let ica = localStorage.getItem("itemCodigoAccion");
          console.log("ica:", ica);
        }
      }

      let tipo;
      switch (item.estado) {
        case "ESTAPR": // APROBADO
          tipo = "step active"
          break;
        case "ESTREL": // PROCESADO REALIZADO
          tipo = "step active"
          break;
        case "ESTREC": // RECHAZADO
          tipo = "step danger-step"
          break;
        case "ESTESP": // EN ESPERA
          tipo = "step warning-step"
          break;
        case "ESTPEN": // PENDIENTE
          tipo = "step pendiente"
          break;
      }

      if (item.accion != "Comentario" && (item.estado === "ESTAPR" || item.estado === "ESTREC")) {
        if (item.estado === "ESTAPR") {
          return (
            <div className={tipo} key={item.idAccionGenerada}>
              <div className='step-info'>
                <div className='step-label'>{item.accion}</div>
              </div>
              <div className='step-content'>
                <span className='text-muted'>{item.fechaHoraAccion === "" ? "" : fechaFormateada}</span>
                <p>{item.usuarioDominio}</p>
              </div>
            </div>
          )
        }

        if (item.estado === "ESTREC") {
          return (
            <div className={tipo} key={item.idAccionGenerada}>
              <div className='step-info'>
                <div className='step-label'>{item.accion}</div>
              </div>
              <div className='step-content'>
                <span className='text-muted'>{item.fechaHoraAccion === "" ? "" : fechaFormateada}</span>
                <p>{item.usuarioDominio}</p>
              </div>
            </div>
          )
        }
      } else if (item.accion != "Comentario" && item.estado === "ESTPEN") {
        if (item.estado === "ESTPEN") {
          return (
            <div className={tipo} key={item.idAccionGenerada}>
              <div className='step-info'>
                <div className='step-label'>{item.accion}</div>
              </div>
              <div className='step-content'>
                <span className='text-muted'>{item.fechaHoraAccion === "" ? "" : fechaFormateada}</span>
                <p>{item.usuarioDominio}</p>
              </div>
              {<DatosProvider><VerAutorizantes /></DatosProvider>}
            </div>
          )
        }
      } else if (item.accion != "Comentario" && item.estado === "ESTESP") {
        if (item.estado === "ESTESP") {
          return (
            <div className={tipo} key={item.idAccionGenerada}>
              <div className='step-info'>
                <div className='step-label'>{item.accion}</div>
              </div>
              <div className='step-content'>
                <span className='text-muted'>{item.fechaHoraAccion === "" ? "" : fechaFormateada}</span>
                <p>{item.usuarioDominio}</p>
              </div>
            </div>
          )
        }
      } else if (item.accion != "Comentario" && item.estado !== "ESTINA") {
        return (
          <div className={tipo} key={item.idAccionGenerada}>
            <div className='step-info'>
              <div className='step-label'>{item.accion}</div>
            </div>
            <div className='step-content'>
              <span className='text-muted'>{item.fechaHoraAccion === "" ? "" : fechaFormateada}</span>
              <p>{item.usuarioDominio}</p>
            </div>
          </div>
        )
      }
    })
    return jsx;
  }

  useEffect(() => {
    try {
      getAcciones().then(async result => {
        const sortedData = result as Acciones;
        sortedData.sort((a, b): any => {
          const jerarquia: JerarquiaDeEstado = {
            "ESTAPR": 1,
            "ESTREC": 1,
            "ESTPEN": 2,
            "ESTESP": 3
          };

          if (jerarquia[a.estado] - jerarquia[b.estado] === 0) {
            return a.idAccionGenerada - b.idAccionGenerada;
          }
          return jerarquia[a.estado] - jerarquia[b.estado];
        });
        setJSX(await getJSX(sortedData));
        console.log("sortedData:", sortedData);
      })
    } catch (error) {
      console.log("Error Catch Acciones:", error);
    }
  }, []);

  return (
    <>
      <div className='text-primary text-center my-4 font-weight-bold'>SEGUIMIENTO</div>
      <div className='row d-flex flex-column justify-content-center align-items-center'>
        <div className='col-12 col-md-8'>
          <div className='steps-vertical' id='idRowTracking'>
            {jsx}
          </div>
        </div>
      </div>
    </>
  )
}

export default Tracking;