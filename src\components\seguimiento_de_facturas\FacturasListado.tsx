/// <reference types="webpack/module" />

import React, { useContext } from "react";
import { FacturaGetResponse } from "../../comm/contracts/FacturaGetResponse";
import { fetchFacturasServices } from "../../services/facturaServices";
import FacturaFormContext from "../contextProvider/counterContext";
import { Factura } from "./Factura";
import Tracking from "../tracking/Tracking";
import { FacturaMontos } from "./FacturaMontos";
import { DatosProvider } from "../contextProvider/datosProveedorContext";
import { FacturaEmisor } from "./FacturaEmisor";
import { FacturaReceptor } from "./FacturaReceptor";
import { FacturaHeaderMData } from "./FacturaHeaderMData";
import Comentarios from "../tracking/Comentarios";

'use strict'

export const FacturaListado = (idLote: any) => {

  const fetchFacturas = async () => {
    const _facturas = await fetchFacturasServices(idLote)
    const _jsxFacturas = _facturas.map((f) => renderFacturas(f))
    return _jsxFacturas
  }

  const renderFacturas = (factura: FacturaGetResponse) => {
    return (
      <FacturaFormContext.Provider value={{ counter: 0, factura: factura, onlyRead: true }}>
        <DatosProvider><Factura /></DatosProvider>
      </FacturaFormContext.Provider>
    )
  }

  function renderNavSeguimiento(): any {
    const _FacturaFormContext = useContext(FacturaFormContext);
    const hrefStart = "#";
    const IDsObject = {
      factura: "factura" + _FacturaFormContext.counter,
      collapseFactura: 'facturaCollapse' + _FacturaFormContext.counter,
      collapseArticulo: 'ArticuloCollapse' + _FacturaFormContext.counter,
      btnExpandirPrincipal: 'expandir-principal' + _FacturaFormContext.counter,
    }
    return (
      <div>
        <div className="invoice-header d-flex justify-content-center row">
          <DatosProvider>
            <div className="col-md-4"><FacturaEmisor /></div>
            <div className="col-md-4"><FacturaReceptor /></div>
            <div className="col-md-4"><FacturaHeaderMData /></div>
          </DatosProvider>
        </div>
        <ul className="nav nav-tabs my-4 mx-1" role="tablist" id="myTab">
          <li className="nav-item" role="presentation">
            <a className="nav-link active expandir-principal" id={"factura-m-tab" + IDsObject.btnExpandirPrincipal} data-toggle="tab" data-target="#factura-m-tab-pane" href={hrefStart + IDsObject.collapseFactura} role="tab" aria-controls="factura-montos-tab-pane" aria-selected="true">
              Detalle factura
            </a>
          </li>
          <li className="nav-item" role="presentation">
            <a className="nav-link" id="tracking" data-toggle="tab" data-target="#tracking-tab-pane" href="#tracking-tab" role="tab" aria-controls="seguimiento-tab-pane" aria-selected="false">Seguimiento</a>
          </li>
        </ul>
        <div className="tab-content" id="myTabContent">
          <div id="factura-m-tab-pane" className="tab-pane fade show active" role="tabpanel" aria-labelledby="factura-montos-tab" tabIndex={0}>
            <DatosProvider><FacturaMontos /></DatosProvider>
          </div>
          <div className="tab-pane fade" id="tracking-tab-pane" role="tabpanel" aria-labelledby="seguimiento-tab" tabIndex={0}>
            <Tracking />
          </div>
        </div>
        <div className="my-2">
          <Comentarios />
        </div>
      </div>
    )
  }

  return (
    fetchFacturas(),
    renderNavSeguimiento()
  )
}