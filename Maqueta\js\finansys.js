let username = '';
const previewDiv = document.getElementById('prevcontent');
const defaultDiv = document.getElementById('defaultcont');
// Close preview
const closePreview = () =>{
  document.getElementById('prevcontent').classList.remove('preview--show');
  $('.tbl-item').removeClass('tbl-item--selected');

  // Show default content
  showDefaultView();
}

const getDetailsData = () => {
  // Activar la linea clickeada agregando la clase tbl-item--selected

  // Traer los datos completos de la operacion
  
  // Mostrar el preview
  showDetailsView(data);
}

$('.tbl-item').on('click', function(){
  $(this).toggleClass('tbl-item--selected');
    let div = this;
    username = div.getElementsByClassName('tbl-item__name')[0].innerText;
  // Mostrar el preview
    showDetailsView(username);
});
                                                                                                                                                                
const defaultContent = () => {
  return(
    `
    <div class="text-center">
      <i class="fa fa-file fa-2x mb-3"></i>
      <h5 class="mb-0">Seleccione una Operación</h5>
      <p>Los detalles se visualizan aqui</p>
    </div>
    `
  )
};

const defaultLoader = () => {
  return(
    `
    <div class="text-center">
      <i class="fa fa-sync-alt fa-spin fa-2x mb-3"></i>
      <p>Obteniendo datos...</p>
    </div>
    `
  )
};

const multiSelect = (number) => {
  number = 2;
  return(
    `
    <div class="text-center">
      <i class="fa fa-copy fa-2x mb-3"></i>
      <h5 class="mb-0">${number} Operaciones Seleccionadas</h5>
      <p><a href="#">Seleccionar todas</a> las operaciones</p>
      <div class="text-left px-5">
        <a href="#" class="d-block mb-2"><i class="fa fa-check"></i> Aprobar</a>
        <a href="#" class="d-block mb-2" data-toggle="modal" data-target="#rechazar"><i class="fa fa-times"></i> Rechazar</a>
      </div>
    </div>
  `
  )
};

// Function to display operation detail
const operacion = (username) => {
  // mapear/recibir array
    return(`
    <div class="px-4 py-3 d-flex justify-content-between bg-white sticky-top">
        <div class="d-flex align-items-center">
            <span onclick="closePreview()" class="pw-close">
              <span class="pw-close__bar-1"></span>
              <span class="pw-close__bar-2"></span>
            </span>
            <p class="ml-3 mb-0">Solicitud No. 5750131219</p>
        </div>
        <!-- Preview header actions -->
        <div class="btn-group">
            <a href="#" class="btn btn-outline-fun-blue" data-toggle="modal" data-target="#rechazar">Rechazar</a>
            <a href="#" class="btn btn-fun-blue">Aprobar</a>
        </div>
    </div>
    <div class="prw-cont__detail px-4">
      <div class="row mb-4">
        <div class="col-12 col-md-3">
          <figure class="avatar avatar--lg bg-key-lime">
            <span>SS</span>
          </figure>
          <div class="mt-2">
            <h5 class="mb-0">${username}</h5>
            <p class="mb-0">Clasificacion 1</p>
            <p>Cod.: 410241</p>
          </div>
          <div class="form-group">
            <button onclick="showDocumentsView()" class="btn btn-secondary">Ver documentos</button>
          </div>
        </div>
        <div class="col-12 col-md-9">
          <div class="row">
            <div class="col-6">
              <div class="client-data">
                <b>Documento</b>
                <p>CI - 5.864.546</p>
              </div>
              <div class="client-data">
                <b>Email</b>
                <p><EMAIL> <span class="badge badge-warning">Nuevo</span></p>
              </div>
              <div class="client-data">
                <b>Sucursal</b>
                <p>Coronel Oviedo</p>
              </div>
            </div>
            <div class="col-6">
              <div class="client-data">
                <b>Celular</b>
                <p>0984893626 <span class="badge badge-warning">Nuevo</span></p>
              </div>
              <div class="client-data">
                <b>Estado Civil</b>
                <p>Soltero</p>
              </div>
              <div class="client-data">
                <b>Oficial</b>
                <p>Alejandro Centurion</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <ul class="nav nav-tabs mb-4" id="myTab" role="tablist">
        <li class="nav-item">
          <a class="nav-link active" id="home-tab" data-toggle="tab" href="#home" role="tab" aria-controls="home" aria-selected="true">Detalles</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="profile-tab" data-toggle="tab" href="#profile" role="tab" aria-controls="profile" aria-selected="false">Seguimiento</a>
        </li>
      </ul>
      <div class="tab-content mb-4" id="myTabContent">
        <div class="tab-pane fade show active" id="home" role="tabpanel" aria-labelledby="home-tab">
            
          <!-- Contenido de la operacion -->

            <div class="col-12">
              <div class="row">
                <div class="col-6">
                  <div class="client-data">
                    <b>Monto</b>
                    <p>5.864.546</p>

                    <b>Plazo</b>
                    <p>60 meses</p>

                    <b>Tasa</b>
                    <p>26%</p>
                  </div>
                </div>
                <div class="col-6">
                  <div class="client-data">
                    <b>Cuota</b>
                    <p>540.178</p>
          
                    <b>Destino</b>
                    <p>Vehiculo</p>
                    
                    <b>Gastos</b>
                    <p>550.000</p>
                  </div>
                </div>
              </div>
            </div>
        </div>

        <div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab">
        
          <!-- Seguimiento -->

          <div class="row">
            <div class="col-12 col-md-4 text-center">
              <div class="card py-4">
                <h3 class="mb-0">3 de 8</h3>
                <p class="mb-0">Aprobado</p>
              </div>
            </div> 
            <div class="col-12 col-md-8">
              <div class="steps-vertical">
                <div class="step active">
                  <div class="step-info">
                    <div class="step-label">Análisis de Consumo</div>
                  </div>
                  <div class="step-content">
                    <span class="text-muted">2020-06-22 11:39</span> 
                    <p>Jose Ignacio Barrios Rodriguez</p>
                  </div>
                </div>
                <div class="step warning-step active">
                  <div class="step-info">
                    <div class="step-label">Aprobación de seguimiento</div>
                  </div>
                  <div class="step-content">
                    <span class="text-muted">2020-06-22 11:39</span> 
                    <p>Jose Ignacio Barrios Rodriguez</p>
                  </div>
                </div>
                <div class="step">
                  <div class="step-info">
                    <div class="step-label">Inicio de seguimiento</div>
                  </div>
                  <div class="step-content">
                    <span class="text-muted">2020-06-22 11:39</span> 
                    <p>Jose Ignacio Barrios Rodriguez</p> 
                  </div>
                </div>
              </div>
            </div>
                     
          </div>
        </div>

      </div>
      <div class="comments py-4">
        <h6 class="mb-2">Comentarios</h6>
        <div class="comment">
          <div class="comment__photo">
            <figure class="avatar avatar--md avatar--text bg-fun-blue">
                <span class="text-white">SS</span>
            </figure>
          </div>
          <div class="comment__content">
            <div class="d-flex justify-content-between">
              <h5>Gonzalo Ruiz</h5>
              <p class="text-muted">JUE. FEB. 27, 15:40Hs.</p>
            </div>
            <p class="charge">Oficial de cuentas</p>
            <p>APROBADO POR RIESGOS, NO NOS IMPORTA</p>
          </div>
        </div>

        <div class="comment comment--owner">
          <div class="comment__photo">
            <figure class="avatar avatar--md avatar--text bg-fun-blue">
                <span class="text-white">SS</span>
            </figure>
          </div>
          <div class="comment__content">
            <div class="d-flex justify-content-between">
              <h5>Sebastian Solis</h5>
              <p class="text-muted">JUE. FEB. 27, 15:40Hs.</p>
            </div>
            <p class="charge">Oficial de cuentas</p>
            <p>Vamos a darle al cliente lo que quiere</p>
          </div>
        </div>
      
        <div class="comment-box">
          <label for="comentarios">Comentar</label>
          <textarea id="comentairos" class="form-control" rows="3"></textarea>
          <button class="btn btn-outline-fun-blue mt-2">Guardar</button>
        </div>
      </div>
    `);
}


const documentos = () => {
    return(
        `
      <div class="px-4 py-3 d-flex justify-content-between bg-white sticky-top">
        <div class="d-flex align-items-center">
            <span onclick="closePreview()" class="pw-close">
              <span class="pw-close__bar-1"></span>
              <span class="pw-close__bar-2"></span>
            </span>
            <p class="ml-3 mb-0">Solicitud No. 5750131219</p>
        </div>
        <!-- Preview header actions -->
        <div class="btn-group">
            <a href="#" class="btn btn-outline-fun-blue">Rechazar</a>
            <a href="#" class="btn btn-fun-blue">Aprobar</a>
        </div>
    </div>
      <div class="prw-cont__detail px-4">
        <div class="pt-5 pb-3">
            <h4 class="text-center">Documentos</h4>
            <button onclick="showDetailsView()" class="btn btn-link"><i class="fa fa-chevron-left"></i> Operación</button>
        </div>
    
        <div class="py-2 row">
            <div class="col-12 col-md-4">
                <input class="form-control" type="text" placeholder="Buscar documentos...">
            </div>
            <div class="col-12 col-md-4 offset-md-4 text-right">
                <a href="#" class="btn btn-secondary">Filtros</a>
            </div>
        </div>
    
        <div class="card-documentos border-fun-blue">
            <div class="file-info d-flex align-items-center card-documentos__info">
              <div class="mr-3">
                <input type="checkbox">
              </div>
              <div class="text">
                  <p class="card-documentos__title">Comprobante de Ingresos</p>
                  <p class="mb-0">Tu archivo debe ser PDF, JPG o PNG</p>
              </div>
            </div>
            <div class="text-left card-documentos__text">
                <p class="text-muted mb-0">Carpeta Digital</p>
                <p class="text-muted mb-0">Vence: 10/20/22</p>
                <span class="badge badge-success">Certificado</span>
            </div>
            <div class="file-action card-documentos__action">
                <a href="#" class="btn btn-outline-secondary">Adjuntar</a>
            </div>
        </div>

        <div class="card-documentos border-fun-blue">
            <div class="file-info d-flex align-items-center card-documentos__info">
              <div class="mr-3">
                <input type="checkbox">
              </div>
              <div class="text">
                  <p class="card-documentos__title">Comprobante de Ingresos</p>
                  <p class="mb-0">Tu archivo debe ser PDF, JPG o PNG</p>
              </div>
            </div>
            <div class="text-left card-documentos__text">
                <p class="text-muted mb-0">Carpeta Digital</p>
                <p class="text-muted mb-0">Vence: 10/20/22</p>
                <span class="badge badge-success">Certificado</span>
            </div>
            <div class="file-action card-documentos__action">
                <a href="#" class="btn btn-outline-secondary">Adjuntar</a>
            </div>
        </div>

        <div class="card-documentos border-fun-blue">
            <div class="file-info d-flex align-items-center card-documentos__info">
              <div class="mr-3">
                <input type="checkbox">
              </div>
              <div class="text">
                  <p class="card-documentos__title">Comprobante de Ingresos</p>
                  <p class="mb-0">Tu archivo debe ser PDF, JPG o PNG</p>
              </div>
            </div>
            <div class="text-left card-documentos__text">
                <p class="text-muted mb-0">Carpeta Digital</p>
                <p class="text-muted mb-0">Vence: 10/20/22</p>
                <span class="badge badge-success">Certificado</span>
            </div>
            <div class="file-action card-documentos__action">
                <a href="#" class="btn btn-outline-secondary">Adjuntar</a>
            </div>
        </div>

        <div class="btn-group mb-5">
          <a href="#" class="btn btn-outline-fun-blue disabled">Solicitar</a>
          <a href="#" class="btn btn-outline-fun-blue">Anexar a Carpeta</a>
        </div>
      </div>
    `);
}

const showDetailsView = (username) => {
  defaultDiv.classList.remove('default--show');
  //opendetailview
  previewDiv.classList.add('preview--show');
  previewDiv.innerHTML = operacion(username);
}

const showDocumentsView = () => {
  defaultDiv.classList.remove('default--show');
  //opendetailview
  previewDiv.classList.add('preview--show');
  previewDiv.innerHTML = documentos();
}

const showMultiView = () => {
  defaultDiv.classList.remove('default--show');
  previewDiv.classList.remove('preview--show');
  //opendetailview
  defaultDiv.classList.add('default--show');
  defaultDiv.innerHTML = multiSelect();
}

const showDefaultView = () => {
  defaultDiv.classList.add('default--show');
  defaultDiv.innerHTML = defaultContent();
}

const showLoaderView = () => {
  defaultDiv.classList.add('default--show');
  defaultDiv.innerHTML = defaultLoader();
}