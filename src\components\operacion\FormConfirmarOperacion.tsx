import React, { useState, useTransition } from "react";
import { responderTareaService } from "../../services/operacionesService";
import { textArea } from "../TextArea";
import Loader from "../loader/Loader";
import { DatosProvider, useProveedor } from "../contextProvider/datosProveedorContext";
import SeleccionarArea from "../SeleccionarArea";
import { insertarAutorizantes } from "../insertarAutorizantes";
import { PanelListado } from "../listado/crearDivisiones";
import { ContainerRender } from "../Container";
import ModalValidacionAprobacionBandeja from "../modales/ModalValidacionAprobacionBandeja";
import { Modal } from "../ui/Modal";

export const FormConfirmarOperacion = (props: any) => {
  const { state } = useProveedor();
  const [isPending, startTransition] = useTransition();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [modalValidacionFallida, setModalValidacionFallida] = useState(false);
  const [mensajeValidacion, setMensajeValidacion] = useState("");
  const [showModal, setShowModal] = useState(true);

  const cerrarModalValidacionFallida = () => { setModalValidacionFallida(false); };
  const abrirModalValidacionAutorizante = () => { 
    setMensajeValidacion("Debe agregar al menos un Autorizante de area."); 
    setModalValidacionFallida(true);
  };

  const inputRef = React.useRef(null);
  const jsxMensajeConfirmacion = <div className="alert alert-primary" role="alert">
    Esta Seguro que quiere continuar con su decision? <b>{props.decision}</b>
  </div>

  const responderTarea = async () => {
    const comentarioPrueba = inputRef.current.value;
    console.log("comentarioPrueba:", comentarioPrueba);
    await responderTareaService(props.idTarea, props.decision, 'Tareas Seguimiento Facturas', props.codOperacion, !comentarioPrueba ? "Sin comentarios." : comentarioPrueba)
  }

  const renderizarOperaciones = async () => {
    try {
      ContainerRender({
        component: <DatosProvider><PanelListado /></DatosProvider>
      })
    } catch (error) {
      console.log("Error catch renderizar ListaItem:", error);
    }
  }

  const ejecutarResponderTarea = async () => {
    console.log("ENTRO EN EJECUTAR RESPONDER TAREA.");
    setIsSubmitting(true);
    
    try {
      let codigoAccionItem = localStorage.getItem("itemCodigoAccion");
      let arrayID: any = localStorage.getItem("arrayID");
      let autorizantes = JSON.parse(arrayID);
      
      if (codigoAccionItem === "SF0002") {
        if (JSON.parse(localStorage.getItem("arrayID")).length === 0) {
          abrirModalValidacionAutorizante();
          return;
        } else {
          await insertarAutorizantes(state.numeroOperacionCliente, autorizantes);
        }
        localStorage.setItem("arrayID", null);
        arrayID = [];
        localStorage.setItem("arrayID", JSON.stringify(arrayID));
      }
      
      await responderTarea();
      
      setShowModal(false);
      startTransition(() => {
        renderizarOperaciones();
      });
      
    } catch (error) {
      console.error("Error al ejecutar responder tarea:", error);
    } finally {
      setIsSubmitting(false);
    }
  }

  const handleCloseModal = () => {
    setShowModal(false);
  }

  return (
    <>
      <Modal
        isOpen={showModal}
        onClose={handleCloseModal}
        title="Confirmar Operación"
        size="md"
      >
        <div className="space-y-4">
          {jsxMensajeConfirmacion}
          {textArea('Comentario', 'comentarioConfirmacion', 3, inputRef)}
          
          <div className="d-flex align-items-start">
            <div className="d-flex align-items-center">
              <button 
                 onClick={ejecutarResponderTarea}
                 disabled={isSubmitting || isPending}
                 id="idConfirm" 
                 type="button"  
                 className="btn btn-outline-fun-blue mr-4">
                 {isSubmitting ? "Procesando..." : "Confirmar"}
              </button>
              {(isSubmitting || isPending) && <Loader />}
            </div>
            {localStorage.getItem("itemCodigoAccion") === "SF0002" ? 
              <div>
                <form>
                  <DatosProvider><SeleccionarArea /></DatosProvider>
                </form>
              </div> : ""
            }
          </div>
        </div>
      </Modal>
      
      <ModalValidacionAprobacionBandeja 
        show={modalValidacionFallida} 
        onClose={cerrarModalValidacionFallida} 
        message={mensajeValidacion} 
      />
    </>
  )
}
