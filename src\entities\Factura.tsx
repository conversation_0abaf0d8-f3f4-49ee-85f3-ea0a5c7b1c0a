import { Artic<PERSON> } from "./Articulo"
import { <PERSON><PERSON> } from "./Monto"
import { NumeroFactura } from "./NumeroFactura"
import { Proveedor } from "./Proveedor"

export class Factura {
  idCarga: number
  idLote: number
  numeroFactura: NumeroFactura
  proveedor: Proveedor
  monto: Monto
  provision: boolean
  fechaEmision: Date
  fechaExpiracion: Date
  tipoComprobante: string
  descripcion: string
  condicionVenta: string
  articulos: Articulo[]
}