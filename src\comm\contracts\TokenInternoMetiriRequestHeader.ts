import { Ambiente } from "../../entities/Enums/enumAmbientes";

export class TokenInternoMetiriRequestHeader {
  constructor(_ambiente: number) {
    switch (_ambiente) {
      case Ambiente.dev:
        this['Subscription-key'] = '5c5a22191bbe4abc9d9571803e4d8aac'
        this['Grant-Type'] = 'client_credentials'
        this['Client-Id'] = 'metiri'
        this['Client-Secret'] = 'd1770f20-f79f-4b7e-acf7-ebc3fb4b1743'
        this['Scope'] = 'profile'
        this['Accept'] = 'application/json'

      case Ambiente.qa:
        this['Subscription-key'] = '5c5a22191bbe4abc9d9571803e4d8aac'
        this['Grant-Type'] = 'client_credentials'
        this['Client-Id'] = 'metiri'
        this['Client-Secret'] = 'd1770f20-f79f-4b7e-acf7-ebc3fb4b1743'
        this['Scope'] = 'profile'
        this['Accept'] = 'application/json'

      case Ambiente.prod:
        this['Subscription-key'] = '5c5a22191bbe4abc9d9571803e4d8aac'
        this['Grant-Type'] = 'client_credentials'
        this['Client-Id'] = 'metiri'
        this['Client-Secret'] = 'd1770f20-f79f-4b7e-acf7-ebc3fb4b1743'
        this['Scope'] = 'profile'
        this['Accept'] = 'application/json'
    }
  }
  'Subscription-key': string
  'Grant-Type': string
  'Client-Id': string
  'Client-Secret': string
  'Scope': string
  'Accept': string
}