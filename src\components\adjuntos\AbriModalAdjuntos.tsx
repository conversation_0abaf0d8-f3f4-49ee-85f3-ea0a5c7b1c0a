/// <reference types="webpack/module" />

import React from "react";
import { createRoot } from "react-dom/client";
import { ModalPortal } from "./ModalPortal";

export const AbrilModalAdjuntos = (operacion: any) => {
  const node = document.createElement("div");
  node.id = 'ModalAdjuntos'
  let _root = document.getElementById('container')
  _root.appendChild(node)
  const _modalEl = document.getElementById('ModalAdjuntos')
  const _rootModal = createRoot(_modalEl)
  _rootModal.render(<ModalPortal operacion={operacion} />)
}