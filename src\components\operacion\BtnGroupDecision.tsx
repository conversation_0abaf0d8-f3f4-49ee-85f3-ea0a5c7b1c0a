/// <reference types="webpack/module" />

import React, { useMemo, useState } from "react";
import { obtenerCaminoPorTarea } from "../../comm/apiSharePoint";
import { idUsuariosSitios } from "../../comm/SPsitiosOBJ";
import { IlistadoItemFactura } from "../../entities/Intefaces/IListado";
import { getUrlSPBase } from "../../utilities/contextInfo";
import { BtnDecision } from "./BtnDecision";

export const BtnGroupDecision = (props: any) => {

  const _listadoItemFacturaProp = props.listadoItemFactura as IlistadoItemFactura

  const [btns, setBtns] = useState([<></>])

  const onSuccess = (response: any, idTarea: any) => {
    const _listadoItemFactura: IlistadoItemFactura = _listadoItemFacturaProp
    const _CodOperacion = _listadoItemFactura.titulo.match(/[0-9]*$/)[0];
    console.log("_CodOperacion:", _CodOperacion);
    console.log("idTarea:", idTarea);

    console.log('estoy en BtnGroupDecision')
    const _btnsJSX: JSX.Element[] = []
    const _decisiones: string[] = response['soap:Envelope']['soap:Body']['GetOutcomesForFlexiTaskResponse']['GetOutcomesForFlexiTaskResult']['ConfiguredOutcome']
    _decisiones.forEach((item: any) => {
      _btnsJSX.push(BtnDecision(item['@attributes'].Name, idTarea, _CodOperacion))
    }
    )
    setBtns(exValue => [...exValue, ..._btnsJSX])
  }

  const onFailure = (error: any) => {
    console.log(error)
  }

  const fetchTareas = async () => {
    console.log('llegue a fetch tareas')
    const _listadoItemFactura = props.listadoItemFactura as IlistadoItemFactura
    const _idTarea = _listadoItemFactura.idTarea
    const _lista = 'Tareas Seguimiento Facturas'
    const _urlSitio = getUrlSPBase() + idUsuariosSitios[0].site
    obtenerCaminoPorTarea(_idTarea, onSuccess, onFailure, _lista, _urlSitio)
  }

  useMemo(() => {
    fetchTareas();
  }, [])

  return (
    <div className="btn-group" id="btnGroup">
      {btns}
    </div>
  )
}


