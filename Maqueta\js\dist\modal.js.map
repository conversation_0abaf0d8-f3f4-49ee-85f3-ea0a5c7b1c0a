{"version": 3, "file": "modal.js", "sources": ["../src/modal.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.4.1): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME               = 'modal'\nconst VERSION            = '4.4.1'\nconst DATA_KEY           = 'bs.modal'\nconst EVENT_KEY          = `.${DATA_KEY}`\nconst DATA_API_KEY       = '.data-api'\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\nconst ESCAPE_KEYCODE     = 27 // KeyboardEvent.which value for Escape (Esc) key\n\nconst Default = {\n  backdrop : true,\n  keyboard : true,\n  focus    : true,\n  show     : true\n}\n\nconst DefaultType = {\n  backdrop : '(boolean|string)',\n  keyboard : 'boolean',\n  focus    : 'boolean',\n  show     : 'boolean'\n}\n\nconst Event = {\n  HIDE              : `hide${EVENT_KEY}`,\n  HIDE_PREVENTED    : `hidePrevented${EVENT_KEY}`,\n  HIDDEN            : `hidden${EVENT_KEY}`,\n  SHOW              : `show${EVENT_KEY}`,\n  SHOWN             : `shown${EVENT_KEY}`,\n  FOCUSIN           : `focusin${EVENT_KEY}`,\n  RESIZE            : `resize${EVENT_KEY}`,\n  CLICK_DISMISS     : `click.dismiss${EVENT_KEY}`,\n  KEYDOWN_DISMISS   : `keydown.dismiss${EVENT_KEY}`,\n  MOUSEUP_DISMISS   : `mouseup.dismiss${EVENT_KEY}`,\n  MOUSEDOWN_DISMISS : `mousedown.dismiss${EVENT_KEY}`,\n  CLICK_DATA_API    : `click${EVENT_KEY}${DATA_API_KEY}`\n}\n\nconst ClassName = {\n  SCROLLABLE         : 'modal-dialog-scrollable',\n  SCROLLBAR_MEASURER : 'modal-scrollbar-measure',\n  BACKDROP           : 'modal-backdrop',\n  OPEN               : 'modal-open',\n  FADE               : 'fade',\n  SHOW               : 'show',\n  STATIC             : 'modal-static'\n}\n\nconst Selector = {\n  DIALOG         : '.modal-dialog',\n  MODAL_BODY     : '.modal-body',\n  DATA_TOGGLE    : '[data-toggle=\"modal\"]',\n  DATA_DISMISS   : '[data-dismiss=\"modal\"]',\n  FIXED_CONTENT  : '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top',\n  STICKY_CONTENT : '.sticky-top'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Modal {\n  constructor(element, config) {\n    this._config              = this._getConfig(config)\n    this._element             = element\n    this._dialog              = element.querySelector(Selector.DIALOG)\n    this._backdrop            = null\n    this._isShown             = false\n    this._isBodyOverflowing   = false\n    this._ignoreBackdropClick = false\n    this._isTransitioning     = false\n    this._scrollbarWidth      = 0\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    if ($(this._element).hasClass(ClassName.FADE)) {\n      this._isTransitioning = true\n    }\n\n    const showEvent = $.Event(Event.SHOW, {\n      relatedTarget\n    })\n\n    $(this._element).trigger(showEvent)\n\n    if (this._isShown || showEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._isShown = true\n\n    this._checkScrollbar()\n    this._setScrollbar()\n\n    this._adjustDialog()\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    $(this._element).on(\n      Event.CLICK_DISMISS,\n      Selector.DATA_DISMISS,\n      (event) => this.hide(event)\n    )\n\n    $(this._dialog).on(Event.MOUSEDOWN_DISMISS, () => {\n      $(this._element).one(Event.MOUSEUP_DISMISS, (event) => {\n        if ($(event.target).is(this._element)) {\n          this._ignoreBackdropClick = true\n        }\n      })\n    })\n\n    this._showBackdrop(() => this._showElement(relatedTarget))\n  }\n\n  hide(event) {\n    if (event) {\n      event.preventDefault()\n    }\n\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = $.Event(Event.HIDE)\n\n    $(this._element).trigger(hideEvent)\n\n    if (!this._isShown || hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._isShown = false\n    const transition = $(this._element).hasClass(ClassName.FADE)\n\n    if (transition) {\n      this._isTransitioning = true\n    }\n\n    this._setEscapeEvent()\n    this._setResizeEvent()\n\n    $(document).off(Event.FOCUSIN)\n\n    $(this._element).removeClass(ClassName.SHOW)\n\n    $(this._element).off(Event.CLICK_DISMISS)\n    $(this._dialog).off(Event.MOUSEDOWN_DISMISS)\n\n\n    if (transition) {\n      const transitionDuration  = Util.getTransitionDurationFromElement(this._element)\n\n      $(this._element)\n        .one(Util.TRANSITION_END, (event) => this._hideModal(event))\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      this._hideModal()\n    }\n  }\n\n  dispose() {\n    [window, this._element, this._dialog]\n      .forEach((htmlElement) => $(htmlElement).off(EVENT_KEY))\n\n    /**\n     * `document` has 2 events `Event.FOCUSIN` and `Event.CLICK_DATA_API`\n     * Do not move `document` in `htmlElements` array\n     * It will remove `Event.CLICK_DATA_API` event that should remain\n     */\n    $(document).off(Event.FOCUSIN)\n\n    $.removeData(this._element, DATA_KEY)\n\n    this._config              = null\n    this._element             = null\n    this._dialog              = null\n    this._backdrop            = null\n    this._isShown             = null\n    this._isBodyOverflowing   = null\n    this._ignoreBackdropClick = null\n    this._isTransitioning     = null\n    this._scrollbarWidth      = null\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    Util.typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _triggerBackdropTransition() {\n    if (this._config.backdrop === 'static') {\n      const hideEventPrevented = $.Event(Event.HIDE_PREVENTED)\n\n      $(this._element).trigger(hideEventPrevented)\n      if (hideEventPrevented.defaultPrevented) {\n        return\n      }\n\n      this._element.classList.add(ClassName.STATIC)\n\n      const modalTransitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n      $(this._element).one(Util.TRANSITION_END, () => {\n        this._element.classList.remove(ClassName.STATIC)\n      })\n        .emulateTransitionEnd(modalTransitionDuration)\n      this._element.focus()\n    } else {\n      this.hide()\n    }\n  }\n\n  _showElement(relatedTarget) {\n    const transition = $(this._element).hasClass(ClassName.FADE)\n    const modalBody = this._dialog ? this._dialog.querySelector(Selector.MODAL_BODY) : null\n\n    if (!this._element.parentNode ||\n        this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n      // Don't move modal's DOM position\n      document.body.appendChild(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n\n    if ($(this._dialog).hasClass(ClassName.SCROLLABLE) && modalBody) {\n      modalBody.scrollTop = 0\n    } else {\n      this._element.scrollTop = 0\n    }\n\n    if (transition) {\n      Util.reflow(this._element)\n    }\n\n    $(this._element).addClass(ClassName.SHOW)\n\n    if (this._config.focus) {\n      this._enforceFocus()\n    }\n\n    const shownEvent = $.Event(Event.SHOWN, {\n      relatedTarget\n    })\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._element.focus()\n      }\n      this._isTransitioning = false\n      $(this._element).trigger(shownEvent)\n    }\n\n    if (transition) {\n      const transitionDuration  = Util.getTransitionDurationFromElement(this._dialog)\n\n      $(this._dialog)\n        .one(Util.TRANSITION_END, transitionComplete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      transitionComplete()\n    }\n  }\n\n  _enforceFocus() {\n    $(document)\n      .off(Event.FOCUSIN) // Guard against infinite focus loop\n      .on(Event.FOCUSIN, (event) => {\n        if (document !== event.target &&\n            this._element !== event.target &&\n            $(this._element).has(event.target).length === 0) {\n          this._element.focus()\n        }\n      })\n  }\n\n  _setEscapeEvent() {\n    if (this._isShown && this._config.keyboard) {\n      $(this._element).on(Event.KEYDOWN_DISMISS, (event) => {\n        if (event.which === ESCAPE_KEYCODE) {\n          this._triggerBackdropTransition()\n        }\n      })\n    } else if (!this._isShown) {\n      $(this._element).off(Event.KEYDOWN_DISMISS)\n    }\n  }\n\n  _setResizeEvent() {\n    if (this._isShown) {\n      $(window).on(Event.RESIZE, (event) => this.handleUpdate(event))\n    } else {\n      $(window).off(Event.RESIZE)\n    }\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._isTransitioning = false\n    this._showBackdrop(() => {\n      $(document.body).removeClass(ClassName.OPEN)\n      this._resetAdjustments()\n      this._resetScrollbar()\n      $(this._element).trigger(Event.HIDDEN)\n    })\n  }\n\n  _removeBackdrop() {\n    if (this._backdrop) {\n      $(this._backdrop).remove()\n      this._backdrop = null\n    }\n  }\n\n  _showBackdrop(callback) {\n    const animate = $(this._element).hasClass(ClassName.FADE)\n      ? ClassName.FADE : ''\n\n    if (this._isShown && this._config.backdrop) {\n      this._backdrop = document.createElement('div')\n      this._backdrop.className = ClassName.BACKDROP\n\n      if (animate) {\n        this._backdrop.classList.add(animate)\n      }\n\n      $(this._backdrop).appendTo(document.body)\n\n      $(this._element).on(Event.CLICK_DISMISS, (event) => {\n        if (this._ignoreBackdropClick) {\n          this._ignoreBackdropClick = false\n          return\n        }\n        if (event.target !== event.currentTarget) {\n          return\n        }\n\n        this._triggerBackdropTransition()\n      })\n\n      if (animate) {\n        Util.reflow(this._backdrop)\n      }\n\n      $(this._backdrop).addClass(ClassName.SHOW)\n\n      if (!callback) {\n        return\n      }\n\n      if (!animate) {\n        callback()\n        return\n      }\n\n      const backdropTransitionDuration = Util.getTransitionDurationFromElement(this._backdrop)\n\n      $(this._backdrop)\n        .one(Util.TRANSITION_END, callback)\n        .emulateTransitionEnd(backdropTransitionDuration)\n    } else if (!this._isShown && this._backdrop) {\n      $(this._backdrop).removeClass(ClassName.SHOW)\n\n      const callbackRemove = () => {\n        this._removeBackdrop()\n        if (callback) {\n          callback()\n        }\n      }\n\n      if ($(this._element).hasClass(ClassName.FADE)) {\n        const backdropTransitionDuration = Util.getTransitionDurationFromElement(this._backdrop)\n\n        $(this._backdrop)\n          .one(Util.TRANSITION_END, callbackRemove)\n          .emulateTransitionEnd(backdropTransitionDuration)\n      } else {\n        callbackRemove()\n      }\n    } else if (callback) {\n      callback()\n    }\n  }\n\n  // ----------------------------------------------------------------------\n  // the following methods are used to handle overflowing modals\n  // todo (fat): these should probably be refactored out of modal.js\n  // ----------------------------------------------------------------------\n\n  _adjustDialog() {\n    const isModalOverflowing =\n      this._element.scrollHeight > document.documentElement.clientHeight\n\n    if (!this._isBodyOverflowing && isModalOverflowing) {\n      this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n    }\n\n    if (this._isBodyOverflowing && !isModalOverflowing) {\n      this._element.style.paddingRight = `${this._scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  _checkScrollbar() {\n    const rect = document.body.getBoundingClientRect()\n    this._isBodyOverflowing = rect.left + rect.right < window.innerWidth\n    this._scrollbarWidth = this._getScrollbarWidth()\n  }\n\n  _setScrollbar() {\n    if (this._isBodyOverflowing) {\n      // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n      //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n      const fixedContent = [].slice.call(document.querySelectorAll(Selector.FIXED_CONTENT))\n      const stickyContent = [].slice.call(document.querySelectorAll(Selector.STICKY_CONTENT))\n\n      // Adjust fixed content padding\n      $(fixedContent).each((index, element) => {\n        const actualPadding = element.style.paddingRight\n        const calculatedPadding = $(element).css('padding-right')\n        $(element)\n          .data('padding-right', actualPadding)\n          .css('padding-right', `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`)\n      })\n\n      // Adjust sticky content margin\n      $(stickyContent).each((index, element) => {\n        const actualMargin = element.style.marginRight\n        const calculatedMargin = $(element).css('margin-right')\n        $(element)\n          .data('margin-right', actualMargin)\n          .css('margin-right', `${parseFloat(calculatedMargin) - this._scrollbarWidth}px`)\n      })\n\n      // Adjust body padding\n      const actualPadding = document.body.style.paddingRight\n      const calculatedPadding = $(document.body).css('padding-right')\n      $(document.body)\n        .data('padding-right', actualPadding)\n        .css('padding-right', `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`)\n    }\n\n    $(document.body).addClass(ClassName.OPEN)\n  }\n\n  _resetScrollbar() {\n    // Restore fixed content padding\n    const fixedContent = [].slice.call(document.querySelectorAll(Selector.FIXED_CONTENT))\n    $(fixedContent).each((index, element) => {\n      const padding = $(element).data('padding-right')\n      $(element).removeData('padding-right')\n      element.style.paddingRight = padding ? padding : ''\n    })\n\n    // Restore sticky content\n    const elements = [].slice.call(document.querySelectorAll(`${Selector.STICKY_CONTENT}`))\n    $(elements).each((index, element) => {\n      const margin = $(element).data('margin-right')\n      if (typeof margin !== 'undefined') {\n        $(element).css('margin-right', margin).removeData('margin-right')\n      }\n    })\n\n    // Restore body padding\n    const padding = $(document.body).data('padding-right')\n    $(document.body).removeData('padding-right')\n    document.body.style.paddingRight = padding ? padding : ''\n  }\n\n  _getScrollbarWidth() { // thx d.walsh\n    const scrollDiv = document.createElement('div')\n    scrollDiv.className = ClassName.SCROLLBAR_MEASURER\n    document.body.appendChild(scrollDiv)\n    const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n    document.body.removeChild(scrollDiv)\n    return scrollbarWidth\n  }\n\n  // Static\n\n  static _jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = {\n        ...Default,\n        ...$(this).data(),\n        ...typeof config === 'object' && config ? config : {}\n      }\n\n      if (!data) {\n        data = new Modal(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n        data[config](relatedTarget)\n      } else if (_config.show) {\n        data.show(relatedTarget)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document).on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n  let target\n  const selector = Util.getSelectorFromElement(this)\n\n  if (selector) {\n    target = document.querySelector(selector)\n  }\n\n  const config = $(target).data(DATA_KEY)\n    ? 'toggle' : {\n      ...$(target).data(),\n      ...$(this).data()\n    }\n\n  if (this.tagName === 'A' || this.tagName === 'AREA') {\n    event.preventDefault()\n  }\n\n  const $target = $(target).one(Event.SHOW, (showEvent) => {\n    if (showEvent.isDefaultPrevented()) {\n      // Only register focus restorer if modal will actually get shown\n      return\n    }\n\n    $target.one(Event.HIDDEN, () => {\n      if ($(this).is(':visible')) {\n        this.focus()\n      }\n    })\n  })\n\n  Modal._jQueryInterface.call($(target), config, this)\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Modal._jQueryInterface\n$.fn[NAME].Constructor = Modal\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Modal._jQueryInterface\n}\n\nexport default Modal\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "ESCAPE_KEYCODE", "<PERSON><PERSON><PERSON>", "backdrop", "keyboard", "focus", "show", "DefaultType", "Event", "HIDE", "HIDE_PREVENTED", "HIDDEN", "SHOW", "SHOWN", "FOCUSIN", "RESIZE", "CLICK_DISMISS", "KEYDOWN_DISMISS", "MOUSEUP_DISMISS", "MOUSEDOWN_DISMISS", "CLICK_DATA_API", "ClassName", "SCROLLABLE", "SCROLLBAR_MEASURER", "BACKDROP", "OPEN", "FADE", "STATIC", "Selector", "DIALOG", "MODAL_BODY", "DATA_TOGGLE", "DATA_DISMISS", "FIXED_CONTENT", "STICKY_CONTENT", "Modal", "element", "config", "_config", "_getConfig", "_element", "_dialog", "querySelector", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_isTransitioning", "_scrollbarWidth", "toggle", "relatedTarget", "hide", "hasClass", "showEvent", "trigger", "isDefaultPrevented", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "_setEscapeEvent", "_setResizeEvent", "on", "event", "one", "target", "is", "_showBackdrop", "_showElement", "preventDefault", "hideEvent", "transition", "document", "off", "removeClass", "transitionDuration", "<PERSON><PERSON>", "getTransitionDurationFromElement", "TRANSITION_END", "_hideModal", "emulateTransitionEnd", "dispose", "window", "for<PERSON>ach", "htmlElement", "removeData", "handleUpdate", "typeCheckConfig", "_triggerBackdropTransition", "hideEventPrevented", "defaultPrevented", "classList", "add", "modalTransitionDuration", "remove", "modalBody", "parentNode", "nodeType", "Node", "ELEMENT_NODE", "body", "append<PERSON><PERSON><PERSON>", "style", "display", "removeAttribute", "setAttribute", "scrollTop", "reflow", "addClass", "_enforceFocus", "shownEvent", "transitionComplete", "has", "length", "which", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "callback", "animate", "createElement", "className", "appendTo", "currentTarget", "backdropTransitionDuration", "callback<PERSON><PERSON><PERSON>", "isModalOverflowing", "scrollHeight", "documentElement", "clientHeight", "paddingLeft", "paddingRight", "rect", "getBoundingClientRect", "left", "right", "innerWidth", "_getScrollbarWidth", "fixedContent", "slice", "call", "querySelectorAll", "sticky<PERSON>ontent", "each", "index", "actualPadding", "calculatedPadding", "css", "data", "parseFloat", "<PERSON><PERSON><PERSON><PERSON>", "marginRight", "<PERSON><PERSON><PERSON><PERSON>", "padding", "elements", "margin", "scrollDiv", "scrollbarWidth", "width", "clientWidth", "<PERSON><PERSON><PERSON><PERSON>", "_jQueryInterface", "TypeError", "selector", "getSelectorFromElement", "tagName", "$target", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAUA;;;;;;EAMA,IAAMA,IAAI,GAAiB,OAA3B;EACA,IAAMC,OAAO,GAAc,OAA3B;EACA,IAAMC,QAAQ,GAAa,UAA3B;EACA,IAAMC,SAAS,SAAgBD,QAA/B;EACA,IAAME,YAAY,GAAS,WAA3B;EACA,IAAMC,kBAAkB,GAAGC,CAAC,CAACC,EAAF,CAAKP,IAAL,CAA3B;EACA,IAAMQ,cAAc,GAAO,EAA3B;;EAEA,IAAMC,OAAO,GAAG;EACdC,EAAAA,QAAQ,EAAG,IADG;EAEdC,EAAAA,QAAQ,EAAG,IAFG;EAGdC,EAAAA,KAAK,EAAM,IAHG;EAIdC,EAAAA,IAAI,EAAO;EAJG,CAAhB;EAOA,IAAMC,WAAW,GAAG;EAClBJ,EAAAA,QAAQ,EAAG,kBADO;EAElBC,EAAAA,QAAQ,EAAG,SAFO;EAGlBC,EAAAA,KAAK,EAAM,SAHO;EAIlBC,EAAAA,IAAI,EAAO;EAJO,CAApB;EAOA,IAAME,KAAK,GAAG;EACZC,EAAAA,IAAI,WAAuBb,SADf;EAEZc,EAAAA,cAAc,oBAAsBd,SAFxB;EAGZe,EAAAA,MAAM,aAAuBf,SAHjB;EAIZgB,EAAAA,IAAI,WAAuBhB,SAJf;EAKZiB,EAAAA,KAAK,YAAuBjB,SALhB;EAMZkB,EAAAA,OAAO,cAAuBlB,SANlB;EAOZmB,EAAAA,MAAM,aAAuBnB,SAPjB;EAQZoB,EAAAA,aAAa,oBAAuBpB,SARxB;EASZqB,EAAAA,eAAe,sBAAuBrB,SAT1B;EAUZsB,EAAAA,eAAe,sBAAuBtB,SAV1B;EAWZuB,EAAAA,iBAAiB,wBAAuBvB,SAX5B;EAYZwB,EAAAA,cAAc,YAAcxB,SAAd,GAA0BC;EAZ5B,CAAd;EAeA,IAAMwB,SAAS,GAAG;EAChBC,EAAAA,UAAU,EAAW,yBADL;EAEhBC,EAAAA,kBAAkB,EAAG,yBAFL;EAGhBC,EAAAA,QAAQ,EAAa,gBAHL;EAIhBC,EAAAA,IAAI,EAAiB,YAJL;EAKhBC,EAAAA,IAAI,EAAiB,MALL;EAMhBd,EAAAA,IAAI,EAAiB,MANL;EAOhBe,EAAAA,MAAM,EAAe;EAPL,CAAlB;EAUA,IAAMC,QAAQ,GAAG;EACfC,EAAAA,MAAM,EAAW,eADF;EAEfC,EAAAA,UAAU,EAAO,aAFF;EAGfC,EAAAA,WAAW,EAAM,uBAHF;EAIfC,EAAAA,YAAY,EAAK,wBAJF;EAKfC,EAAAA,aAAa,EAAI,mDALF;EAMfC,EAAAA,cAAc,EAAG;EANF,CAAjB;EASA;;;;;;MAMMC;;;EACJ,iBAAYC,OAAZ,EAAqBC,MAArB,EAA6B;EAC3B,SAAKC,OAAL,GAA4B,KAAKC,UAAL,CAAgBF,MAAhB,CAA5B;EACA,SAAKG,QAAL,GAA4BJ,OAA5B;EACA,SAAKK,OAAL,GAA4BL,OAAO,CAACM,aAAR,CAAsBd,QAAQ,CAACC,MAA/B,CAA5B;EACA,SAAKc,SAAL,GAA4B,IAA5B;EACA,SAAKC,QAAL,GAA4B,KAA5B;EACA,SAAKC,kBAAL,GAA4B,KAA5B;EACA,SAAKC,oBAAL,GAA4B,KAA5B;EACA,SAAKC,gBAAL,GAA4B,KAA5B;EACA,SAAKC,eAAL,GAA4B,CAA5B;EACD;;;;;EAYD;WAEAC,SAAA,gBAAOC,aAAP,EAAsB;EACpB,WAAO,KAAKN,QAAL,GAAgB,KAAKO,IAAL,EAAhB,GAA8B,KAAK7C,IAAL,CAAU4C,aAAV,CAArC;EACD;;WAED5C,OAAA,cAAK4C,aAAL,EAAoB;EAAA;;EAClB,QAAI,KAAKN,QAAL,IAAiB,KAAKG,gBAA1B,EAA4C;EAC1C;EACD;;EAED,QAAIhD,CAAC,CAAC,KAAKyC,QAAN,CAAD,CAAiBY,QAAjB,CAA0B/B,SAAS,CAACK,IAApC,CAAJ,EAA+C;EAC7C,WAAKqB,gBAAL,GAAwB,IAAxB;EACD;;EAED,QAAMM,SAAS,GAAGtD,CAAC,CAACS,KAAF,CAAQA,KAAK,CAACI,IAAd,EAAoB;EACpCsC,MAAAA,aAAa,EAAbA;EADoC,KAApB,CAAlB;EAIAnD,IAAAA,CAAC,CAAC,KAAKyC,QAAN,CAAD,CAAiBc,OAAjB,CAAyBD,SAAzB;;EAEA,QAAI,KAAKT,QAAL,IAAiBS,SAAS,CAACE,kBAAV,EAArB,EAAqD;EACnD;EACD;;EAED,SAAKX,QAAL,GAAgB,IAAhB;;EAEA,SAAKY,eAAL;;EACA,SAAKC,aAAL;;EAEA,SAAKC,aAAL;;EAEA,SAAKC,eAAL;;EACA,SAAKC,eAAL;;EAEA7D,IAAAA,CAAC,CAAC,KAAKyC,QAAN,CAAD,CAAiBqB,EAAjB,CACErD,KAAK,CAACQ,aADR,EAEEY,QAAQ,CAACI,YAFX,EAGE,UAAC8B,KAAD;EAAA,aAAW,KAAI,CAACX,IAAL,CAAUW,KAAV,CAAX;EAAA,KAHF;EAMA/D,IAAAA,CAAC,CAAC,KAAK0C,OAAN,CAAD,CAAgBoB,EAAhB,CAAmBrD,KAAK,CAACW,iBAAzB,EAA4C,YAAM;EAChDpB,MAAAA,CAAC,CAAC,KAAI,CAACyC,QAAN,CAAD,CAAiBuB,GAAjB,CAAqBvD,KAAK,CAACU,eAA3B,EAA4C,UAAC4C,KAAD,EAAW;EACrD,YAAI/D,CAAC,CAAC+D,KAAK,CAACE,MAAP,CAAD,CAAgBC,EAAhB,CAAmB,KAAI,CAACzB,QAAxB,CAAJ,EAAuC;EACrC,UAAA,KAAI,CAACM,oBAAL,GAA4B,IAA5B;EACD;EACF,OAJD;EAKD,KAND;;EAQA,SAAKoB,aAAL,CAAmB;EAAA,aAAM,KAAI,CAACC,YAAL,CAAkBjB,aAAlB,CAAN;EAAA,KAAnB;EACD;;WAEDC,OAAA,cAAKW,KAAL,EAAY;EAAA;;EACV,QAAIA,KAAJ,EAAW;EACTA,MAAAA,KAAK,CAACM,cAAN;EACD;;EAED,QAAI,CAAC,KAAKxB,QAAN,IAAkB,KAAKG,gBAA3B,EAA6C;EAC3C;EACD;;EAED,QAAMsB,SAAS,GAAGtE,CAAC,CAACS,KAAF,CAAQA,KAAK,CAACC,IAAd,CAAlB;EAEAV,IAAAA,CAAC,CAAC,KAAKyC,QAAN,CAAD,CAAiBc,OAAjB,CAAyBe,SAAzB;;EAEA,QAAI,CAAC,KAAKzB,QAAN,IAAkByB,SAAS,CAACd,kBAAV,EAAtB,EAAsD;EACpD;EACD;;EAED,SAAKX,QAAL,GAAgB,KAAhB;EACA,QAAM0B,UAAU,GAAGvE,CAAC,CAAC,KAAKyC,QAAN,CAAD,CAAiBY,QAAjB,CAA0B/B,SAAS,CAACK,IAApC,CAAnB;;EAEA,QAAI4C,UAAJ,EAAgB;EACd,WAAKvB,gBAAL,GAAwB,IAAxB;EACD;;EAED,SAAKY,eAAL;;EACA,SAAKC,eAAL;;EAEA7D,IAAAA,CAAC,CAACwE,QAAD,CAAD,CAAYC,GAAZ,CAAgBhE,KAAK,CAACM,OAAtB;EAEAf,IAAAA,CAAC,CAAC,KAAKyC,QAAN,CAAD,CAAiBiC,WAAjB,CAA6BpD,SAAS,CAACT,IAAvC;EAEAb,IAAAA,CAAC,CAAC,KAAKyC,QAAN,CAAD,CAAiBgC,GAAjB,CAAqBhE,KAAK,CAACQ,aAA3B;EACAjB,IAAAA,CAAC,CAAC,KAAK0C,OAAN,CAAD,CAAgB+B,GAAhB,CAAoBhE,KAAK,CAACW,iBAA1B;;EAGA,QAAImD,UAAJ,EAAgB;EACd,UAAMI,kBAAkB,GAAIC,IAAI,CAACC,gCAAL,CAAsC,KAAKpC,QAA3C,CAA5B;EAEAzC,MAAAA,CAAC,CAAC,KAAKyC,QAAN,CAAD,CACGuB,GADH,CACOY,IAAI,CAACE,cADZ,EAC4B,UAACf,KAAD;EAAA,eAAW,MAAI,CAACgB,UAAL,CAAgBhB,KAAhB,CAAX;EAAA,OAD5B,EAEGiB,oBAFH,CAEwBL,kBAFxB;EAGD,KAND,MAMO;EACL,WAAKI,UAAL;EACD;EACF;;WAEDE,UAAA,mBAAU;EACR,KAACC,MAAD,EAAS,KAAKzC,QAAd,EAAwB,KAAKC,OAA7B,EACGyC,OADH,CACW,UAACC,WAAD;EAAA,aAAiBpF,CAAC,CAACoF,WAAD,CAAD,CAAeX,GAAf,CAAmB5E,SAAnB,CAAjB;EAAA,KADX;EAGA;;;;;;EAKAG,IAAAA,CAAC,CAACwE,QAAD,CAAD,CAAYC,GAAZ,CAAgBhE,KAAK,CAACM,OAAtB;EAEAf,IAAAA,CAAC,CAACqF,UAAF,CAAa,KAAK5C,QAAlB,EAA4B7C,QAA5B;EAEA,SAAK2C,OAAL,GAA4B,IAA5B;EACA,SAAKE,QAAL,GAA4B,IAA5B;EACA,SAAKC,OAAL,GAA4B,IAA5B;EACA,SAAKE,SAAL,GAA4B,IAA5B;EACA,SAAKC,QAAL,GAA4B,IAA5B;EACA,SAAKC,kBAAL,GAA4B,IAA5B;EACA,SAAKC,oBAAL,GAA4B,IAA5B;EACA,SAAKC,gBAAL,GAA4B,IAA5B;EACA,SAAKC,eAAL,GAA4B,IAA5B;EACD;;WAEDqC,eAAA,wBAAe;EACb,SAAK3B,aAAL;EACD;;;WAIDnB,aAAA,oBAAWF,MAAX,EAAmB;EACjBA,IAAAA,MAAM,sBACDnC,OADC,MAEDmC,MAFC,CAAN;EAIAsC,IAAAA,IAAI,CAACW,eAAL,CAAqB7F,IAArB,EAA2B4C,MAA3B,EAAmC9B,WAAnC;EACA,WAAO8B,MAAP;EACD;;WAEDkD,6BAAA,sCAA6B;EAAA;;EAC3B,QAAI,KAAKjD,OAAL,CAAanC,QAAb,KAA0B,QAA9B,EAAwC;EACtC,UAAMqF,kBAAkB,GAAGzF,CAAC,CAACS,KAAF,CAAQA,KAAK,CAACE,cAAd,CAA3B;EAEAX,MAAAA,CAAC,CAAC,KAAKyC,QAAN,CAAD,CAAiBc,OAAjB,CAAyBkC,kBAAzB;;EACA,UAAIA,kBAAkB,CAACC,gBAAvB,EAAyC;EACvC;EACD;;EAED,WAAKjD,QAAL,CAAckD,SAAd,CAAwBC,GAAxB,CAA4BtE,SAAS,CAACM,MAAtC;;EAEA,UAAMiE,uBAAuB,GAAGjB,IAAI,CAACC,gCAAL,CAAsC,KAAKpC,QAA3C,CAAhC;EAEAzC,MAAAA,CAAC,CAAC,KAAKyC,QAAN,CAAD,CAAiBuB,GAAjB,CAAqBY,IAAI,CAACE,cAA1B,EAA0C,YAAM;EAC9C,QAAA,MAAI,CAACrC,QAAL,CAAckD,SAAd,CAAwBG,MAAxB,CAA+BxE,SAAS,CAACM,MAAzC;EACD,OAFD,EAGGoD,oBAHH,CAGwBa,uBAHxB;;EAIA,WAAKpD,QAAL,CAAcnC,KAAd;EACD,KAjBD,MAiBO;EACL,WAAK8C,IAAL;EACD;EACF;;WAEDgB,eAAA,sBAAajB,aAAb,EAA4B;EAAA;;EAC1B,QAAMoB,UAAU,GAAGvE,CAAC,CAAC,KAAKyC,QAAN,CAAD,CAAiBY,QAAjB,CAA0B/B,SAAS,CAACK,IAApC,CAAnB;EACA,QAAMoE,SAAS,GAAG,KAAKrD,OAAL,GAAe,KAAKA,OAAL,CAAaC,aAAb,CAA2Bd,QAAQ,CAACE,UAApC,CAAf,GAAiE,IAAnF;;EAEA,QAAI,CAAC,KAAKU,QAAL,CAAcuD,UAAf,IACA,KAAKvD,QAAL,CAAcuD,UAAd,CAAyBC,QAAzB,KAAsCC,IAAI,CAACC,YAD/C,EAC6D;EAC3D;EACA3B,MAAAA,QAAQ,CAAC4B,IAAT,CAAcC,WAAd,CAA0B,KAAK5D,QAA/B;EACD;;EAED,SAAKA,QAAL,CAAc6D,KAAd,CAAoBC,OAApB,GAA8B,OAA9B;;EACA,SAAK9D,QAAL,CAAc+D,eAAd,CAA8B,aAA9B;;EACA,SAAK/D,QAAL,CAAcgE,YAAd,CAA2B,YAA3B,EAAyC,IAAzC;;EAEA,QAAIzG,CAAC,CAAC,KAAK0C,OAAN,CAAD,CAAgBW,QAAhB,CAAyB/B,SAAS,CAACC,UAAnC,KAAkDwE,SAAtD,EAAiE;EAC/DA,MAAAA,SAAS,CAACW,SAAV,GAAsB,CAAtB;EACD,KAFD,MAEO;EACL,WAAKjE,QAAL,CAAciE,SAAd,GAA0B,CAA1B;EACD;;EAED,QAAInC,UAAJ,EAAgB;EACdK,MAAAA,IAAI,CAAC+B,MAAL,CAAY,KAAKlE,QAAjB;EACD;;EAEDzC,IAAAA,CAAC,CAAC,KAAKyC,QAAN,CAAD,CAAiBmE,QAAjB,CAA0BtF,SAAS,CAACT,IAApC;;EAEA,QAAI,KAAK0B,OAAL,CAAajC,KAAjB,EAAwB;EACtB,WAAKuG,aAAL;EACD;;EAED,QAAMC,UAAU,GAAG9G,CAAC,CAACS,KAAF,CAAQA,KAAK,CAACK,KAAd,EAAqB;EACtCqC,MAAAA,aAAa,EAAbA;EADsC,KAArB,CAAnB;;EAIA,QAAM4D,kBAAkB,GAAG,SAArBA,kBAAqB,GAAM;EAC/B,UAAI,MAAI,CAACxE,OAAL,CAAajC,KAAjB,EAAwB;EACtB,QAAA,MAAI,CAACmC,QAAL,CAAcnC,KAAd;EACD;;EACD,MAAA,MAAI,CAAC0C,gBAAL,GAAwB,KAAxB;EACAhD,MAAAA,CAAC,CAAC,MAAI,CAACyC,QAAN,CAAD,CAAiBc,OAAjB,CAAyBuD,UAAzB;EACD,KAND;;EAQA,QAAIvC,UAAJ,EAAgB;EACd,UAAMI,kBAAkB,GAAIC,IAAI,CAACC,gCAAL,CAAsC,KAAKnC,OAA3C,CAA5B;EAEA1C,MAAAA,CAAC,CAAC,KAAK0C,OAAN,CAAD,CACGsB,GADH,CACOY,IAAI,CAACE,cADZ,EAC4BiC,kBAD5B,EAEG/B,oBAFH,CAEwBL,kBAFxB;EAGD,KAND,MAMO;EACLoC,MAAAA,kBAAkB;EACnB;EACF;;WAEDF,gBAAA,yBAAgB;EAAA;;EACd7G,IAAAA,CAAC,CAACwE,QAAD,CAAD,CACGC,GADH,CACOhE,KAAK,CAACM,OADb;EAAA,KAEG+C,EAFH,CAEMrD,KAAK,CAACM,OAFZ,EAEqB,UAACgD,KAAD,EAAW;EAC5B,UAAIS,QAAQ,KAAKT,KAAK,CAACE,MAAnB,IACA,MAAI,CAACxB,QAAL,KAAkBsB,KAAK,CAACE,MADxB,IAEAjE,CAAC,CAAC,MAAI,CAACyC,QAAN,CAAD,CAAiBuE,GAAjB,CAAqBjD,KAAK,CAACE,MAA3B,EAAmCgD,MAAnC,KAA8C,CAFlD,EAEqD;EACnD,QAAA,MAAI,CAACxE,QAAL,CAAcnC,KAAd;EACD;EACF,KARH;EASD;;WAEDsD,kBAAA,2BAAkB;EAAA;;EAChB,QAAI,KAAKf,QAAL,IAAiB,KAAKN,OAAL,CAAalC,QAAlC,EAA4C;EAC1CL,MAAAA,CAAC,CAAC,KAAKyC,QAAN,CAAD,CAAiBqB,EAAjB,CAAoBrD,KAAK,CAACS,eAA1B,EAA2C,UAAC6C,KAAD,EAAW;EACpD,YAAIA,KAAK,CAACmD,KAAN,KAAgBhH,cAApB,EAAoC;EAClC,UAAA,MAAI,CAACsF,0BAAL;EACD;EACF,OAJD;EAKD,KAND,MAMO,IAAI,CAAC,KAAK3C,QAAV,EAAoB;EACzB7C,MAAAA,CAAC,CAAC,KAAKyC,QAAN,CAAD,CAAiBgC,GAAjB,CAAqBhE,KAAK,CAACS,eAA3B;EACD;EACF;;WAED2C,kBAAA,2BAAkB;EAAA;;EAChB,QAAI,KAAKhB,QAAT,EAAmB;EACjB7C,MAAAA,CAAC,CAACkF,MAAD,CAAD,CAAUpB,EAAV,CAAarD,KAAK,CAACO,MAAnB,EAA2B,UAAC+C,KAAD;EAAA,eAAW,MAAI,CAACuB,YAAL,CAAkBvB,KAAlB,CAAX;EAAA,OAA3B;EACD,KAFD,MAEO;EACL/D,MAAAA,CAAC,CAACkF,MAAD,CAAD,CAAUT,GAAV,CAAchE,KAAK,CAACO,MAApB;EACD;EACF;;WAED+D,aAAA,sBAAa;EAAA;;EACX,SAAKtC,QAAL,CAAc6D,KAAd,CAAoBC,OAApB,GAA8B,MAA9B;;EACA,SAAK9D,QAAL,CAAcgE,YAAd,CAA2B,aAA3B,EAA0C,IAA1C;;EACA,SAAKhE,QAAL,CAAc+D,eAAd,CAA8B,YAA9B;;EACA,SAAKxD,gBAAL,GAAwB,KAAxB;;EACA,SAAKmB,aAAL,CAAmB,YAAM;EACvBnE,MAAAA,CAAC,CAACwE,QAAQ,CAAC4B,IAAV,CAAD,CAAiB1B,WAAjB,CAA6BpD,SAAS,CAACI,IAAvC;;EACA,MAAA,MAAI,CAACyF,iBAAL;;EACA,MAAA,MAAI,CAACC,eAAL;;EACApH,MAAAA,CAAC,CAAC,MAAI,CAACyC,QAAN,CAAD,CAAiBc,OAAjB,CAAyB9C,KAAK,CAACG,MAA/B;EACD,KALD;EAMD;;WAEDyG,kBAAA,2BAAkB;EAChB,QAAI,KAAKzE,SAAT,EAAoB;EAClB5C,MAAAA,CAAC,CAAC,KAAK4C,SAAN,CAAD,CAAkBkD,MAAlB;EACA,WAAKlD,SAAL,GAAiB,IAAjB;EACD;EACF;;WAEDuB,gBAAA,uBAAcmD,QAAd,EAAwB;EAAA;;EACtB,QAAMC,OAAO,GAAGvH,CAAC,CAAC,KAAKyC,QAAN,CAAD,CAAiBY,QAAjB,CAA0B/B,SAAS,CAACK,IAApC,IACZL,SAAS,CAACK,IADE,GACK,EADrB;;EAGA,QAAI,KAAKkB,QAAL,IAAiB,KAAKN,OAAL,CAAanC,QAAlC,EAA4C;EAC1C,WAAKwC,SAAL,GAAiB4B,QAAQ,CAACgD,aAAT,CAAuB,KAAvB,CAAjB;EACA,WAAK5E,SAAL,CAAe6E,SAAf,GAA2BnG,SAAS,CAACG,QAArC;;EAEA,UAAI8F,OAAJ,EAAa;EACX,aAAK3E,SAAL,CAAe+C,SAAf,CAAyBC,GAAzB,CAA6B2B,OAA7B;EACD;;EAEDvH,MAAAA,CAAC,CAAC,KAAK4C,SAAN,CAAD,CAAkB8E,QAAlB,CAA2BlD,QAAQ,CAAC4B,IAApC;EAEApG,MAAAA,CAAC,CAAC,KAAKyC,QAAN,CAAD,CAAiBqB,EAAjB,CAAoBrD,KAAK,CAACQ,aAA1B,EAAyC,UAAC8C,KAAD,EAAW;EAClD,YAAI,MAAI,CAAChB,oBAAT,EAA+B;EAC7B,UAAA,MAAI,CAACA,oBAAL,GAA4B,KAA5B;EACA;EACD;;EACD,YAAIgB,KAAK,CAACE,MAAN,KAAiBF,KAAK,CAAC4D,aAA3B,EAA0C;EACxC;EACD;;EAED,QAAA,MAAI,CAACnC,0BAAL;EACD,OAVD;;EAYA,UAAI+B,OAAJ,EAAa;EACX3C,QAAAA,IAAI,CAAC+B,MAAL,CAAY,KAAK/D,SAAjB;EACD;;EAED5C,MAAAA,CAAC,CAAC,KAAK4C,SAAN,CAAD,CAAkBgE,QAAlB,CAA2BtF,SAAS,CAACT,IAArC;;EAEA,UAAI,CAACyG,QAAL,EAAe;EACb;EACD;;EAED,UAAI,CAACC,OAAL,EAAc;EACZD,QAAAA,QAAQ;EACR;EACD;;EAED,UAAMM,0BAA0B,GAAGhD,IAAI,CAACC,gCAAL,CAAsC,KAAKjC,SAA3C,CAAnC;EAEA5C,MAAAA,CAAC,CAAC,KAAK4C,SAAN,CAAD,CACGoB,GADH,CACOY,IAAI,CAACE,cADZ,EAC4BwC,QAD5B,EAEGtC,oBAFH,CAEwB4C,0BAFxB;EAGD,KA1CD,MA0CO,IAAI,CAAC,KAAK/E,QAAN,IAAkB,KAAKD,SAA3B,EAAsC;EAC3C5C,MAAAA,CAAC,CAAC,KAAK4C,SAAN,CAAD,CAAkB8B,WAAlB,CAA8BpD,SAAS,CAACT,IAAxC;;EAEA,UAAMgH,cAAc,GAAG,SAAjBA,cAAiB,GAAM;EAC3B,QAAA,MAAI,CAACR,eAAL;;EACA,YAAIC,QAAJ,EAAc;EACZA,UAAAA,QAAQ;EACT;EACF,OALD;;EAOA,UAAItH,CAAC,CAAC,KAAKyC,QAAN,CAAD,CAAiBY,QAAjB,CAA0B/B,SAAS,CAACK,IAApC,CAAJ,EAA+C;EAC7C,YAAMiG,2BAA0B,GAAGhD,IAAI,CAACC,gCAAL,CAAsC,KAAKjC,SAA3C,CAAnC;;EAEA5C,QAAAA,CAAC,CAAC,KAAK4C,SAAN,CAAD,CACGoB,GADH,CACOY,IAAI,CAACE,cADZ,EAC4B+C,cAD5B,EAEG7C,oBAFH,CAEwB4C,2BAFxB;EAGD,OAND,MAMO;EACLC,QAAAA,cAAc;EACf;EACF,KAnBM,MAmBA,IAAIP,QAAJ,EAAc;EACnBA,MAAAA,QAAQ;EACT;EACF;EAGD;EACA;EACA;;;WAEA3D,gBAAA,yBAAgB;EACd,QAAMmE,kBAAkB,GACtB,KAAKrF,QAAL,CAAcsF,YAAd,GAA6BvD,QAAQ,CAACwD,eAAT,CAAyBC,YADxD;;EAGA,QAAI,CAAC,KAAKnF,kBAAN,IAA4BgF,kBAAhC,EAAoD;EAClD,WAAKrF,QAAL,CAAc6D,KAAd,CAAoB4B,WAApB,GAAqC,KAAKjF,eAA1C;EACD;;EAED,QAAI,KAAKH,kBAAL,IAA2B,CAACgF,kBAAhC,EAAoD;EAClD,WAAKrF,QAAL,CAAc6D,KAAd,CAAoB6B,YAApB,GAAsC,KAAKlF,eAA3C;EACD;EACF;;WAEDkE,oBAAA,6BAAoB;EAClB,SAAK1E,QAAL,CAAc6D,KAAd,CAAoB4B,WAApB,GAAkC,EAAlC;EACA,SAAKzF,QAAL,CAAc6D,KAAd,CAAoB6B,YAApB,GAAmC,EAAnC;EACD;;WAED1E,kBAAA,2BAAkB;EAChB,QAAM2E,IAAI,GAAG5D,QAAQ,CAAC4B,IAAT,CAAciC,qBAAd,EAAb;EACA,SAAKvF,kBAAL,GAA0BsF,IAAI,CAACE,IAAL,GAAYF,IAAI,CAACG,KAAjB,GAAyBrD,MAAM,CAACsD,UAA1D;EACA,SAAKvF,eAAL,GAAuB,KAAKwF,kBAAL,EAAvB;EACD;;WAED/E,gBAAA,yBAAgB;EAAA;;EACd,QAAI,KAAKZ,kBAAT,EAA6B;EAC3B;EACA;EACA,UAAM4F,YAAY,GAAG,GAAGC,KAAH,CAASC,IAAT,CAAcpE,QAAQ,CAACqE,gBAAT,CAA0BhH,QAAQ,CAACK,aAAnC,CAAd,CAArB;EACA,UAAM4G,aAAa,GAAG,GAAGH,KAAH,CAASC,IAAT,CAAcpE,QAAQ,CAACqE,gBAAT,CAA0BhH,QAAQ,CAACM,cAAnC,CAAd,CAAtB,CAJ2B;;EAO3BnC,MAAAA,CAAC,CAAC0I,YAAD,CAAD,CAAgBK,IAAhB,CAAqB,UAACC,KAAD,EAAQ3G,OAAR,EAAoB;EACvC,YAAM4G,aAAa,GAAG5G,OAAO,CAACiE,KAAR,CAAc6B,YAApC;EACA,YAAMe,iBAAiB,GAAGlJ,CAAC,CAACqC,OAAD,CAAD,CAAW8G,GAAX,CAAe,eAAf,CAA1B;EACAnJ,QAAAA,CAAC,CAACqC,OAAD,CAAD,CACG+G,IADH,CACQ,eADR,EACyBH,aADzB,EAEGE,GAFH,CAEO,eAFP,EAE2BE,UAAU,CAACH,iBAAD,CAAV,GAAgC,OAAI,CAACjG,eAFhE;EAGD,OAND,EAP2B;;EAgB3BjD,MAAAA,CAAC,CAAC8I,aAAD,CAAD,CAAiBC,IAAjB,CAAsB,UAACC,KAAD,EAAQ3G,OAAR,EAAoB;EACxC,YAAMiH,YAAY,GAAGjH,OAAO,CAACiE,KAAR,CAAciD,WAAnC;EACA,YAAMC,gBAAgB,GAAGxJ,CAAC,CAACqC,OAAD,CAAD,CAAW8G,GAAX,CAAe,cAAf,CAAzB;EACAnJ,QAAAA,CAAC,CAACqC,OAAD,CAAD,CACG+G,IADH,CACQ,cADR,EACwBE,YADxB,EAEGH,GAFH,CAEO,cAFP,EAE0BE,UAAU,CAACG,gBAAD,CAAV,GAA+B,OAAI,CAACvG,eAF9D;EAGD,OAND,EAhB2B;;EAyB3B,UAAMgG,aAAa,GAAGzE,QAAQ,CAAC4B,IAAT,CAAcE,KAAd,CAAoB6B,YAA1C;EACA,UAAMe,iBAAiB,GAAGlJ,CAAC,CAACwE,QAAQ,CAAC4B,IAAV,CAAD,CAAiB+C,GAAjB,CAAqB,eAArB,CAA1B;EACAnJ,MAAAA,CAAC,CAACwE,QAAQ,CAAC4B,IAAV,CAAD,CACGgD,IADH,CACQ,eADR,EACyBH,aADzB,EAEGE,GAFH,CAEO,eAFP,EAE2BE,UAAU,CAACH,iBAAD,CAAV,GAAgC,KAAKjG,eAFhE;EAGD;;EAEDjD,IAAAA,CAAC,CAACwE,QAAQ,CAAC4B,IAAV,CAAD,CAAiBQ,QAAjB,CAA0BtF,SAAS,CAACI,IAApC;EACD;;WAED0F,kBAAA,2BAAkB;EAChB;EACA,QAAMsB,YAAY,GAAG,GAAGC,KAAH,CAASC,IAAT,CAAcpE,QAAQ,CAACqE,gBAAT,CAA0BhH,QAAQ,CAACK,aAAnC,CAAd,CAArB;EACAlC,IAAAA,CAAC,CAAC0I,YAAD,CAAD,CAAgBK,IAAhB,CAAqB,UAACC,KAAD,EAAQ3G,OAAR,EAAoB;EACvC,UAAMoH,OAAO,GAAGzJ,CAAC,CAACqC,OAAD,CAAD,CAAW+G,IAAX,CAAgB,eAAhB,CAAhB;EACApJ,MAAAA,CAAC,CAACqC,OAAD,CAAD,CAAWgD,UAAX,CAAsB,eAAtB;EACAhD,MAAAA,OAAO,CAACiE,KAAR,CAAc6B,YAAd,GAA6BsB,OAAO,GAAGA,OAAH,GAAa,EAAjD;EACD,KAJD,EAHgB;;EAUhB,QAAMC,QAAQ,GAAG,GAAGf,KAAH,CAASC,IAAT,CAAcpE,QAAQ,CAACqE,gBAAT,MAA6BhH,QAAQ,CAACM,cAAtC,CAAd,CAAjB;EACAnC,IAAAA,CAAC,CAAC0J,QAAD,CAAD,CAAYX,IAAZ,CAAiB,UAACC,KAAD,EAAQ3G,OAAR,EAAoB;EACnC,UAAMsH,MAAM,GAAG3J,CAAC,CAACqC,OAAD,CAAD,CAAW+G,IAAX,CAAgB,cAAhB,CAAf;;EACA,UAAI,OAAOO,MAAP,KAAkB,WAAtB,EAAmC;EACjC3J,QAAAA,CAAC,CAACqC,OAAD,CAAD,CAAW8G,GAAX,CAAe,cAAf,EAA+BQ,MAA/B,EAAuCtE,UAAvC,CAAkD,cAAlD;EACD;EACF,KALD,EAXgB;;EAmBhB,QAAMoE,OAAO,GAAGzJ,CAAC,CAACwE,QAAQ,CAAC4B,IAAV,CAAD,CAAiBgD,IAAjB,CAAsB,eAAtB,CAAhB;EACApJ,IAAAA,CAAC,CAACwE,QAAQ,CAAC4B,IAAV,CAAD,CAAiBf,UAAjB,CAA4B,eAA5B;EACAb,IAAAA,QAAQ,CAAC4B,IAAT,CAAcE,KAAd,CAAoB6B,YAApB,GAAmCsB,OAAO,GAAGA,OAAH,GAAa,EAAvD;EACD;;WAEDhB,qBAAA,8BAAqB;EAAE;EACrB,QAAMmB,SAAS,GAAGpF,QAAQ,CAACgD,aAAT,CAAuB,KAAvB,CAAlB;EACAoC,IAAAA,SAAS,CAACnC,SAAV,GAAsBnG,SAAS,CAACE,kBAAhC;EACAgD,IAAAA,QAAQ,CAAC4B,IAAT,CAAcC,WAAd,CAA0BuD,SAA1B;EACA,QAAMC,cAAc,GAAGD,SAAS,CAACvB,qBAAV,GAAkCyB,KAAlC,GAA0CF,SAAS,CAACG,WAA3E;EACAvF,IAAAA,QAAQ,CAAC4B,IAAT,CAAc4D,WAAd,CAA0BJ,SAA1B;EACA,WAAOC,cAAP;EACD;;;UAIMI,mBAAP,0BAAwB3H,MAAxB,EAAgCa,aAAhC,EAA+C;EAC7C,WAAO,KAAK4F,IAAL,CAAU,YAAY;EAC3B,UAAIK,IAAI,GAAGpJ,CAAC,CAAC,IAAD,CAAD,CAAQoJ,IAAR,CAAaxJ,QAAb,CAAX;;EACA,UAAM2C,OAAO,sBACRpC,OADQ,MAERH,CAAC,CAAC,IAAD,CAAD,CAAQoJ,IAAR,EAFQ,MAGR,OAAO9G,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAHxC,CAAb;;EAMA,UAAI,CAAC8G,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIhH,KAAJ,CAAU,IAAV,EAAgBG,OAAhB,CAAP;EACAvC,QAAAA,CAAC,CAAC,IAAD,CAAD,CAAQoJ,IAAR,CAAaxJ,QAAb,EAAuBwJ,IAAvB;EACD;;EAED,UAAI,OAAO9G,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAO8G,IAAI,CAAC9G,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAI4H,SAAJ,wBAAkC5H,MAAlC,QAAN;EACD;;EACD8G,QAAAA,IAAI,CAAC9G,MAAD,CAAJ,CAAaa,aAAb;EACD,OALD,MAKO,IAAIZ,OAAO,CAAChC,IAAZ,EAAkB;EACvB6I,QAAAA,IAAI,CAAC7I,IAAL,CAAU4C,aAAV;EACD;EACF,KArBM,CAAP;EAsBD;;;;0BAldoB;EACnB,aAAOxD,OAAP;EACD;;;0BAEoB;EACnB,aAAOQ,OAAP;EACD;;;;;EA+cH;;;;;;;EAMAH,CAAC,CAACwE,QAAD,CAAD,CAAYV,EAAZ,CAAerD,KAAK,CAACY,cAArB,EAAqCQ,QAAQ,CAACG,WAA9C,EAA2D,UAAU+B,KAAV,EAAiB;EAAA;;EAC1E,MAAIE,MAAJ;EACA,MAAMkG,QAAQ,GAAGvF,IAAI,CAACwF,sBAAL,CAA4B,IAA5B,CAAjB;;EAEA,MAAID,QAAJ,EAAc;EACZlG,IAAAA,MAAM,GAAGO,QAAQ,CAAC7B,aAAT,CAAuBwH,QAAvB,CAAT;EACD;;EAED,MAAM7H,MAAM,GAAGtC,CAAC,CAACiE,MAAD,CAAD,CAAUmF,IAAV,CAAexJ,QAAf,IACX,QADW,sBAERI,CAAC,CAACiE,MAAD,CAAD,CAAUmF,IAAV,EAFQ,MAGRpJ,CAAC,CAAC,IAAD,CAAD,CAAQoJ,IAAR,EAHQ,CAAf;;EAMA,MAAI,KAAKiB,OAAL,KAAiB,GAAjB,IAAwB,KAAKA,OAAL,KAAiB,MAA7C,EAAqD;EACnDtG,IAAAA,KAAK,CAACM,cAAN;EACD;;EAED,MAAMiG,OAAO,GAAGtK,CAAC,CAACiE,MAAD,CAAD,CAAUD,GAAV,CAAcvD,KAAK,CAACI,IAApB,EAA0B,UAACyC,SAAD,EAAe;EACvD,QAAIA,SAAS,CAACE,kBAAV,EAAJ,EAAoC;EAClC;EACA;EACD;;EAED8G,IAAAA,OAAO,CAACtG,GAAR,CAAYvD,KAAK,CAACG,MAAlB,EAA0B,YAAM;EAC9B,UAAIZ,CAAC,CAAC,OAAD,CAAD,CAAQkE,EAAR,CAAW,UAAX,CAAJ,EAA4B;EAC1B,QAAA,OAAI,CAAC5D,KAAL;EACD;EACF,KAJD;EAKD,GAXe,CAAhB;;EAaA8B,EAAAA,KAAK,CAAC6H,gBAAN,CAAuBrB,IAAvB,CAA4B5I,CAAC,CAACiE,MAAD,CAA7B,EAAuC3B,MAAvC,EAA+C,IAA/C;EACD,CAhCD;EAkCA;;;;;;EAMAtC,CAAC,CAACC,EAAF,CAAKP,IAAL,IAAa0C,KAAK,CAAC6H,gBAAnB;EACAjK,CAAC,CAACC,EAAF,CAAKP,IAAL,EAAW6K,WAAX,GAAyBnI,KAAzB;;EACApC,CAAC,CAACC,EAAF,CAAKP,IAAL,EAAW8K,UAAX,GAAwB,YAAM;EAC5BxK,EAAAA,CAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb;EACA,SAAOqC,KAAK,CAAC6H,gBAAb;EACD,CAHD;;;;;;;;"}