import { FacturaInsert } from "../comm/contracts/FacturaInsert"
import { MontoInsert } from "../entities/MontoInsert"
import { NumeroFactura } from "../entities/NumeroFactura"
import { Articulo } from "../entities/Articulo"
import { obtenerProovedor } from "./usuarioServices"
import { calcularGravadasArticulosDom } from "../entities/Gravadas"
import { getFacturas, postFactura, postMigracionProveedorFacturas } from "../comm/apiProveedorFactura"
import { isErrorResponse } from "../comm/contracts/ErrorResponse"
import { isFacturaGetResponseArr } from "../comm/contracts/FacturaGetResponse"
import { Estados } from "../entities/Enums/estadosRespuesta"

export const datosFacturasObtenerDOM = async () => {
  console.log("entró en datosFacturasDOM")

  try {
    let cantidadFacturas = document.getElementsByClassName('invoice').length
    console.log("cantidad de facturas: ", cantidadFacturas)
    let _facturaDataInsertArr: FacturaInsert[]
    _facturaDataInsertArr = []
    console.log("_facturaDataInsertArr: ", _facturaDataInsertArr)

    const _usuarioDeContexto = localStorage.getItem('usuarioTresLetras')
    console.log("_usuarioContexto: ", _usuarioDeContexto)
    let _tipoComprabante = (document.getElementById('tipoComprobante') as HTMLInputElement);
    let _tipoComprabanteValue = _tipoComprabante?.value;
    console.log("_tipoComprabanteValue:", _tipoComprabanteValue);

    for (let index = 0; index < cantidadFacturas; index++) {
      console.log("bucle for Facturas")
      let _unProveedorId = (document.getElementById('buscadorProveedor') as HTMLInputElement)
      let _unProveedorIdValue = _unProveedorId?.value;
      console.log("nro Doc: ", _unProveedorIdValue)
      let _unTipoDocumento = (document.getElementById('tipoDocumento') as HTMLInputElement)
      let _unTipoDocumentoValue = _unTipoDocumento?.value;
      console.log("tipoDoc: ", _unTipoDocumentoValue)

      let unProveedor = await obtenerProovedor(_unProveedorIdValue, _unTipoDocumentoValue)
      let _unaFactura: FacturaInsert
      let tipoPago: any = (document.getElementById('provisionCheck' + index) as HTMLInputElement).checked ? "PR" : "PP";
      _unaFactura = {
        usuarioCarga: _usuarioDeContexto,
        monto: getMontoDom(index),
        idEmisor: unProveedor.id,
        numeroFactura: getNumeroFacturaDOM(index),
        provision: (document.getElementById('provisionCheck' + index) as HTMLInputElement).checked,
        condicionVenta: (document.getElementById('condicionState' + index) as HTMLInputElement).value,
        fechaEmision: (document.getElementById('datePicker' + index + 'emision') as HTMLInputElement).value,
        descripcion: (document.getElementById('descripcion' + index) as HTMLInputElement).value,
        tipoComprobante: _tipoComprabanteValue,
        timbrado: (document.getElementById('timbrado-input' + index + 'field') as HTMLSelectElement).value,
        articulos: [],
        marcaTipoPago: tipoPago
      }
      console.log("tipoPago:", tipoPago);
      console.log("_unaFactura:", _unaFactura);
      _facturaDataInsertArr.push(_unaFactura)
    }
    console.log("_facturaDataInsertArr: ", _facturaDataInsertArr)
    return _facturaDataInsertArr

  } catch (error) {
    console.log(error)
  }

}

export const insertFactura = async (body: FacturaInsert[]) => {
  console.log("Antes de invocar al método, body:", body);
  let _facturaResponse = await postFactura(body);
  console.log("_facturaResponse insertFactura:", _facturaResponse);
  if (_facturaResponse.status === Estados.estadoDeSolicitudProcesada) {
    return _facturaResponse
  } else if (isErrorResponse(_facturaResponse)) {
    return _facturaResponse
  }

}

export const fetchFacturasServices = async (idLote?: number, numeroFactura?: string) => {
  const _facturasResponse = await getFacturas(idLote, numeroFactura)
  if (isFacturaGetResponseArr(_facturasResponse)) {
    return _facturasResponse
  }
}

const getNumeroFacturaDOM = (index: number) => {
  let _numeroFacturaParteUno = (document.getElementById('Numero-de-factura-input' + index + 'parte1') as HTMLInputElement).value
  let _numeroFacturaParteDos = (document.getElementById('Numero-de-factura-input' + index + 'parte2') as HTMLInputElement).value
  let _numeroFacturaParteTres = (document.getElementById('Numero-de-factura-input' + index + 'parte3') as HTMLInputElement).value
  let _numeroConcat = _numeroFacturaParteUno + _numeroFacturaParteDos + _numeroFacturaParteTres
  let numeroFactura = new NumeroFactura(_numeroConcat)

  return numeroFactura
}

const getMontoDom = (index: number) => {

  function convertirNumero(cadena: string | number): number {
    if (typeof cadena === 'number') {
      cadena = cadena.toString();
    }
    if (!isNaN(parseFloat(cadena))) {
      let nuevaCadena = cadena.replace(/,/g, '');
      if (nuevaCadena.endsWith('.')) {
        nuevaCadena = nuevaCadena.slice(0, -1);
      }
      return parseFloat(nuevaCadena);
    } else {
      throw new Error('La cadena proporcionada no es un número válido.');
    }
  }

  let unMonto: MontoInsert
  unMonto = {
    moneda: (document.getElementById('monendaState' + index) as HTMLInputElement).value,
    total: convertirNumero((document.getElementById('total-input' + index) as HTMLInputElement).value),
    exentas: convertirNumero((document.getElementById('exentas' + index) as HTMLInputElement).value),
    ivaCincoPorCiento: convertirNumero((document.getElementById('iva5' + index) as HTMLInputElement).value),
    ivaDiezPorCiento: convertirNumero((document.getElementById('iva10' + index) as HTMLInputElement).value),
    gravadasCincoPorCiento: convertirNumero((document.getElementById('gravadas5' + index) as HTMLInputElement).value),
    gravadasDiezPorCiento: convertirNumero((document.getElementById('gravadas10' + index) as HTMLInputElement).value),
    montoTotalCinco: convertirNumero((document.getElementById('pcInput5' + index) as HTMLInputElement).value),
    montoTotalDiez: convertirNumero((document.getElementById('pcInput10' + index) as HTMLInputElement).value)
  }
  return unMonto
}

const getArticulosDOM = (index: number) => {
  const _cantidadCols = document.querySelector('#articulosBody' + index).childElementCount
  let _articulos: Articulo[]
  _articulos = []
  for (let i = 1; i < _cantidadCols + 1; i++) {
    const _ivaCincoPorCiento = (document.getElementById('cincopcntcol' + index + "a" + i) as HTMLInputElement).valueAsNumber
    const _ivaDiezPorCiento = (document.getElementById('diezpcentcol' + index + "a" + i) as HTMLInputElement).valueAsNumber
    console.log('index')
    console.log(_cantidadCols)
    let _gravadasCincoPorCiento: number
    let _gravadasDiezPorCiento: number
    const _total = (document.getElementById('montocol' + index + "a" + i) as HTMLInputElement).valueAsNumber
    console.log('total')
    console.log(_total)
    if (_ivaCincoPorCiento != 0 && !isNaN(_ivaCincoPorCiento)) {
      _gravadasCincoPorCiento = calcularGravadasArticulosDom(_ivaCincoPorCiento).cincoPorCiento
    }
    else {
      _gravadasDiezPorCiento = calcularGravadasArticulosDom(_ivaCincoPorCiento).diezPorCiento
    }
    console.log('gravadas')
    console.log(_gravadasCincoPorCiento)
    console.log(_gravadasDiezPorCiento)

    let _unArticulo: Articulo
    _unArticulo =
    {
      descripcion: (document.getElementById('artcol' + index + "a" + i) as HTMLInputElement).value,
      monto: _total,
      IvaCincoPorciento: (!isNaN(_ivaCincoPorCiento) && _ivaCincoPorCiento === 0) ? _ivaCincoPorCiento : 0,
      IvaDiezPorciento: (!isNaN(_ivaDiezPorCiento) && _ivaDiezPorCiento === 0) ? _ivaDiezPorCiento : 0,
      GravadasCincoPorciento: _gravadasCincoPorCiento,
      GravadasDiezPorCiento: _gravadasDiezPorCiento,
      cantidad: (document.getElementById('cantidadcol' + index + "a" + i) as HTMLInputElement).valueAsNumber,
      valorUnitario: (document.getElementById('preciounitariocol' + index + "a" + i) as HTMLInputElement).valueAsNumber,
    }
    _articulos.push(_unArticulo)
  }
  console.log(_articulos)
  return _articulos
}



