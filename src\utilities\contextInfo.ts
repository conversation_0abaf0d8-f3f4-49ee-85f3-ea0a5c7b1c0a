import { Ambiente } from "../entities/Enums/enumAmbientes"
// @params:
export function getAmbienteContexto() {

  if (document.URL.includes("indexQA.aspx")) {
    return Ambiente.qa
  }
  else if (document.URL.includes("dev")) {
    return Ambiente.dev
  }
  else {
    return Ambiente.prod
  }
}

export function getUrlBase() {
  if (getAmbienteContexto() === Ambiente.dev) {
    return 'https://expose-desa-gw.bancontinental.com.py'
  }
  else if (getAmbienteContexto() === Ambiente.qa) {
    return 'https://expose-qa-gw.bancontinental.com.py'
  }
  else {
    return 'https://apibanking-gw.bancontinental.com.py/'
  }
}

export function getUrlSPBase() {
  let siteUrl
  if (getAmbienteContexto() == Ambiente.dev) {
    siteUrl = "https://dev-intranet/sitios/"
  } else if (getAmbienteContexto() == Ambiente.qa) {
    siteUrl = "https://dev-intranet/sitios/"
  } else {
    siteUrl = "https://intranet/sitios/"
  }

  return siteUrl
}

export function getUrlSeguimientoFactura() {
  if (getAmbienteContexto() === Ambiente.dev) {
    return 'https://dev-intranet/sitios/Sistemas/SeguimientoFacturas';
  } else if (getAmbienteContexto() === Ambiente.qa) {
    return 'https://dev-intranet/sitios/Sistemas/SeguimientoFacturas';
  } else {
    return 'https://intranet/sitios/Sistemas/SeguimientoFacturas';
  }
}

