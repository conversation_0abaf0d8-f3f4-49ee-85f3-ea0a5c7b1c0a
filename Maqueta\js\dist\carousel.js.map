{"version": 3, "file": "carousel.js", "sources": ["../src/carousel.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.4.1): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME                   = 'carousel'\nconst VERSION                = '4.4.1'\nconst DATA_KEY               = 'bs.carousel'\nconst EVENT_KEY              = `.${DATA_KEY}`\nconst DATA_API_KEY           = '.data-api'\nconst JQUERY_NO_CONFLICT     = $.fn[NAME]\nconst ARROW_LEFT_KEYCODE     = 37 // KeyboardEvent.which value for left arrow key\nconst ARROW_RIGHT_KEYCODE    = 39 // KeyboardEvent.which value for right arrow key\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\nconst SWIPE_THRESHOLD        = 40\n\nconst Default = {\n  interval : 5000,\n  keyboard : true,\n  slide    : false,\n  pause    : 'hover',\n  wrap     : true,\n  touch    : true\n}\n\nconst DefaultType = {\n  interval : '(number|boolean)',\n  keyboard : 'boolean',\n  slide    : '(boolean|string)',\n  pause    : '(string|boolean)',\n  wrap     : 'boolean',\n  touch    : 'boolean'\n}\n\nconst Direction = {\n  NEXT     : 'next',\n  PREV     : 'prev',\n  LEFT     : 'left',\n  RIGHT    : 'right'\n}\n\nconst Event = {\n  SLIDE          : `slide${EVENT_KEY}`,\n  SLID           : `slid${EVENT_KEY}`,\n  KEYDOWN        : `keydown${EVENT_KEY}`,\n  MOUSEENTER     : `mouseenter${EVENT_KEY}`,\n  MOUSELEAVE     : `mouseleave${EVENT_KEY}`,\n  TOUCHSTART     : `touchstart${EVENT_KEY}`,\n  TOUCHMOVE      : `touchmove${EVENT_KEY}`,\n  TOUCHEND       : `touchend${EVENT_KEY}`,\n  POINTERDOWN    : `pointerdown${EVENT_KEY}`,\n  POINTERUP      : `pointerup${EVENT_KEY}`,\n  DRAG_START     : `dragstart${EVENT_KEY}`,\n  LOAD_DATA_API  : `load${EVENT_KEY}${DATA_API_KEY}`,\n  CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n}\n\nconst ClassName = {\n  CAROUSEL      : 'carousel',\n  ACTIVE        : 'active',\n  SLIDE         : 'slide',\n  RIGHT         : 'carousel-item-right',\n  LEFT          : 'carousel-item-left',\n  NEXT          : 'carousel-item-next',\n  PREV          : 'carousel-item-prev',\n  ITEM          : 'carousel-item',\n  POINTER_EVENT : 'pointer-event'\n}\n\nconst Selector = {\n  ACTIVE      : '.active',\n  ACTIVE_ITEM : '.active.carousel-item',\n  ITEM        : '.carousel-item',\n  ITEM_IMG    : '.carousel-item img',\n  NEXT_PREV   : '.carousel-item-next, .carousel-item-prev',\n  INDICATORS  : '.carousel-indicators',\n  DATA_SLIDE  : '[data-slide], [data-slide-to]',\n  DATA_RIDE   : '[data-ride=\"carousel\"]'\n}\n\nconst PointerType = {\n  TOUCH : 'touch',\n  PEN   : 'pen'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\nclass Carousel {\n  constructor(element, config) {\n    this._items         = null\n    this._interval      = null\n    this._activeElement = null\n    this._isPaused      = false\n    this._isSliding     = false\n    this.touchTimeout   = null\n    this.touchStartX    = 0\n    this.touchDeltaX    = 0\n\n    this._config            = this._getConfig(config)\n    this._element           = element\n    this._indicatorsElement = this._element.querySelector(Selector.INDICATORS)\n    this._touchSupported    = 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n    this._pointerEvent      = Boolean(window.PointerEvent || window.MSPointerEvent)\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  next() {\n    if (!this._isSliding) {\n      this._slide(Direction.NEXT)\n    }\n  }\n\n  nextWhenVisible() {\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden &&\n      ($(this._element).is(':visible') && $(this._element).css('visibility') !== 'hidden')) {\n      this.next()\n    }\n  }\n\n  prev() {\n    if (!this._isSliding) {\n      this._slide(Direction.PREV)\n    }\n  }\n\n  pause(event) {\n    if (!event) {\n      this._isPaused = true\n    }\n\n    if (this._element.querySelector(Selector.NEXT_PREV)) {\n      Util.triggerTransitionEnd(this._element)\n      this.cycle(true)\n    }\n\n    clearInterval(this._interval)\n    this._interval = null\n  }\n\n  cycle(event) {\n    if (!event) {\n      this._isPaused = false\n    }\n\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    if (this._config.interval && !this._isPaused) {\n      this._interval = setInterval(\n        (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n        this._config.interval\n      )\n    }\n  }\n\n  to(index) {\n    this._activeElement = this._element.querySelector(Selector.ACTIVE_ITEM)\n\n    const activeIndex = this._getItemIndex(this._activeElement)\n\n    if (index > this._items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      $(this._element).one(Event.SLID, () => this.to(index))\n      return\n    }\n\n    if (activeIndex === index) {\n      this.pause()\n      this.cycle()\n      return\n    }\n\n    const direction = index > activeIndex\n      ? Direction.NEXT\n      : Direction.PREV\n\n    this._slide(direction, this._items[index])\n  }\n\n  dispose() {\n    $(this._element).off(EVENT_KEY)\n    $.removeData(this._element, DATA_KEY)\n\n    this._items             = null\n    this._config            = null\n    this._element           = null\n    this._interval          = null\n    this._isPaused          = null\n    this._isSliding         = null\n    this._activeElement     = null\n    this._indicatorsElement = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...config\n    }\n    Util.typeCheckConfig(NAME, config, DefaultType)\n    return config\n  }\n\n  _handleSwipe() {\n    const absDeltax = Math.abs(this.touchDeltaX)\n\n    if (absDeltax <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltax / this.touchDeltaX\n\n    this.touchDeltaX = 0\n\n    // swipe left\n    if (direction > 0) {\n      this.prev()\n    }\n\n    // swipe right\n    if (direction < 0) {\n      this.next()\n    }\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      $(this._element)\n        .on(Event.KEYDOWN, (event) => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      $(this._element)\n        .on(Event.MOUSEENTER, (event) => this.pause(event))\n        .on(Event.MOUSELEAVE, (event) => this.cycle(event))\n    }\n\n    if (this._config.touch) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    if (!this._touchSupported) {\n      return\n    }\n\n    const start = (event) => {\n      if (this._pointerEvent && PointerType[event.originalEvent.pointerType.toUpperCase()]) {\n        this.touchStartX = event.originalEvent.clientX\n      } else if (!this._pointerEvent) {\n        this.touchStartX = event.originalEvent.touches[0].clientX\n      }\n    }\n\n    const move = (event) => {\n      // ensure swiping with one touch and not pinching\n      if (event.originalEvent.touches && event.originalEvent.touches.length > 1) {\n        this.touchDeltaX = 0\n      } else {\n        this.touchDeltaX = event.originalEvent.touches[0].clientX - this.touchStartX\n      }\n    }\n\n    const end = (event) => {\n      if (this._pointerEvent && PointerType[event.originalEvent.pointerType.toUpperCase()]) {\n        this.touchDeltaX = event.originalEvent.clientX - this.touchStartX\n      }\n\n      this._handleSwipe()\n      if (this._config.pause === 'hover') {\n        // If it's a touch-enabled device, mouseenter/leave are fired as\n        // part of the mouse compatibility events on first tap - the carousel\n        // would stop cycling until user tapped out of it;\n        // here, we listen for touchend, explicitly pause the carousel\n        // (as if it's the second time we tap on it, mouseenter compat event\n        // is NOT fired) and after a timeout (to allow for mouse compatibility\n        // events to fire) we explicitly restart cycling\n\n        this.pause()\n        if (this.touchTimeout) {\n          clearTimeout(this.touchTimeout)\n        }\n        this.touchTimeout = setTimeout((event) => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n      }\n    }\n\n    $(this._element.querySelectorAll(Selector.ITEM_IMG)).on(Event.DRAG_START, (e) => e.preventDefault())\n    if (this._pointerEvent) {\n      $(this._element).on(Event.POINTERDOWN, (event) => start(event))\n      $(this._element).on(Event.POINTERUP, (event) => end(event))\n\n      this._element.classList.add(ClassName.POINTER_EVENT)\n    } else {\n      $(this._element).on(Event.TOUCHSTART, (event) => start(event))\n      $(this._element).on(Event.TOUCHMOVE, (event) => move(event))\n      $(this._element).on(Event.TOUCHEND, (event) => end(event))\n    }\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    switch (event.which) {\n      case ARROW_LEFT_KEYCODE:\n        event.preventDefault()\n        this.prev()\n        break\n      case ARROW_RIGHT_KEYCODE:\n        event.preventDefault()\n        this.next()\n        break\n      default:\n    }\n  }\n\n  _getItemIndex(element) {\n    this._items = element && element.parentNode\n      ? [].slice.call(element.parentNode.querySelectorAll(Selector.ITEM))\n      : []\n    return this._items.indexOf(element)\n  }\n\n  _getItemByDirection(direction, activeElement) {\n    const isNextDirection = direction === Direction.NEXT\n    const isPrevDirection = direction === Direction.PREV\n    const activeIndex     = this._getItemIndex(activeElement)\n    const lastItemIndex   = this._items.length - 1\n    const isGoingToWrap   = isPrevDirection && activeIndex === 0 ||\n                            isNextDirection && activeIndex === lastItemIndex\n\n    if (isGoingToWrap && !this._config.wrap) {\n      return activeElement\n    }\n\n    const delta     = direction === Direction.PREV ? -1 : 1\n    const itemIndex = (activeIndex + delta) % this._items.length\n\n    return itemIndex === -1\n      ? this._items[this._items.length - 1] : this._items[itemIndex]\n  }\n\n  _triggerSlideEvent(relatedTarget, eventDirectionName) {\n    const targetIndex = this._getItemIndex(relatedTarget)\n    const fromIndex = this._getItemIndex(this._element.querySelector(Selector.ACTIVE_ITEM))\n    const slideEvent = $.Event(Event.SLIDE, {\n      relatedTarget,\n      direction: eventDirectionName,\n      from: fromIndex,\n      to: targetIndex\n    })\n\n    $(this._element).trigger(slideEvent)\n\n    return slideEvent\n  }\n\n  _setActiveIndicatorElement(element) {\n    if (this._indicatorsElement) {\n      const indicators = [].slice.call(this._indicatorsElement.querySelectorAll(Selector.ACTIVE))\n      $(indicators)\n        .removeClass(ClassName.ACTIVE)\n\n      const nextIndicator = this._indicatorsElement.children[\n        this._getItemIndex(element)\n      ]\n\n      if (nextIndicator) {\n        $(nextIndicator).addClass(ClassName.ACTIVE)\n      }\n    }\n  }\n\n  _slide(direction, element) {\n    const activeElement = this._element.querySelector(Selector.ACTIVE_ITEM)\n    const activeElementIndex = this._getItemIndex(activeElement)\n    const nextElement   = element || activeElement &&\n      this._getItemByDirection(direction, activeElement)\n    const nextElementIndex = this._getItemIndex(nextElement)\n    const isCycling = Boolean(this._interval)\n\n    let directionalClassName\n    let orderClassName\n    let eventDirectionName\n\n    if (direction === Direction.NEXT) {\n      directionalClassName = ClassName.LEFT\n      orderClassName = ClassName.NEXT\n      eventDirectionName = Direction.LEFT\n    } else {\n      directionalClassName = ClassName.RIGHT\n      orderClassName = ClassName.PREV\n      eventDirectionName = Direction.RIGHT\n    }\n\n    if (nextElement && $(nextElement).hasClass(ClassName.ACTIVE)) {\n      this._isSliding = false\n      return\n    }\n\n    const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n    if (slideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      return\n    }\n\n    this._isSliding = true\n\n    if (isCycling) {\n      this.pause()\n    }\n\n    this._setActiveIndicatorElement(nextElement)\n\n    const slidEvent = $.Event(Event.SLID, {\n      relatedTarget: nextElement,\n      direction: eventDirectionName,\n      from: activeElementIndex,\n      to: nextElementIndex\n    })\n\n    if ($(this._element).hasClass(ClassName.SLIDE)) {\n      $(nextElement).addClass(orderClassName)\n\n      Util.reflow(nextElement)\n\n      $(activeElement).addClass(directionalClassName)\n      $(nextElement).addClass(directionalClassName)\n\n      const nextElementInterval = parseInt(nextElement.getAttribute('data-interval'), 10)\n      if (nextElementInterval) {\n        this._config.defaultInterval = this._config.defaultInterval || this._config.interval\n        this._config.interval = nextElementInterval\n      } else {\n        this._config.interval = this._config.defaultInterval || this._config.interval\n      }\n\n      const transitionDuration = Util.getTransitionDurationFromElement(activeElement)\n\n      $(activeElement)\n        .one(Util.TRANSITION_END, () => {\n          $(nextElement)\n            .removeClass(`${directionalClassName} ${orderClassName}`)\n            .addClass(ClassName.ACTIVE)\n\n          $(activeElement).removeClass(`${ClassName.ACTIVE} ${orderClassName} ${directionalClassName}`)\n\n          this._isSliding = false\n\n          setTimeout(() => $(this._element).trigger(slidEvent), 0)\n        })\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      $(activeElement).removeClass(ClassName.ACTIVE)\n      $(nextElement).addClass(ClassName.ACTIVE)\n\n      this._isSliding = false\n      $(this._element).trigger(slidEvent)\n    }\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      let _config = {\n        ...Default,\n        ...$(this).data()\n      }\n\n      if (typeof config === 'object') {\n        _config = {\n          ..._config,\n          ...config\n        }\n      }\n\n      const action = typeof config === 'string' ? config : _config.slide\n\n      if (!data) {\n        data = new Carousel(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'number') {\n        data.to(config)\n      } else if (typeof action === 'string') {\n        if (typeof data[action] === 'undefined') {\n          throw new TypeError(`No method named \"${action}\"`)\n        }\n        data[action]()\n      } else if (_config.interval && _config.ride) {\n        data.pause()\n        data.cycle()\n      }\n    })\n  }\n\n  static _dataApiClickHandler(event) {\n    const selector = Util.getSelectorFromElement(this)\n\n    if (!selector) {\n      return\n    }\n\n    const target = $(selector)[0]\n\n    if (!target || !$(target).hasClass(ClassName.CAROUSEL)) {\n      return\n    }\n\n    const config = {\n      ...$(target).data(),\n      ...$(this).data()\n    }\n    const slideIndex = this.getAttribute('data-slide-to')\n\n    if (slideIndex) {\n      config.interval = false\n    }\n\n    Carousel._jQueryInterface.call($(target), config)\n\n    if (slideIndex) {\n      $(target).data(DATA_KEY).to(slideIndex)\n    }\n\n    event.preventDefault()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document)\n  .on(Event.CLICK_DATA_API, Selector.DATA_SLIDE, Carousel._dataApiClickHandler)\n\n$(window).on(Event.LOAD_DATA_API, () => {\n  const carousels = [].slice.call(document.querySelectorAll(Selector.DATA_RIDE))\n  for (let i = 0, len = carousels.length; i < len; i++) {\n    const $carousel = $(carousels[i])\n    Carousel._jQueryInterface.call($carousel, $carousel.data())\n  }\n})\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Carousel._jQueryInterface\n$.fn[NAME].Constructor = Carousel\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Carousel._jQueryInterface\n}\n\nexport default Carousel\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "ARROW_LEFT_KEYCODE", "ARROW_RIGHT_KEYCODE", "TOUCHEVENT_COMPAT_WAIT", "SWIPE_THRESHOLD", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "slide", "pause", "wrap", "touch", "DefaultType", "Direction", "NEXT", "PREV", "LEFT", "RIGHT", "Event", "SLIDE", "SLID", "KEYDOWN", "MOUSEENTER", "MOUSELEAVE", "TOUCHSTART", "TOUCHMOVE", "TOUCHEND", "POINTERDOWN", "POINTERUP", "DRAG_START", "LOAD_DATA_API", "CLICK_DATA_API", "ClassName", "CAROUSEL", "ACTIVE", "ITEM", "POINTER_EVENT", "Selector", "ACTIVE_ITEM", "ITEM_IMG", "NEXT_PREV", "INDICATORS", "DATA_SLIDE", "DATA_RIDE", "PointerType", "TOUCH", "PEN", "Carousel", "element", "config", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "touchStartX", "touchDeltaX", "_config", "_getConfig", "_element", "_indicatorsElement", "querySelector", "_touchSupported", "document", "documentElement", "navigator", "maxTouchPoints", "_pointerEvent", "Boolean", "window", "PointerEvent", "MSPointerEvent", "_addEventListeners", "next", "_slide", "nextWhenVisible", "hidden", "is", "css", "prev", "event", "<PERSON><PERSON>", "triggerTransitionEnd", "cycle", "clearInterval", "setInterval", "visibilityState", "bind", "to", "index", "activeIndex", "_getItemIndex", "length", "one", "direction", "dispose", "off", "removeData", "typeCheckConfig", "_handleSwipe", "absDeltax", "Math", "abs", "on", "_keydown", "_addTouchEventListeners", "start", "originalEvent", "pointerType", "toUpperCase", "clientX", "touches", "move", "end", "clearTimeout", "setTimeout", "querySelectorAll", "e", "preventDefault", "classList", "add", "test", "target", "tagName", "which", "parentNode", "slice", "call", "indexOf", "_getItemByDirection", "activeElement", "isNextDirection", "isPrevDirection", "lastItemIndex", "isGoingToWrap", "delta", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "slideEvent", "from", "trigger", "_setActiveIndicatorElement", "indicators", "removeClass", "nextIndicator", "children", "addClass", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "hasClass", "isDefaultPrevented", "slidEvent", "reflow", "nextElementInterval", "parseInt", "getAttribute", "defaultInterval", "transitionDuration", "getTransitionDurationFromElement", "TRANSITION_END", "emulateTransitionEnd", "_jQueryInterface", "each", "data", "action", "TypeError", "ride", "_dataApiClickHandler", "selector", "getSelectorFromElement", "slideIndex", "carousels", "i", "len", "$carousel", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAUA;;;;;;EAMA,IAAMA,IAAI,GAAqB,UAA/B;EACA,IAAMC,OAAO,GAAkB,OAA/B;EACA,IAAMC,QAAQ,GAAiB,aAA/B;EACA,IAAMC,SAAS,SAAoBD,QAAnC;EACA,IAAME,YAAY,GAAa,WAA/B;EACA,IAAMC,kBAAkB,GAAOC,CAAC,CAACC,EAAF,CAAKP,IAAL,CAA/B;EACA,IAAMQ,kBAAkB,GAAO,EAA/B;;EACA,IAAMC,mBAAmB,GAAM,EAA/B;;EACA,IAAMC,sBAAsB,GAAG,GAA/B;;EACA,IAAMC,eAAe,GAAU,EAA/B;EAEA,IAAMC,OAAO,GAAG;EACdC,EAAAA,QAAQ,EAAG,IADG;EAEdC,EAAAA,QAAQ,EAAG,IAFG;EAGdC,EAAAA,KAAK,EAAM,KAHG;EAIdC,EAAAA,KAAK,EAAM,OAJG;EAKdC,EAAAA,IAAI,EAAO,IALG;EAMdC,EAAAA,KAAK,EAAM;EANG,CAAhB;EASA,IAAMC,WAAW,GAAG;EAClBN,EAAAA,QAAQ,EAAG,kBADO;EAElBC,EAAAA,QAAQ,EAAG,SAFO;EAGlBC,EAAAA,KAAK,EAAM,kBAHO;EAIlBC,EAAAA,KAAK,EAAM,kBAJO;EAKlBC,EAAAA,IAAI,EAAO,SALO;EAMlBC,EAAAA,KAAK,EAAM;EANO,CAApB;EASA,IAAME,SAAS,GAAG;EAChBC,EAAAA,IAAI,EAAO,MADK;EAEhBC,EAAAA,IAAI,EAAO,MAFK;EAGhBC,EAAAA,IAAI,EAAO,MAHK;EAIhBC,EAAAA,KAAK,EAAM;EAJK,CAAlB;EAOA,IAAMC,KAAK,GAAG;EACZC,EAAAA,KAAK,YAAoBvB,SADb;EAEZwB,EAAAA,IAAI,WAAoBxB,SAFZ;EAGZyB,EAAAA,OAAO,cAAoBzB,SAHf;EAIZ0B,EAAAA,UAAU,iBAAoB1B,SAJlB;EAKZ2B,EAAAA,UAAU,iBAAoB3B,SALlB;EAMZ4B,EAAAA,UAAU,iBAAoB5B,SANlB;EAOZ6B,EAAAA,SAAS,gBAAoB7B,SAPjB;EAQZ8B,EAAAA,QAAQ,eAAoB9B,SARhB;EASZ+B,EAAAA,WAAW,kBAAoB/B,SATnB;EAUZgC,EAAAA,SAAS,gBAAoBhC,SAVjB;EAWZiC,EAAAA,UAAU,gBAAmBjC,SAXjB;EAYZkC,EAAAA,aAAa,WAAWlC,SAAX,GAAuBC,YAZxB;EAaZkC,EAAAA,cAAc,YAAWnC,SAAX,GAAuBC;EAbzB,CAAd;EAgBA,IAAMmC,SAAS,GAAG;EAChBC,EAAAA,QAAQ,EAAQ,UADA;EAEhBC,EAAAA,MAAM,EAAU,QAFA;EAGhBf,EAAAA,KAAK,EAAW,OAHA;EAIhBF,EAAAA,KAAK,EAAW,qBAJA;EAKhBD,EAAAA,IAAI,EAAY,oBALA;EAMhBF,EAAAA,IAAI,EAAY,oBANA;EAOhBC,EAAAA,IAAI,EAAY,oBAPA;EAQhBoB,EAAAA,IAAI,EAAY,eARA;EAShBC,EAAAA,aAAa,EAAG;EATA,CAAlB;EAYA,IAAMC,QAAQ,GAAG;EACfH,EAAAA,MAAM,EAAQ,SADC;EAEfI,EAAAA,WAAW,EAAG,uBAFC;EAGfH,EAAAA,IAAI,EAAU,gBAHC;EAIfI,EAAAA,QAAQ,EAAM,oBAJC;EAKfC,EAAAA,SAAS,EAAK,0CALC;EAMfC,EAAAA,UAAU,EAAI,sBANC;EAOfC,EAAAA,UAAU,EAAI,+BAPC;EAQfC,EAAAA,SAAS,EAAK;EARC,CAAjB;EAWA,IAAMC,WAAW,GAAG;EAClBC,EAAAA,KAAK,EAAG,OADU;EAElBC,EAAAA,GAAG,EAAK;EAFU,CAApB;EAKA;;;;;;MAKMC;;;EACJ,oBAAYC,OAAZ,EAAqBC,MAArB,EAA6B;EAC3B,SAAKC,MAAL,GAAsB,IAAtB;EACA,SAAKC,SAAL,GAAsB,IAAtB;EACA,SAAKC,cAAL,GAAsB,IAAtB;EACA,SAAKC,SAAL,GAAsB,KAAtB;EACA,SAAKC,UAAL,GAAsB,KAAtB;EACA,SAAKC,YAAL,GAAsB,IAAtB;EACA,SAAKC,WAAL,GAAsB,CAAtB;EACA,SAAKC,WAAL,GAAsB,CAAtB;EAEA,SAAKC,OAAL,GAA0B,KAAKC,UAAL,CAAgBV,MAAhB,CAA1B;EACA,SAAKW,QAAL,GAA0BZ,OAA1B;EACA,SAAKa,kBAAL,GAA0B,KAAKD,QAAL,CAAcE,aAAd,CAA4BzB,QAAQ,CAACI,UAArC,CAA1B;EACA,SAAKsB,eAAL,GAA0B,kBAAkBC,QAAQ,CAACC,eAA3B,IAA8CC,SAAS,CAACC,cAAV,GAA2B,CAAnG;EACA,SAAKC,aAAL,GAA0BC,OAAO,CAACC,MAAM,CAACC,YAAP,IAAuBD,MAAM,CAACE,cAA/B,CAAjC;;EAEA,SAAKC,kBAAL;EACD;;;;;EAYD;WAEAC,OAAA,gBAAO;EACL,QAAI,CAAC,KAAKpB,UAAV,EAAsB;EACpB,WAAKqB,MAAL,CAAY9D,SAAS,CAACC,IAAtB;EACD;EACF;;WAED8D,kBAAA,2BAAkB;EAChB;EACA;EACA,QAAI,CAACZ,QAAQ,CAACa,MAAV,IACD9E,CAAC,CAAC,KAAK6D,QAAN,CAAD,CAAiBkB,EAAjB,CAAoB,UAApB,KAAmC/E,CAAC,CAAC,KAAK6D,QAAN,CAAD,CAAiBmB,GAAjB,CAAqB,YAArB,MAAuC,QAD7E,EACwF;EACtF,WAAKL,IAAL;EACD;EACF;;WAEDM,OAAA,gBAAO;EACL,QAAI,CAAC,KAAK1B,UAAV,EAAsB;EACpB,WAAKqB,MAAL,CAAY9D,SAAS,CAACE,IAAtB;EACD;EACF;;WAEDN,QAAA,eAAMwE,KAAN,EAAa;EACX,QAAI,CAACA,KAAL,EAAY;EACV,WAAK5B,SAAL,GAAiB,IAAjB;EACD;;EAED,QAAI,KAAKO,QAAL,CAAcE,aAAd,CAA4BzB,QAAQ,CAACG,SAArC,CAAJ,EAAqD;EACnD0C,MAAAA,IAAI,CAACC,oBAAL,CAA0B,KAAKvB,QAA/B;EACA,WAAKwB,KAAL,CAAW,IAAX;EACD;;EAEDC,IAAAA,aAAa,CAAC,KAAKlC,SAAN,CAAb;EACA,SAAKA,SAAL,GAAiB,IAAjB;EACD;;WAEDiC,QAAA,eAAMH,KAAN,EAAa;EACX,QAAI,CAACA,KAAL,EAAY;EACV,WAAK5B,SAAL,GAAiB,KAAjB;EACD;;EAED,QAAI,KAAKF,SAAT,EAAoB;EAClBkC,MAAAA,aAAa,CAAC,KAAKlC,SAAN,CAAb;EACA,WAAKA,SAAL,GAAiB,IAAjB;EACD;;EAED,QAAI,KAAKO,OAAL,CAAapD,QAAb,IAAyB,CAAC,KAAK+C,SAAnC,EAA8C;EAC5C,WAAKF,SAAL,GAAiBmC,WAAW,CAC1B,CAACtB,QAAQ,CAACuB,eAAT,GAA2B,KAAKX,eAAhC,GAAkD,KAAKF,IAAxD,EAA8Dc,IAA9D,CAAmE,IAAnE,CAD0B,EAE1B,KAAK9B,OAAL,CAAapD,QAFa,CAA5B;EAID;EACF;;WAEDmF,KAAA,YAAGC,KAAH,EAAU;EAAA;;EACR,SAAKtC,cAAL,GAAsB,KAAKQ,QAAL,CAAcE,aAAd,CAA4BzB,QAAQ,CAACC,WAArC,CAAtB;;EAEA,QAAMqD,WAAW,GAAG,KAAKC,aAAL,CAAmB,KAAKxC,cAAxB,CAApB;;EAEA,QAAIsC,KAAK,GAAG,KAAKxC,MAAL,CAAY2C,MAAZ,GAAqB,CAA7B,IAAkCH,KAAK,GAAG,CAA9C,EAAiD;EAC/C;EACD;;EAED,QAAI,KAAKpC,UAAT,EAAqB;EACnBvD,MAAAA,CAAC,CAAC,KAAK6D,QAAN,CAAD,CAAiBkC,GAAjB,CAAqB5E,KAAK,CAACE,IAA3B,EAAiC;EAAA,eAAM,KAAI,CAACqE,EAAL,CAAQC,KAAR,CAAN;EAAA,OAAjC;EACA;EACD;;EAED,QAAIC,WAAW,KAAKD,KAApB,EAA2B;EACzB,WAAKjF,KAAL;EACA,WAAK2E,KAAL;EACA;EACD;;EAED,QAAMW,SAAS,GAAGL,KAAK,GAAGC,WAAR,GACd9E,SAAS,CAACC,IADI,GAEdD,SAAS,CAACE,IAFd;;EAIA,SAAK4D,MAAL,CAAYoB,SAAZ,EAAuB,KAAK7C,MAAL,CAAYwC,KAAZ,CAAvB;EACD;;WAEDM,UAAA,mBAAU;EACRjG,IAAAA,CAAC,CAAC,KAAK6D,QAAN,CAAD,CAAiBqC,GAAjB,CAAqBrG,SAArB;EACAG,IAAAA,CAAC,CAACmG,UAAF,CAAa,KAAKtC,QAAlB,EAA4BjE,QAA5B;EAEA,SAAKuD,MAAL,GAA0B,IAA1B;EACA,SAAKQ,OAAL,GAA0B,IAA1B;EACA,SAAKE,QAAL,GAA0B,IAA1B;EACA,SAAKT,SAAL,GAA0B,IAA1B;EACA,SAAKE,SAAL,GAA0B,IAA1B;EACA,SAAKC,UAAL,GAA0B,IAA1B;EACA,SAAKF,cAAL,GAA0B,IAA1B;EACA,SAAKS,kBAAL,GAA0B,IAA1B;EACD;;;WAIDF,aAAA,oBAAWV,MAAX,EAAmB;EACjBA,IAAAA,MAAM,sBACD5C,OADC,MAED4C,MAFC,CAAN;EAIAiC,IAAAA,IAAI,CAACiB,eAAL,CAAqB1G,IAArB,EAA2BwD,MAA3B,EAAmCrC,WAAnC;EACA,WAAOqC,MAAP;EACD;;WAEDmD,eAAA,wBAAe;EACb,QAAMC,SAAS,GAAGC,IAAI,CAACC,GAAL,CAAS,KAAK9C,WAAd,CAAlB;;EAEA,QAAI4C,SAAS,IAAIjG,eAAjB,EAAkC;EAChC;EACD;;EAED,QAAM2F,SAAS,GAAGM,SAAS,GAAG,KAAK5C,WAAnC;EAEA,SAAKA,WAAL,GAAmB,CAAnB,CATa;;EAYb,QAAIsC,SAAS,GAAG,CAAhB,EAAmB;EACjB,WAAKf,IAAL;EACD,KAdY;;;EAiBb,QAAIe,SAAS,GAAG,CAAhB,EAAmB;EACjB,WAAKrB,IAAL;EACD;EACF;;WAEDD,qBAAA,8BAAqB;EAAA;;EACnB,QAAI,KAAKf,OAAL,CAAanD,QAAjB,EAA2B;EACzBR,MAAAA,CAAC,CAAC,KAAK6D,QAAN,CAAD,CACG4C,EADH,CACMtF,KAAK,CAACG,OADZ,EACqB,UAAC4D,KAAD;EAAA,eAAW,MAAI,CAACwB,QAAL,CAAcxB,KAAd,CAAX;EAAA,OADrB;EAED;;EAED,QAAI,KAAKvB,OAAL,CAAajD,KAAb,KAAuB,OAA3B,EAAoC;EAClCV,MAAAA,CAAC,CAAC,KAAK6D,QAAN,CAAD,CACG4C,EADH,CACMtF,KAAK,CAACI,UADZ,EACwB,UAAC2D,KAAD;EAAA,eAAW,MAAI,CAACxE,KAAL,CAAWwE,KAAX,CAAX;EAAA,OADxB,EAEGuB,EAFH,CAEMtF,KAAK,CAACK,UAFZ,EAEwB,UAAC0D,KAAD;EAAA,eAAW,MAAI,CAACG,KAAL,CAAWH,KAAX,CAAX;EAAA,OAFxB;EAGD;;EAED,QAAI,KAAKvB,OAAL,CAAa/C,KAAjB,EAAwB;EACtB,WAAK+F,uBAAL;EACD;EACF;;WAEDA,0BAAA,mCAA0B;EAAA;;EACxB,QAAI,CAAC,KAAK3C,eAAV,EAA2B;EACzB;EACD;;EAED,QAAM4C,KAAK,GAAG,SAARA,KAAQ,CAAC1B,KAAD,EAAW;EACvB,UAAI,MAAI,CAACb,aAAL,IAAsBxB,WAAW,CAACqC,KAAK,CAAC2B,aAAN,CAAoBC,WAApB,CAAgCC,WAAhC,EAAD,CAArC,EAAsF;EACpF,QAAA,MAAI,CAACtD,WAAL,GAAmByB,KAAK,CAAC2B,aAAN,CAAoBG,OAAvC;EACD,OAFD,MAEO,IAAI,CAAC,MAAI,CAAC3C,aAAV,EAAyB;EAC9B,QAAA,MAAI,CAACZ,WAAL,GAAmByB,KAAK,CAAC2B,aAAN,CAAoBI,OAApB,CAA4B,CAA5B,EAA+BD,OAAlD;EACD;EACF,KAND;;EAQA,QAAME,IAAI,GAAG,SAAPA,IAAO,CAAChC,KAAD,EAAW;EACtB;EACA,UAAIA,KAAK,CAAC2B,aAAN,CAAoBI,OAApB,IAA+B/B,KAAK,CAAC2B,aAAN,CAAoBI,OAApB,CAA4BnB,MAA5B,GAAqC,CAAxE,EAA2E;EACzE,QAAA,MAAI,CAACpC,WAAL,GAAmB,CAAnB;EACD,OAFD,MAEO;EACL,QAAA,MAAI,CAACA,WAAL,GAAmBwB,KAAK,CAAC2B,aAAN,CAAoBI,OAApB,CAA4B,CAA5B,EAA+BD,OAA/B,GAAyC,MAAI,CAACvD,WAAjE;EACD;EACF,KAPD;;EASA,QAAM0D,GAAG,GAAG,SAANA,GAAM,CAACjC,KAAD,EAAW;EACrB,UAAI,MAAI,CAACb,aAAL,IAAsBxB,WAAW,CAACqC,KAAK,CAAC2B,aAAN,CAAoBC,WAApB,CAAgCC,WAAhC,EAAD,CAArC,EAAsF;EACpF,QAAA,MAAI,CAACrD,WAAL,GAAmBwB,KAAK,CAAC2B,aAAN,CAAoBG,OAApB,GAA8B,MAAI,CAACvD,WAAtD;EACD;;EAED,MAAA,MAAI,CAAC4C,YAAL;;EACA,UAAI,MAAI,CAAC1C,OAAL,CAAajD,KAAb,KAAuB,OAA3B,EAAoC;EAClC;EACA;EACA;EACA;EACA;EACA;EACA;EAEA,QAAA,MAAI,CAACA,KAAL;;EACA,YAAI,MAAI,CAAC8C,YAAT,EAAuB;EACrB4D,UAAAA,YAAY,CAAC,MAAI,CAAC5D,YAAN,CAAZ;EACD;;EACD,QAAA,MAAI,CAACA,YAAL,GAAoB6D,UAAU,CAAC,UAACnC,KAAD;EAAA,iBAAW,MAAI,CAACG,KAAL,CAAWH,KAAX,CAAX;EAAA,SAAD,EAA+B9E,sBAAsB,GAAG,MAAI,CAACuD,OAAL,CAAapD,QAArE,CAA9B;EACD;EACF,KArBD;;EAuBAP,IAAAA,CAAC,CAAC,KAAK6D,QAAL,CAAcyD,gBAAd,CAA+BhF,QAAQ,CAACE,QAAxC,CAAD,CAAD,CAAqDiE,EAArD,CAAwDtF,KAAK,CAACW,UAA9D,EAA0E,UAACyF,CAAD;EAAA,aAAOA,CAAC,CAACC,cAAF,EAAP;EAAA,KAA1E;;EACA,QAAI,KAAKnD,aAAT,EAAwB;EACtBrE,MAAAA,CAAC,CAAC,KAAK6D,QAAN,CAAD,CAAiB4C,EAAjB,CAAoBtF,KAAK,CAACS,WAA1B,EAAuC,UAACsD,KAAD;EAAA,eAAW0B,KAAK,CAAC1B,KAAD,CAAhB;EAAA,OAAvC;EACAlF,MAAAA,CAAC,CAAC,KAAK6D,QAAN,CAAD,CAAiB4C,EAAjB,CAAoBtF,KAAK,CAACU,SAA1B,EAAqC,UAACqD,KAAD;EAAA,eAAWiC,GAAG,CAACjC,KAAD,CAAd;EAAA,OAArC;;EAEA,WAAKrB,QAAL,CAAc4D,SAAd,CAAwBC,GAAxB,CAA4BzF,SAAS,CAACI,aAAtC;EACD,KALD,MAKO;EACLrC,MAAAA,CAAC,CAAC,KAAK6D,QAAN,CAAD,CAAiB4C,EAAjB,CAAoBtF,KAAK,CAACM,UAA1B,EAAsC,UAACyD,KAAD;EAAA,eAAW0B,KAAK,CAAC1B,KAAD,CAAhB;EAAA,OAAtC;EACAlF,MAAAA,CAAC,CAAC,KAAK6D,QAAN,CAAD,CAAiB4C,EAAjB,CAAoBtF,KAAK,CAACO,SAA1B,EAAqC,UAACwD,KAAD;EAAA,eAAWgC,IAAI,CAAChC,KAAD,CAAf;EAAA,OAArC;EACAlF,MAAAA,CAAC,CAAC,KAAK6D,QAAN,CAAD,CAAiB4C,EAAjB,CAAoBtF,KAAK,CAACQ,QAA1B,EAAoC,UAACuD,KAAD;EAAA,eAAWiC,GAAG,CAACjC,KAAD,CAAd;EAAA,OAApC;EACD;EACF;;WAEDwB,WAAA,kBAASxB,KAAT,EAAgB;EACd,QAAI,kBAAkByC,IAAlB,CAAuBzC,KAAK,CAAC0C,MAAN,CAAaC,OAApC,CAAJ,EAAkD;EAChD;EACD;;EAED,YAAQ3C,KAAK,CAAC4C,KAAd;EACE,WAAK5H,kBAAL;EACEgF,QAAAA,KAAK,CAACsC,cAAN;EACA,aAAKvC,IAAL;EACA;;EACF,WAAK9E,mBAAL;EACE+E,QAAAA,KAAK,CAACsC,cAAN;EACA,aAAK7C,IAAL;EACA;EARJ;EAWD;;WAEDkB,gBAAA,uBAAc5C,OAAd,EAAuB;EACrB,SAAKE,MAAL,GAAcF,OAAO,IAAIA,OAAO,CAAC8E,UAAnB,GACV,GAAGC,KAAH,CAASC,IAAT,CAAchF,OAAO,CAAC8E,UAAR,CAAmBT,gBAAnB,CAAoChF,QAAQ,CAACF,IAA7C,CAAd,CADU,GAEV,EAFJ;EAGA,WAAO,KAAKe,MAAL,CAAY+E,OAAZ,CAAoBjF,OAApB,CAAP;EACD;;WAEDkF,sBAAA,6BAAoBnC,SAApB,EAA+BoC,aAA/B,EAA8C;EAC5C,QAAMC,eAAe,GAAGrC,SAAS,KAAKlF,SAAS,CAACC,IAAhD;EACA,QAAMuH,eAAe,GAAGtC,SAAS,KAAKlF,SAAS,CAACE,IAAhD;;EACA,QAAM4E,WAAW,GAAO,KAAKC,aAAL,CAAmBuC,aAAnB,CAAxB;;EACA,QAAMG,aAAa,GAAK,KAAKpF,MAAL,CAAY2C,MAAZ,GAAqB,CAA7C;EACA,QAAM0C,aAAa,GAAKF,eAAe,IAAI1C,WAAW,KAAK,CAAnC,IACAyC,eAAe,IAAIzC,WAAW,KAAK2C,aAD3D;;EAGA,QAAIC,aAAa,IAAI,CAAC,KAAK7E,OAAL,CAAahD,IAAnC,EAAyC;EACvC,aAAOyH,aAAP;EACD;;EAED,QAAMK,KAAK,GAAOzC,SAAS,KAAKlF,SAAS,CAACE,IAAxB,GAA+B,CAAC,CAAhC,GAAoC,CAAtD;EACA,QAAM0H,SAAS,GAAG,CAAC9C,WAAW,GAAG6C,KAAf,IAAwB,KAAKtF,MAAL,CAAY2C,MAAtD;EAEA,WAAO4C,SAAS,KAAK,CAAC,CAAf,GACH,KAAKvF,MAAL,CAAY,KAAKA,MAAL,CAAY2C,MAAZ,GAAqB,CAAjC,CADG,GACmC,KAAK3C,MAAL,CAAYuF,SAAZ,CAD1C;EAED;;WAEDC,qBAAA,4BAAmBC,aAAnB,EAAkCC,kBAAlC,EAAsD;EACpD,QAAMC,WAAW,GAAG,KAAKjD,aAAL,CAAmB+C,aAAnB,CAApB;;EACA,QAAMG,SAAS,GAAG,KAAKlD,aAAL,CAAmB,KAAKhC,QAAL,CAAcE,aAAd,CAA4BzB,QAAQ,CAACC,WAArC,CAAnB,CAAlB;;EACA,QAAMyG,UAAU,GAAGhJ,CAAC,CAACmB,KAAF,CAAQA,KAAK,CAACC,KAAd,EAAqB;EACtCwH,MAAAA,aAAa,EAAbA,aADsC;EAEtC5C,MAAAA,SAAS,EAAE6C,kBAF2B;EAGtCI,MAAAA,IAAI,EAAEF,SAHgC;EAItCrD,MAAAA,EAAE,EAAEoD;EAJkC,KAArB,CAAnB;EAOA9I,IAAAA,CAAC,CAAC,KAAK6D,QAAN,CAAD,CAAiBqF,OAAjB,CAAyBF,UAAzB;EAEA,WAAOA,UAAP;EACD;;WAEDG,6BAAA,oCAA2BlG,OAA3B,EAAoC;EAClC,QAAI,KAAKa,kBAAT,EAA6B;EAC3B,UAAMsF,UAAU,GAAG,GAAGpB,KAAH,CAASC,IAAT,CAAc,KAAKnE,kBAAL,CAAwBwD,gBAAxB,CAAyChF,QAAQ,CAACH,MAAlD,CAAd,CAAnB;EACAnC,MAAAA,CAAC,CAACoJ,UAAD,CAAD,CACGC,WADH,CACepH,SAAS,CAACE,MADzB;;EAGA,UAAMmH,aAAa,GAAG,KAAKxF,kBAAL,CAAwByF,QAAxB,CACpB,KAAK1D,aAAL,CAAmB5C,OAAnB,CADoB,CAAtB;;EAIA,UAAIqG,aAAJ,EAAmB;EACjBtJ,QAAAA,CAAC,CAACsJ,aAAD,CAAD,CAAiBE,QAAjB,CAA0BvH,SAAS,CAACE,MAApC;EACD;EACF;EACF;;WAEDyC,SAAA,gBAAOoB,SAAP,EAAkB/C,OAAlB,EAA2B;EAAA;;EACzB,QAAMmF,aAAa,GAAG,KAAKvE,QAAL,CAAcE,aAAd,CAA4BzB,QAAQ,CAACC,WAArC,CAAtB;;EACA,QAAMkH,kBAAkB,GAAG,KAAK5D,aAAL,CAAmBuC,aAAnB,CAA3B;;EACA,QAAMsB,WAAW,GAAKzG,OAAO,IAAImF,aAAa,IAC5C,KAAKD,mBAAL,CAAyBnC,SAAzB,EAAoCoC,aAApC,CADF;;EAEA,QAAMuB,gBAAgB,GAAG,KAAK9D,aAAL,CAAmB6D,WAAnB,CAAzB;;EACA,QAAME,SAAS,GAAGtF,OAAO,CAAC,KAAKlB,SAAN,CAAzB;EAEA,QAAIyG,oBAAJ;EACA,QAAIC,cAAJ;EACA,QAAIjB,kBAAJ;;EAEA,QAAI7C,SAAS,KAAKlF,SAAS,CAACC,IAA5B,EAAkC;EAChC8I,MAAAA,oBAAoB,GAAG5H,SAAS,CAAChB,IAAjC;EACA6I,MAAAA,cAAc,GAAG7H,SAAS,CAAClB,IAA3B;EACA8H,MAAAA,kBAAkB,GAAG/H,SAAS,CAACG,IAA/B;EACD,KAJD,MAIO;EACL4I,MAAAA,oBAAoB,GAAG5H,SAAS,CAACf,KAAjC;EACA4I,MAAAA,cAAc,GAAG7H,SAAS,CAACjB,IAA3B;EACA6H,MAAAA,kBAAkB,GAAG/H,SAAS,CAACI,KAA/B;EACD;;EAED,QAAIwI,WAAW,IAAI1J,CAAC,CAAC0J,WAAD,CAAD,CAAeK,QAAf,CAAwB9H,SAAS,CAACE,MAAlC,CAAnB,EAA8D;EAC5D,WAAKoB,UAAL,GAAkB,KAAlB;EACA;EACD;;EAED,QAAMyF,UAAU,GAAG,KAAKL,kBAAL,CAAwBe,WAAxB,EAAqCb,kBAArC,CAAnB;;EACA,QAAIG,UAAU,CAACgB,kBAAX,EAAJ,EAAqC;EACnC;EACD;;EAED,QAAI,CAAC5B,aAAD,IAAkB,CAACsB,WAAvB,EAAoC;EAClC;EACA;EACD;;EAED,SAAKnG,UAAL,GAAkB,IAAlB;;EAEA,QAAIqG,SAAJ,EAAe;EACb,WAAKlJ,KAAL;EACD;;EAED,SAAKyI,0BAAL,CAAgCO,WAAhC;;EAEA,QAAMO,SAAS,GAAGjK,CAAC,CAACmB,KAAF,CAAQA,KAAK,CAACE,IAAd,EAAoB;EACpCuH,MAAAA,aAAa,EAAEc,WADqB;EAEpC1D,MAAAA,SAAS,EAAE6C,kBAFyB;EAGpCI,MAAAA,IAAI,EAAEQ,kBAH8B;EAIpC/D,MAAAA,EAAE,EAAEiE;EAJgC,KAApB,CAAlB;;EAOA,QAAI3J,CAAC,CAAC,KAAK6D,QAAN,CAAD,CAAiBkG,QAAjB,CAA0B9H,SAAS,CAACb,KAApC,CAAJ,EAAgD;EAC9CpB,MAAAA,CAAC,CAAC0J,WAAD,CAAD,CAAeF,QAAf,CAAwBM,cAAxB;EAEA3E,MAAAA,IAAI,CAAC+E,MAAL,CAAYR,WAAZ;EAEA1J,MAAAA,CAAC,CAACoI,aAAD,CAAD,CAAiBoB,QAAjB,CAA0BK,oBAA1B;EACA7J,MAAAA,CAAC,CAAC0J,WAAD,CAAD,CAAeF,QAAf,CAAwBK,oBAAxB;EAEA,UAAMM,mBAAmB,GAAGC,QAAQ,CAACV,WAAW,CAACW,YAAZ,CAAyB,eAAzB,CAAD,EAA4C,EAA5C,CAApC;;EACA,UAAIF,mBAAJ,EAAyB;EACvB,aAAKxG,OAAL,CAAa2G,eAAb,GAA+B,KAAK3G,OAAL,CAAa2G,eAAb,IAAgC,KAAK3G,OAAL,CAAapD,QAA5E;EACA,aAAKoD,OAAL,CAAapD,QAAb,GAAwB4J,mBAAxB;EACD,OAHD,MAGO;EACL,aAAKxG,OAAL,CAAapD,QAAb,GAAwB,KAAKoD,OAAL,CAAa2G,eAAb,IAAgC,KAAK3G,OAAL,CAAapD,QAArE;EACD;;EAED,UAAMgK,kBAAkB,GAAGpF,IAAI,CAACqF,gCAAL,CAAsCpC,aAAtC,CAA3B;EAEApI,MAAAA,CAAC,CAACoI,aAAD,CAAD,CACGrC,GADH,CACOZ,IAAI,CAACsF,cADZ,EAC4B,YAAM;EAC9BzK,QAAAA,CAAC,CAAC0J,WAAD,CAAD,CACGL,WADH,CACkBQ,oBADlB,SAC0CC,cAD1C,EAEGN,QAFH,CAEYvH,SAAS,CAACE,MAFtB;EAIAnC,QAAAA,CAAC,CAACoI,aAAD,CAAD,CAAiBiB,WAAjB,CAAgCpH,SAAS,CAACE,MAA1C,SAAoD2H,cAApD,SAAsED,oBAAtE;EAEA,QAAA,MAAI,CAACtG,UAAL,GAAkB,KAAlB;EAEA8D,QAAAA,UAAU,CAAC;EAAA,iBAAMrH,CAAC,CAAC,MAAI,CAAC6D,QAAN,CAAD,CAAiBqF,OAAjB,CAAyBe,SAAzB,CAAN;EAAA,SAAD,EAA4C,CAA5C,CAAV;EACD,OAXH,EAYGS,oBAZH,CAYwBH,kBAZxB;EAaD,KA/BD,MA+BO;EACLvK,MAAAA,CAAC,CAACoI,aAAD,CAAD,CAAiBiB,WAAjB,CAA6BpH,SAAS,CAACE,MAAvC;EACAnC,MAAAA,CAAC,CAAC0J,WAAD,CAAD,CAAeF,QAAf,CAAwBvH,SAAS,CAACE,MAAlC;EAEA,WAAKoB,UAAL,GAAkB,KAAlB;EACAvD,MAAAA,CAAC,CAAC,KAAK6D,QAAN,CAAD,CAAiBqF,OAAjB,CAAyBe,SAAzB;EACD;;EAED,QAAIL,SAAJ,EAAe;EACb,WAAKvE,KAAL;EACD;EACF;;;aAIMsF,mBAAP,0BAAwBzH,MAAxB,EAAgC;EAC9B,WAAO,KAAK0H,IAAL,CAAU,YAAY;EAC3B,UAAIC,IAAI,GAAG7K,CAAC,CAAC,IAAD,CAAD,CAAQ6K,IAAR,CAAajL,QAAb,CAAX;;EACA,UAAI+D,OAAO,sBACNrD,OADM,MAENN,CAAC,CAAC,IAAD,CAAD,CAAQ6K,IAAR,EAFM,CAAX;;EAKA,UAAI,OAAO3H,MAAP,KAAkB,QAAtB,EAAgC;EAC9BS,QAAAA,OAAO,sBACFA,OADE,MAEFT,MAFE,CAAP;EAID;;EAED,UAAM4H,MAAM,GAAG,OAAO5H,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsCS,OAAO,CAAClD,KAA7D;;EAEA,UAAI,CAACoK,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAI7H,QAAJ,CAAa,IAAb,EAAmBW,OAAnB,CAAP;EACA3D,QAAAA,CAAC,CAAC,IAAD,CAAD,CAAQ6K,IAAR,CAAajL,QAAb,EAAuBiL,IAAvB;EACD;;EAED,UAAI,OAAO3H,MAAP,KAAkB,QAAtB,EAAgC;EAC9B2H,QAAAA,IAAI,CAACnF,EAAL,CAAQxC,MAAR;EACD,OAFD,MAEO,IAAI,OAAO4H,MAAP,KAAkB,QAAtB,EAAgC;EACrC,YAAI,OAAOD,IAAI,CAACC,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIC,SAAJ,wBAAkCD,MAAlC,QAAN;EACD;;EACDD,QAAAA,IAAI,CAACC,MAAD,CAAJ;EACD,OALM,MAKA,IAAInH,OAAO,CAACpD,QAAR,IAAoBoD,OAAO,CAACqH,IAAhC,EAAsC;EAC3CH,QAAAA,IAAI,CAACnK,KAAL;EACAmK,QAAAA,IAAI,CAACxF,KAAL;EACD;EACF,KAhCM,CAAP;EAiCD;;aAEM4F,uBAAP,8BAA4B/F,KAA5B,EAAmC;EACjC,QAAMgG,QAAQ,GAAG/F,IAAI,CAACgG,sBAAL,CAA4B,IAA5B,CAAjB;;EAEA,QAAI,CAACD,QAAL,EAAe;EACb;EACD;;EAED,QAAMtD,MAAM,GAAG5H,CAAC,CAACkL,QAAD,CAAD,CAAY,CAAZ,CAAf;;EAEA,QAAI,CAACtD,MAAD,IAAW,CAAC5H,CAAC,CAAC4H,MAAD,CAAD,CAAUmC,QAAV,CAAmB9H,SAAS,CAACC,QAA7B,CAAhB,EAAwD;EACtD;EACD;;EAED,QAAMgB,MAAM,sBACPlD,CAAC,CAAC4H,MAAD,CAAD,CAAUiD,IAAV,EADO,MAEP7K,CAAC,CAAC,IAAD,CAAD,CAAQ6K,IAAR,EAFO,CAAZ;;EAIA,QAAMO,UAAU,GAAG,KAAKf,YAAL,CAAkB,eAAlB,CAAnB;;EAEA,QAAIe,UAAJ,EAAgB;EACdlI,MAAAA,MAAM,CAAC3C,QAAP,GAAkB,KAAlB;EACD;;EAEDyC,IAAAA,QAAQ,CAAC2H,gBAAT,CAA0B1C,IAA1B,CAA+BjI,CAAC,CAAC4H,MAAD,CAAhC,EAA0C1E,MAA1C;;EAEA,QAAIkI,UAAJ,EAAgB;EACdpL,MAAAA,CAAC,CAAC4H,MAAD,CAAD,CAAUiD,IAAV,CAAejL,QAAf,EAAyB8F,EAAzB,CAA4B0F,UAA5B;EACD;;EAEDlG,IAAAA,KAAK,CAACsC,cAAN;EACD;;;;0BAncoB;EACnB,aAAO7H,OAAP;EACD;;;0BAEoB;EACnB,aAAOW,OAAP;EACD;;;;;EAgcH;;;;;;;EAMAN,CAAC,CAACiE,QAAD,CAAD,CACGwC,EADH,CACMtF,KAAK,CAACa,cADZ,EAC4BM,QAAQ,CAACK,UADrC,EACiDK,QAAQ,CAACiI,oBAD1D;EAGAjL,CAAC,CAACuE,MAAD,CAAD,CAAUkC,EAAV,CAAatF,KAAK,CAACY,aAAnB,EAAkC,YAAM;EACtC,MAAMsJ,SAAS,GAAG,GAAGrD,KAAH,CAASC,IAAT,CAAchE,QAAQ,CAACqD,gBAAT,CAA0BhF,QAAQ,CAACM,SAAnC,CAAd,CAAlB;;EACA,OAAK,IAAI0I,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGF,SAAS,CAACvF,MAAhC,EAAwCwF,CAAC,GAAGC,GAA5C,EAAiDD,CAAC,EAAlD,EAAsD;EACpD,QAAME,SAAS,GAAGxL,CAAC,CAACqL,SAAS,CAACC,CAAD,CAAV,CAAnB;;EACAtI,IAAAA,QAAQ,CAAC2H,gBAAT,CAA0B1C,IAA1B,CAA+BuD,SAA/B,EAA0CA,SAAS,CAACX,IAAV,EAA1C;EACD;EACF,CAND;EAQA;;;;;;EAMA7K,CAAC,CAACC,EAAF,CAAKP,IAAL,IAAasD,QAAQ,CAAC2H,gBAAtB;EACA3K,CAAC,CAACC,EAAF,CAAKP,IAAL,EAAW+L,WAAX,GAAyBzI,QAAzB;;EACAhD,CAAC,CAACC,EAAF,CAAKP,IAAL,EAAWgM,UAAX,GAAwB,YAAM;EAC5B1L,EAAAA,CAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb;EACA,SAAOiD,QAAQ,CAAC2H,gBAAhB;EACD,CAHD;;;;;;;;"}