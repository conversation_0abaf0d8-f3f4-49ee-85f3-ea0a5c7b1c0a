let username = '';
const previewDiv = document.getElementById('prevcontent');
const defaultDiv = document.getElementById('defaultcont');
// Close preview
const closePreview = () =>{
  document.getElementById('prevcontent').classList.remove('preview--show');
  $('.tbl-item').removeClass('tbl-item--selected');

  // Show default content
  showDefaultView();
}

const getDetailsData = () => {
  // Activar la linea clickeada agregando la clase tbl-item--selected

  // Traer los datos completos de la operacion
  
  // Mostrar el preview
  showDetailsView(data);
}

$('.tbl-item').on('click', function(){
  $(this).toggleClass('tbl-item--selected');
    let div = this;
    username = div.getElementsByClassName('tbl-item__name')[0].innerText;
  // Mostrar el preview
    showDetailsView(username);
});
                                                                                                                                                                
const defaultContent = () => {
  return(
    `
    <div class="text-center">
      <i class="fa fa-file fa-2x mb-3"></i>
      <h5 class="mb-0">Seleccione una Operación</h5>
      <p>Los detalles se visualizan aqui</p>
    </div>
    `
  )
};

const defaultLoader = () => {
  return(
    `
    <div class="text-center">
      <i class="fa fa-sync-alt fa-spin fa-2x mb-3"></i>
      <p>Obteniendo datos...</p>
    </div>
    `
  )
};

const multiSelect = (number) => {
  return(
    `
    <div class="preview__default d-flex align-items-center justify-content-center">
      <div class="text-center">
        <i class="fa fa-copy fa-2x mb-3"></i>
        <h5 class="mb-0">${number} Operaciones Seleccionadas</h5>
        <p><a href="#">Seleccionar todas</a> las operaciones</p>
        <div class="text-left px-5">
          <a href="#" class="d-block mb-2"><i class="fa fa-check"></i> Aprobar</a>
          <a href="#" class="d-block mb-2"><i class="fa fa-times"></i> Rechazar</a>
        </div>
      </div>
    </div>
  `
  )
};

// Function to display operation detail
const operacion = (username) => {
  // mapear/recibir array
    return(`
    <div class="px-4 py-3 d-flex justify-content-between bg-white sticky-top">
        <div class="d-flex align-items-center">
            <span onclick="closePreview()" class="pw-close">
              <span class="pw-close__bar-1"></span>
              <span class="pw-close__bar-2"></span>
            </span>
            <p class="ml-3 mb-0">Operación No. 5750131219</p>
        </div>
        <!-- Preview header actions -->
        <div class="btn-group">
            <a href="#" class="btn btn-outline-fun-blue">Rechazar</a>
            <a href="#" class="btn btn-fun-blue">Aprobar</a>
        </div>
    </div>
    <div class="prw-cont__detail px-4">
      <div class="row mb-4">
        <div class="col-12 col-md-4">
          <figure class="avatar avatar--lg">
            <img src="https://randomuser.me/api/portraits/men/1.jpg">
          </figure>
          <div class="mt-2 mb-2">
            <h5 class="mb-0">${username}</h5>
            <p class="mb-0">Clasificación 1</p>
            <p class="mb-0">Código: 410241</p>
            <p class="mb-0">Ref. Atraso: BUENA</p>
          </div>
          <div class="form-group">
            <button onclick="showDocumentsView()" class="btn btn-secondary">Ver documentos</button>
          </div>
        </div>
        <div class="col-12 col-md-8">
          <div class="row">
            <div class="col-12">
              <p>Primer crédito hace 2 años</p>
            </div>
            <div class="col-6">
              <div class="client-data">
                <b>Documento</b>
                <p>CI - 5.864.546</p>
              </div>
            </div>
            <div class="col-6">
              <div class="client-data">
                <b>Email</b>
                <p><EMAIL> <span class="badge badge-warning">Nuevo</span></p>
              </div>
            </div>
            <div class="col-6">
              <div class="client-data">
                <b>Celular</b>
                <p>0984893626 <span class="badge badge-warning">Nuevo</span></p>
              </div>
            </div>
            <div class="col-6">
              <div class="client-data">
                <b>Estado Civil</b>
                <p>Soltero</p>
              </div>
            </div>
            <div class="col-6">
              <div class="client-data">
                <b>Sucursal</b>
                <p>Matriz</p>
              </div>
            </div>
            <div class="col-6">
              <div class="client-data">
                <b>Oficial</b>
                <p>Nombre Ejemplo</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <ul class="nav nav-tabs mb-4" id="myTab" role="tablist">
        <li class="nav-item">
          <a class="nav-link active" id="detalles-tab" data-toggle="tab" href="#detalles" role="tab" aria-controls="detalles" aria-selected="true">Detalles</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="excepciones-tab" data-toggle="tab" href="#excepciones" role="tab" aria-controls="excepciones" aria-selected="true">Excepciones</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="profile-tab" data-toggle="tab" href="#profile" role="tab" aria-controls="profile" aria-selected="false">Seguimiento</a>
        </li>
      </ul>
      <div class="tab-content mb-4" id="myTabContent">
        <div class="tab-pane fade show active" id="detalles" role="tabpanel" aria-labelledby="deta..es-tab">
          <!-- Contenido de la operacion -->
            <div class="col-12">
              <div class="row">
                <div class="col-6">
                  <div class="client-data">
                    <b>Monto</b>
                    <p>5.864.546</p>
                  </div>
                </div>
                <div class="col-6">
                  <div class="client-data">
                    <b>Plazo</b>
                    <p>60 meses</p>
                  </div>
                </div>
                <div class="col-6">
                  <div class="client-data">
                    <b>Tasa</b>
                    <p>26%</p>
                  </div>
                </div>
                <div class="col-6">
                  <div class="client-data">
                    <b>Cuota</b>
                    <p>540.178</p>
                  </div>
                </div>
                <div class="col-6">
                  <div class="client-data">
                    <b>Destino de fondos</b>
                    <p>Vehiculo</p>
                  </div>
                </div>
                <div class="col-6">
                  <div class="client-data">
                    <b>Gastos Administrativos</b>
                    <p>550.000</p>
                  </div>
                </div>
                <div class="col-6">
                  <div class="client-data">
                    <b>Garantía</b>
                    <p>Cash Collateral</p>
                  </div>
                </div>
                <div class="col-6">
                  <div class="client-data">
                    <b>Línea de crédito</b>
                    <p>550.000</p>
                  </div>
                </div>
              </div>
            </div>
        </div>

        <div class="tab-pane fade" id="excepciones" role="tabpanel" aria-labelledby="excepciones-tab">
          <!-- Excepciones tab1 -->
          <div class="row">
            <div class="col-md-4">
              <div class="card px-3 py-2">
                <h5 class="mb-2">Tasa de interes</h5>
                <input class="form-control mb-2" type="text" value="13%">
                <p>Comentario de justificacion por defecto</p>
                <div class="form-group">
                  <a href="#" class="btn btn-outline-secondary">Rechazar</a>
                  <a href="#" class="btn btn-outline-success">Aprobar</a>
                </div>
              </div>
            </div>

            <div class="col-md-4">
              <div class="card px-3 py-2">
                <h5 class="mb-2">Gastos administrativos</h5>
                <input class="form-control" type="text" value="2.300.000">
                <p>Comentario de justificacion por defecto</p>
                <div class="form-group">
                  <a href="#" class="btn btn-outline-secondary">Rechazar</a>
                  <a href="#" class="btn btn-outline-success">Aprobar</a>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="card px-3 py-2">
                <h5 class="mb-2">CCT Vencido</h5>
                <p>Comentario de justificacion por defecto</p>
                <div class="form-group">
                  <a href="#" class="btn btn-outline-secondary">Rechazar</a>
                  <a href="#" class="btn btn-outline-success">Aprobar</a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab">
          <!-- Seguimiento -->
          <div class="row">
            <div class="col-12 col-md-4 text-center">
              <div class="card py-4">
                <h3 class="mb-0">3 de 8</h3>
                <p class="mb-0">Aprobado</p>
              </div>
            </div> 
            <div class="col-12 col-md-8">
              <div class="steps-vertical">
                <div class="step danger-step active">
                  <div class="step-info">
                    <div class="step-label">Análisis de Consumo</div>
                  </div>
                  <div class="step-content">
                    <span class="text-muted">2020-06-22 11:39</span> 
                    <p>Jose Ignacio Barrios Rodriguez</p>
                  </div>
                </div>
                <div class="step warning-step">
                  <div class="step-info">
                    <div class="step-label">Aprobación de seguimiento</div>
                  </div>
                  <div class="step-content">
                    <span class="text-muted">2020-06-22 11:39</span> 
                    <p>Jose Ignacio Barrios Rodriguez</p>
                  </div>
                </div>
                <div class="step">
                  <div class="step-info">
                    <div class="step-label">Inicio de seguimiento</div>
                  </div>
                  <div class="step-content">
                    <span class="text-muted">2020-06-22 11:39</span> 
                    <p>Jose Ignacio Barrios Rodriguez</p> 
                  </div>
                </div>
              </div>
            </div>
                     
          </div>
        </div>

      </div>
      <div class="comments py-4">
        <h6 class="mb-2">Comentarios</h6>
        <div class="comment">
          <div class="comment__photo">
            <figure class="avatar avatar--md avatar--text bg-fun-blue">
                <span class="text-white">SS</span>
            </figure>
          </div>
          <div class="comment__content">
            <div class="d-flex justify-content-between">
              <h5>Sergio Solis</h5>
              <p class="text-muted">JUE. FEB. 27, 15:40Hs.</p>
            </div>
            <p class="charge">Oficial de cuentas</p>
            <p>Vamos a darle al cliente lo que quiere</p>
          </div>
        </div>
      
        <div class="comment-box">
          <label for="comentarios">Comentar</label>
          <textarea id="comentairos" class="form-control" rows="3"></textarea>
        </div>
      </div>
    `);
}


const documentos = () => {
    return(
        `
      <div class="px-4 py-3 d-flex justify-content-between bg-white sticky-top">
        <div class="d-flex align-items-center">
            <span onclick="closePreview()" class="pw-close">
              <span class="pw-close__bar-1"></span>
              <span class="pw-close__bar-2"></span>
            </span>
            <p class="ml-3 mb-0">Solicitud No. 5750131219</p>
        </div>
        <!-- Preview header actions -->
        <div class="btn-group">
            <a href="#" class="btn btn-outline-fun-blue">Rechazar</a>
            <a href="#" class="btn btn-fun-blue">Aprobar</a>
        </div>
    </div>
      <div class="prw-cont__detail px-4">
        <div class="pt-5 pb-3">
            <h4 class="text-center">Documentos</h4>
            <button onclick="showDetailsView()" class="btn btn-link"><i class="fa fa-chevron-left"></i> Operación</button>
        </div>
    
        <div class="py-2 row">
            <div class="col-12 col-md-4">
                <input class="form-control" type="text" placeholder="Buscar documentos...">
            </div>
            <div class="col-12 col-md-4 offset-md-4 text-right">
                <a href="#" class="btn btn-secondary">Carpeta Digital</a>
                <a href="#" class="btn btn-secondary">Adjunto</a>
            </div>
        </div>
    
        <div class="card-documentos border-fun-blue">
            <div class="file-info d-flex align-items-center">
                <div class="mr-3">
                    <i class="fa fa-file-pdf fa-2x"></i>
                </div>
                <div class="text">
                    <p class="card-documentos__title">Comprobante de Ingresos</p>
                    <p class="mb-0">Tu archivo debe ser PDF, JPG o PNG</p>
                </div>
            </div>
            <div>
                <p class="text-muted mb-0">Adjunto</p>
            </div>
            <div class="file-action">
                <a href="#" class="btn btn-outline-secondary">Solicitar</a>
            </div>
        </div>
        <div class="card-documentos border-success">
            <div class="file-info d-flex align-items-center">
                <div class="mr-3">
                    <i class="fa fa-file-pdf fa-2x"></i>
                </div>
                <div class="text">
                    <p class="card-documentos__title">Documento de Identidad</p>
                    <p class="mb-0">Tu archivo debe ser PDF, JPG o PNG</p>
                </div>
            </div>
            <div>
                <p class="text-muted mb-0">Adjunto</p>
            </div>
            <div class="file-action">
                <a href="#" class="btn btn-outline-secondary">Solicitar</a>
            </div>
        </div>
        <div class="card-documentos border-milano-red">
            <div class="file-info d-flex align-items-center">
                <div class="mr-3">
                    <i class="fa fa-file-pdf fa-2x"></i>
                </div>
                <div class="text">
                    <p class="card-documentos__title">Patente Comercial</p>
                    <p class="mb-0">Tu archivo debe ser PDF, JPG o PNG</p>
                </div>
            </div>
            <div>
                <p class="text-muted mb-0">Carpeta Digital</p>
            </div>
            <div class="file-action">
                <a href="#" class="btn btn-outline-secondary">Solicitar</a>
            </div>
        </div>
        <div class="card-documentos border-yellow-sea">
            <div class="file-info d-flex align-items-center">
                <div class="mr-3">
                    <i class="fa fa-file-pdf fa-2x"></i>
                </div>
                <div class="text">
                    <p class="card-documentos__title">Patente Comercial</p>
                    <p class="mb-0">Tu archivo debe ser PDF, JPG o PNG</p>
                </div>
            </div>
            <div>
                <p class="text-muted mb-0">Carpeta Digital</p>
            </div>
            <div class="file-action">
                <a href="#" class="btn btn-outline-secondary">Solicitar</a>
            </div>
        </div>
        <div class="card-documentos border-secondary">
            <div class="file-info d-flex align-items-center">
                <div class="mr-3">
                    <i class="fa fa-file-pdf fa-2x"></i>
                </div>
                <div class="text">
                    <p class="card-documentos__title">Patente Comercial</p>
                    <p class="mb-0">Tu archivo debe ser PDF, JPG o PNG</p>
                </div>
            </div>
            <div>
                <p class="text-muted mb-0">Carpeta Digital</p>
            </div>
            <div class="file-action">
                <a href="#" class="btn btn-outline-secondary">Solicitar</a>
            </div>
        </div>

        <div class="card-documentos">
            <div class="file-info d-flex align-items-center">
                <div class="mr-3">
                    <i class="fa fa-file-pdf fa-2x"></i>
                </div>
                <div class="text">
                    <p class="card-documentos__title">Patente Comercial</p>
                    <p class="mb-0">Tu archivo debe ser PDF, JPG o PNG</p>
                </div>
            </div>
            <div>
                <p class="text-muted mb-0">Carpeta Digital</p>
            </div>
            <div class="file-action">
                <a href="#" class="btn btn-outline-secondary">Solicitar</a>
            </div>
        </div>

        <div class="btn-group mb-5">
            <a href="#" class="btn btn-outline-fun-blue">Anexar a Carpeta</a>
        </div>
      </div>
    `);
}

const showDetailsView = (username) => {
  defaultDiv.classList.remove('default--show');
  //opendetailview
  previewDiv.classList.add('preview--show');
  previewDiv.innerHTML = operacion(username);
}

const showDocumentsView = () => {
  defaultDiv.classList.remove('default--show');
  //opendetailview
  previewDiv.classList.add('preview--show');
  previewDiv.innerHTML = documentos();
}

const showDefaultView = () => {
  defaultDiv.classList.add('default--show');
  defaultDiv.innerHTML = defaultContent();
}

const showLoaderView = () => {
  defaultDiv.classList.add('default--show');
  defaultDiv.innerHTML = defaultLoader();
}