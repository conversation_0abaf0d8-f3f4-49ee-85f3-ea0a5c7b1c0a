/*
 
 ██╗      █████╗ ██╗   ██╗ ██████╗ ██╗   ██╗████████╗   ███████╗██╗   ██╗██████╗ ███╗   ███╗███████╗███╗   ██╗██╗   ██╗
 ██║     ██╔══██╗╚██╗ ██╔╝██╔═══██╗██║   ██║╚══██╔══╝   ██╔════╝██║   ██║██╔══██╗████╗ ████║██╔════╝████╗  ██║██║   ██║
 ██║     ███████║ ╚████╔╝ ██║   ██║██║   ██║   ██║█████╗███████╗██║   ██║██████╔╝██╔████╔██║█████╗  ██╔██╗ ██║██║   ██║
 ██║     ██╔══██║  ╚██╔╝  ██║   ██║██║   ██║   ██║╚════╝╚════██║██║   ██║██╔══██╗██║╚██╔╝██║██╔══╝  ██║╚██╗██║██║   ██║
 ███████╗██║  ██║   ██║   ╚██████╔╝╚██████╔╝   ██║      ███████║╚██████╔╝██████╔╝██║ ╚═╝ ██║███████╗██║ ╚████║╚██████╔╝
 ╚══════╝╚═╝  ╚═╝   ╚═╝    ╚═════╝  ╚═════╝    ╚═╝      ╚══════╝ ╚═════╝ ╚═════╝ ╚═╝     ╚═╝╚══════╝╚═╝  ╚═══╝ ╚═════╝ 
 - Estilos para el submenu                                                                                                                      
 
*/


// SubMenu Aside
/////////////////////////////////////////////////

@include block('submenu-aside') {

    position: sticky;
    top: 0;
    width: 100%;
    padding: ($spacer * 3) $spacer $spacer;      
    
    
    ul {
       width: 100%;
       list-style: none;
       margin: 0;
       padding: 0;
    }

    li { margin-bottom: $spacer; }

    a {
        position: relative;
        display: flex;
        color:$gray-800;
        padding: ($spacer / 2) 0;

            &:before { 
                position: absolute;
                // Altura menos ancho del arrow
                top: calc(50% - 10px);
                animation: arrow .5s ease-in-out;
            }

            &:hover {

                &:before {
                    right: -$spacer;
                    @include arrow(left, 10px, $white);
                }
            }
        
            // AnimateArrow
            @keyframes arrow {
                from { right: (-$spacer * 2); }
                to   { right: -$spacer; }
            }  
    }

    img { max-width: 180px; }  

    
    // Al activar pasa el link active a bold
    @include element('active') {
        
        font-weight: bold;

        a {
            &:before {
                right: -$spacer;
                animation: arrow .5s ease-in-out;
                @include arrow(left, 10px, $white);
            }
        }
        
    }

    // SoliticarTarjetas
    @include element('solicitud-card') {

        a {
            max-width: 170px;
            height: 100px;
            margin: 0 auto;
            justify-content: center;
            align-items: center;
            border:1px dashed $gray-300;
            border-radius: 4px;
        }
    }


}//endMenuAside

// SolicitarTarjeta
// @include block('solicitar-tarjeta') {
//     max-width: 170px;
//     height: 100px;
//     margin: 0 auto;
//     display: flex;
//     justify-content: center;
//     align-items: center;
//     border:1px dashed $gray-300;
//     border-radius: 4px;
    
// }