/*
██████╗  █████╗  ██████╗ ███████╗    ███████╗ ██████╗ ██████╗  ██████╗ ██████╗ ████████╗███████╗
██╔══██╗██╔══██╗██╔════╝ ██╔════╝    ██╔════╝██╔═══██╗██╔══██╗██╔═══██╗██╔══██╗╚══██╔══╝██╔════╝
██████╔╝███████║██║  ███╗█████╗      ███████╗██║   ██║██████╔╝██║   ██║██████╔╝   ██║   █████╗
██╔═══╝ ██╔══██║██║   ██║██╔══╝      ╚════██║██║   ██║██╔═══╝ ██║   ██║██╔══██╗   ██║   ██╔══╝
██║     ██║  ██║╚██████╔╝███████╗    ███████║╚██████╔╝██║     ╚██████╔╝██║  ██║   ██║   ███████╗
╚═╝     ╚═╝  ╚═╝ ╚═════╝ ╚══════╝    ╚══════╝ ╚═════╝ ╚═╝      ╚═════╝ ╚═╝  ╚═╝   ╚═╝   ╚══════╝
Especificos para el centro de ayuda
*/

.hero{
    padding: 10em 0;
    background: linear-gradient(to left, $fun-blue 0%, $hippie-blue 230%);
}

.pregunta-home {
    border: solid;
}

.header-conti {
    padding: 18px 0px 18px 0px;
    width: 100%;
    display: flex;
}

.img-illustracion {
    display: block;
    width: 68%;
    margin: auto;
    margin-bottom: -4px;
}

.cont-buscar {
    background-color: #1a4485;
    display: flow-root;
    padding: 20px 0px 100px 0px;
}

.bg-portada {
    background-image: url(../../img/soporte/bg-portada.jpg);
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
}

.ico-soporte {
    width: 100px;
}

.superponer {
    margin-top: -70px !important;
}

.box-categoria {
    border-radius: 15px;
    padding: 20px;
    margin: 10px;
    box-shadow: 0px 10px 15px 0px #00000014;
}

.buscar {
    width: 70% !important;
    margin: auto;
    border: solid 1px #dee2e6;
    border-radius: 4px;
}

.buscar-interna {
    border: solid 1px #dee2e6;
    border-radius: 4px;
}

.titulo-interna {
    margin-bottom: 30px;
    padding-bottom: 6px;
}

.card-text {
    font-size: 12px;
}

.card-title {
    font-size: 16px;
    font-weight: bold;
    color: #1a4485;
}

.accordion>.card>.card-header {
    margin-bottom: 0 !important;
}

/*Acordeon Collapse*/
.panel-default, a:hover {
    text-decoration: none;
  }

  .panel-default>.panel-heading a[aria-expanded="true"]:after {
    content: "\2212";
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
    float: right;
    font-size: 24px;
    color: #BECD42;
  }

  .panel-default>.panel-heading a[aria-expanded="false"]:after {
    content: "\002b";
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
    float: right;
    font-size: 24px;
    color: #BECD42;
  }

/*carrusel*/
.arrow-left {
    position: absolute;
    left: -15px;
    top: 47%;
    color: #bebebe;
}

.arrow-right {
    position: absolute;
    right: -15px;
    top: 47%;
    color: #bebebe;
}

.indicadores {
    position: absolute;
    right: 0;
    bottom: -50px;
    left: 0;
    z-index: 15;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-pack: center;
    justify-content: center;
    padding-left: 0;
    margin-right: 15%;
    margin-left: 15%;
    list-style: none;
}

.indicadores li {
    box-sizing: content-box;
    -ms-flex: 0 1 auto;
    flex: 0 1 auto;
    width: 12px;
    height: 12px;
    margin-right: 5px;
    margin-left: 5px;
    border-radius: 15px;
    text-indent: -999px;
    cursor: pointer;
    background-color: #1a4485;
    background-clip: padding-box;
    opacity: .5;
    transition: opacity .6s ease;
}

/*fin carrusel*/
.ico-margin {
    margin-top: 3px;
}

.campo {
    border: 0 !important;
    background-color: #e9ecef !important;
    font-size: 14px !important;
}

.campo-texto {
    border: 0 !important;
    background-color: #e9ecef !important;
    font-size: 14px !important;
    height: 212px !important;
}

.ico-campo {
    border: 0 !important;
    color: #1a4485 !important;
}

.card-conti {
    color: #151515;
}

.card-conti:hover {
    color: #1a4485;
    text-decoration: none;
}

.index-nav{
    display: flex;
    flex-direction: column;
    list-style: none;
    padding-left: 15px;

    .index-nav-item {
        padding: 0.1rem 0.2rem;
        a{
            color: $gray-600;
        }
    }

    .index-nav-item:hover {
        a{
            color: $fun-blue;
        }
    }

    .index-nav-item.active {
        a{
            color: $fun-blue;
        }
    }
}

/*responsive*/
@media screen and (max-width : 991px){
    .menu-vertical {
        display: none;
    }

    .menu-vertical-r {
        display: block !important;
    }

    .navbar-toggler-conti {
        padding: 7px 10px 7px 10px;
        font-size: 24px;
        line-height: 1;
        background-color: #1a4485;
        border: 1px solid transparent;
        border-radius: .25rem;
        color: #fff;
    }
}

@media screen and (max-width : 768px){
    .card-conti-footer {
        background-color: transparent !important;
        border: 0 !important;
    }

    .card-header-conti {
        background-color: transparent !important;
        border-bottom: 0 !important;
        border-bottom: solid 1px #fff !important;
    }
}