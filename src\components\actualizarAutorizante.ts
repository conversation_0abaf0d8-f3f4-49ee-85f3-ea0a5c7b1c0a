import { getUrlSPBase } from "../utilities/contextInfo";
import { SharepointListData } from "../entities/SharepointListData";
import { spActualizarItem } from "../comm/apiSharePoint";

export const actualizarAutorizante = async (autorizantes: { itemId: any, usuario: any }[], operacionSeguimiento: any, usuario: any): Promise<void> => {
  try {
    for (const autorizante of autorizantes) {
      console.log("autorizantes:", autorizantes);
      console.log("autorizante.itemId:", autorizante.itemId)
      console.log("autorizante.usuario:", autorizante.usuario)
      console.log("autorizante.usuario.$1p_1:", autorizante.usuario.$1p_1);
      console.log("usuario:", usuario);

      console.log("operacionSeguimiento:", operacionSeguimiento);

      let datos: SharepointListData[] = [
        {
          columna: 'Title',
          valor: JSON.parse(operacionSeguimiento).toString()
        },
        {
          columna: 'Usuario',
          valor: JSON.parse(usuario)
        }
      ];

      const onSuccess = () => { console.log("ACTUALIZADO EXITOSAMENTE!!!"); }
      const onFail = () => { console.log("NO SE PUDO ACTUALIZAR!!!"); }
      console.log("datos actualizar autorizante:", datos);
      await spActualizarItem(`${getUrlSPBase()}` + 'Sistemas/SeguimientoFacturas', 'AutorizantesSeguimiento', autorizante.itemId, datos, onSuccess, onFail);
    }
  } catch (error) {
    console.log("Error actualizarAutorizante:", error);
    throw error;
  }
}

