.steps-vertical {
  margin: 1em;
}

.steps-vertical,
.steps-vertical *::before,
.steps-vertical *::after {
  box-sizing: border-box;
}

.step {
  display: flex;
  align-items: flex-start;
  flex: 1;
  flex-direction: column;
  position: relative;
}

.step.active ~ .step::before {
  background-color: $key-lime-pie !important;
}

.step.active .step-info::before{
  border: 2px solid $white;
  box-shadow: 0 0 0 2px $key-lime-pie;
}

.step-info {
  text-align: center;
  align-self: flex-start;
  line-height: 2rem;
  display: flex;
  position: relative;
}

.step-info::before {
  content: '';
  text-align: center;
  position: absolute;
  z-index: 2;
  left: 0;
  top: 0;
  color: #fff;
  transform: translateX(50%);
  border-radius: 50%;
  background: $key-lime-pie;
  width: 1.5em;
  height: 1.5em;
}

.step-label {
  font-weight: 600;
  display: inline-block;
  color: rgba(0, 0, 0, 0.87);
  flex-flow: column nowrap;
  order: 2;
  margin-left: 3.35rem;
  margin-top: 0;
}

.step-content {
  display: block;
  margin-left: 2.5rem;
  padding: 0 0.87rem 0.87rem;
}

.steps-vertical > .step:not(:last-child):after {
  content: '';
  position: absolute;
  width: 2px;
  height: calc(100% - 1.45rem);
  left: 1.45rem;
  top: 1.45rem;
  background-color: $key-lime-pie;
}

.step.danger-step .step-info::before{
  background-color: $milano-red;
}

.step.danger-step > .step-info::before{
  border: 2px solid $white;
  box-shadow: 0 0 0 2px $milano-red;
}

.step.warning-step .step-info::before{
  background-color: $yellow-sea;
}

.step.warning-step > .step-info::before{
  border: 2px solid $white;
  box-shadow: 0 0 0 2px $yellow-sea;
}

.step.default-step .step-info::before{
  background-color: $gray-500;
}

.step.default-step > .step-info::before{
  border: 2px solid $white;
  box-shadow: none;
}

.step.info-step .step-info::before{
  background-color: $hippie-blue;
}

.step.info-step > .step-info::before{
  border: 2px solid $white;
  box-shadow: 0 0 0 2px $hippie-blue;
}