/*

███████╗██╗███╗   ██╗ █████╗ ███╗   ██╗███████╗██╗   ██╗███████╗
██╔════╝██║████╗  ██║██╔══██╗████╗  ██║██╔════╝╚██╗ ██╔╝██╔════╝
█████╗  ██║██╔██╗ ██║███████║██╔██╗ ██║███████╗ ╚████╔╝ ███████╗
██╔══╝  ██║██║╚██╗██║██╔══██║██║╚██╗██║╚════██║  ╚██╔╝  ╚════██║
██║     ██║██║ ╚████║██║  ██║██║ ╚████║███████║   ██║   ███████║
╚═╝     ╚═╝╚═╝  ╚═══╝╚═╝  ╚═╝╚═╝  ╚═══╝╚══════╝   ╚═╝   ╚══════╝

*/


// Archivos Base Bootstrap
@import "bootstrap/functions";
@import "bootstrap/variables";
@import "bootstrap/mixins";

// Base & Vendor
@import "base/font-face";
@import "vendor/bemify/bemify";

// Variables propias sobreescribe el reboot
@import "bootstrap/custom-variables";
@import "bootstrap/reboot";

// Componentes bootstrap que utilizamos
@import "bootstrap/nav";
@import "bootstrap/navbar";
@import "bootstrap/type";
@import "bootstrap/images";
@import "bootstrap/forms";
@import "bootstrap/custom-forms";
@import "bootstrap/buttons";
@import "bootstrap/grid";
@import "bootstrap/modal";
@import "bootstrap/close";
@import "bootstrap/card";
@import "bootstrap/transitions";
@import "bootstrap/dropdown";
@import "bootstrap/input-group";
@import "bootstrap/tables";
@import "bootstrap/spinners";
@import "bootstrap/grid";
@import "bootstrap/badge";


// Page & Layout
@import "layout/sidemenu-header";

// Utilities
@import "bootstrap/utilities";

// Componentes con mixins propios
@import "components/labels";
@import "components/avatar";
@import "components/brand";
@import "components/table-list";
@import "components/card-documentos";
@import "components/comments";
@import "components/steps-vertical";
@import "components/close-icon";
@import "components/custom-modal";
@import "components/inline-filters";
