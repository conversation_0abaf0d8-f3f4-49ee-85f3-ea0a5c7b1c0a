import axios, { AxiosRequestConfig } from 'axios';
import EndPoint from '../comm/EndPoint.Config';
import { getUrlBase } from '../utilities/contextInfo';
import { getPrivilegeHeaders } from '../comm/privilegeEnv';
import { ToastType, showToast } from '../utilities/toastUtils';

class PrivilegeServices {
    public static readonly getReglasPrivilege: any;
    public static readonly evaluarClienteMotorDecisiones: any;

    async getClientePrivilegePorDni(dni: string) {
        let urlBase = getUrlBase();
        const url = `${urlBase}${EndPoint.apiPrivilegedCustomerSegmentationEngine.vUno.get.getClientesPrivilegeDni.replace(':dni', dni)}`;

        try {
            let privilegeHeaders = await getPrivilegeHeaders();

            const config: AxiosRequestConfig = {
                headers: privilegeHeaders
            };

            const response = await axios.get(url, config);
            return response.data;
        } catch (error) {
            if(error.response.status == 400){
                showToast(
                    error?.response?.data || "Error al consultar el cliente Privilege por numero de documento.",
                    ToastType.Error
                )
            }
            console.error('Error al consultar el cliente Privilege por numero de documento: ', error);
            return {};
        }
    }

    async getClientePrivilegePorCodigo(codigoCliente: string) {
        let urlBase = getUrlBase();
        const url = `${urlBase}${EndPoint.apiPrivilegedCustomerSegmentationEngine.vUno.get.getClientesPrivilegeCodigo.replace(':codigoCliente', codigoCliente)}`;

        try {
            let privilegeHeaders = await getPrivilegeHeaders();

            const config: AxiosRequestConfig = {
                headers: privilegeHeaders
            };

            const response = await axios.get(url, config);
            return response.data;
        } catch (error) {
            if(error.response.status == 400){
                showToast(
                    error?.response?.data || "Error al consultar el cliente Privilege por codigo de cliente.",
                    ToastType.Error
                )
            }
            console.error('Error al consultar el cliente Privilege por codigo de cliente: ', error);
            return {};
        }
    }

    async getReglasPrivilege(codigoCliente: string) {
        let urlBase = getUrlBase();
        const url = `${urlBase}${EndPoint.apiPrivilegedCustomerSegmentationEngine.vUno.get.getReglasPrivilege.replace(':codigoCliente', codigoCliente)}`;

        try {
            let privilegeHeaders = await getPrivilegeHeaders();
            
            const config: AxiosRequestConfig = {
                headers: privilegeHeaders
            };
            const response = await axios.get(url, config);
            return response.data;
        } catch (error) {
            if(error.response.status == 400){
                showToast(
                    error?.response?.data || "Error al obtener las reglas de privilege.",
                    ToastType.Error
                )
            }
            console.error('Error al obtener las reglas de privilege: ', error);
            return {};
        }
    }

    async evaluarClienteMotorDecisiones(codigoCliente: string) {
        let urlBase = getUrlBase();
        const url = `${urlBase}${EndPoint.apiPrivilegedCustomerSegmentationEngine.vUno.post.evaluarClienteMotorDecisiones.replace(':codigoCliente', codigoCliente)}`;

        try {
            let privilegeHeaders = await getPrivilegeHeaders();
            
            const config: AxiosRequestConfig = {
                headers: privilegeHeaders
            };

            const response = await axios.post(url, {}, config);
            return response?.data;
        } catch (error) {
            if(error.response.status == 400){
                showToast(
                    error?.response?.data || "Error al evaluar cliente en el motor de decisiones.",
                    ToastType.Error
                )
            }
            console.error('Error al evaluar cliente en el motor de decisiones: ', error);
            return {};
        }
    }

    async iniciarSeguimiento(data: InicioSeguimientoRequest) {
        let urlBase = getUrlBase();
        const url = `${urlBase}${EndPoint.apiPrivilegedCustomerSegmentationEngine.vUno.post.iniciarSeguimiento}`;

        try {
            let privilegeHeaders = await getPrivilegeHeaders();
            
            const config: AxiosRequestConfig = {
                headers: privilegeHeaders
            };

            const response = await axios.post(url, data, config);
            return response?.data;
        } catch (error) {
            if(error?.response?.status == 400){
                showToast(
                    error?.response?.data || "Error al iniciar el seguimiento.",
                    ToastType.Error
                )
            }
            console.error('Error al iniciar el seguimiento: ', error);
            return {};
        }
    }

    async insertarSharepointId(data: UpdateBpmCabeceraRequest) {
        let urlBase = getUrlBase();
        const url = `${urlBase}${EndPoint.apiPrivilegedCustomerSegmentationEngine.vUno.put.insertaCabeceraBPM}`;

        try {
            let privilegeHeaders = await getPrivilegeHeaders();
            
            const config: AxiosRequestConfig = {
                headers: privilegeHeaders
            };

            const response = await axios.put(url, data, config);
            return response?.data;
        } catch (error) {
            if(error?.response?.status == 400){
                showToast(
                    error?.response?.data || "Error al insertar ID sharepoint.",
                    ToastType.Error
                )
            }
            console.error('Error al insertar el id Sharepoint: ', error);
            return {};
        }
    }
    async getClientePrivilegeAdicional(codigoCliente: string, esCedula: boolean) {
        let urlBase = getUrlBase();
        console.log("urlBase: ", urlBase, esCedula);
      
        let endpoint: string;
        if (esCedula) {
          endpoint = EndPoint.apiPrivilegedCustomerSegmentationEngine.vUno.get.getPrivilegeCedula;
        } else {
          endpoint = EndPoint.apiPrivilegedCustomerSegmentationEngine.vUno.get.getAdicionalesPrivilege;
        }
      
        const url = `${urlBase}${endpoint.replace(':codigoCliente', codigoCliente)}`;
        console.log("url: ", url);
      
        try {
          let privilegeHeaders = await getPrivilegeHeaders();
      
          if (!privilegeHeaders) {
            throw new Error('No se pudieron obtener los encabezados de Privilege.');
          }
      
          const config: AxiosRequestConfig = {
            headers: privilegeHeaders,
          };
      
          const response = await axios.get(url, config);
      
          if (!response || !response.data) {
            console.warn('La respuesta no contiene datos.');
            return null;
          }
      
          return response.data;
        } catch (error: any) {
          if (error?.response?.status === 400) {
            showToast(
              error?.response?.data || "Error al consultar los adicionales de cliente Privilege por código de cliente.",
              ToastType.Error
            );
          } else {
            showToast(
              "Ocurrió un error inesperado al consultar los adicionales de cliente Privilege.",
              ToastType.Error
            );
          }
          console.error('Error al consultar los adicionales de cliente Privilege: ', error);
          return {};
        }
      }
      


    async getClientePrivilegesSolicitudes(estado: string) {
        let urlBase = getUrlBase();
        const url = `${urlBase}${EndPoint.apiPrivilegedCustomerSegmentationEngine.vUno.get.getSolicitudesprivilege.replace(':estado', estado)}`;
        console.log("URL generada:", url);

        try {
            let privilegeHeaders = await getPrivilegeHeaders();
            console.log(privilegeHeaders);
            if (!privilegeHeaders) {
                throw new Error('No se pudieron obtener los encabezados de Privilege.');
            }

            const config: AxiosRequestConfig = {
                headers: privilegeHeaders,
            };

            const response = await axios.get(url, config);
            console.log("Lista de ", estado, "Response ",);
            // Verifica si la respuesta tiene datos válidos
            if (!response || !response.data) {
                console.warn('La respuesta no contiene datos .');
                return null;
            }

            console.log("Datos recibidos del backend:", response.data);
            return response.data;
        } catch (error: any) {
            if (error?.response?.status === 400) {
                showToast(
                    error?.response?.data || "Error al consultar los datos.",
                    ToastType.Error
                );
            } else {
                showToast("Ocurrió un error inesperado al consultar los datos.", ToastType.Error);
            }
            console.error('Error al consultar los datos: ', error);
            return null; // Retorna null en caso de error
        }
    }

    async putInclusionAdicionalPrivilege(
        codigoCliente: string,
        data: { CodigoPrivilege: string; GeneraTc: boolean; UserCarga: string }
    ) {
        console.log("puttInclusionAdicionalPrivilege");
        let urlBase = getUrlBase();
        const url = `${urlBase}${EndPoint.apiPrivilegedCustomerSegmentationEngine.vUno.put.inlcusionAdicionalPrivilege.replace(':codigoCliente', codigoCliente)}`;
        console.log("URL generada:", url);

        try {
            let privilegeHeaders = await getPrivilegeHeaders();
            console.log("Encabezados obtenidos:", privilegeHeaders);

            if (!privilegeHeaders) {
                console.error("No se pudieron obtener los encabezados de Privilege.");
                throw new Error('No se pudieron obtener los encabezados de Privilege.');
            }

            const config: AxiosRequestConfig = {
                headers: privilegeHeaders,
            };

            console.log("Configuración para Axios:", config);
            console.log("Datos enviados:", data);

            const response = await axios.put(url, data, config);
            console.log("Respuesta completa del backend:", response);

            if (!response || !response.data) {
                console.warn('La respuesta no contiene datos.');
                return null;
            }

            console.log("Datos recibidos del backend:", response.data);
            return response.data;

        } catch (error: any) {
            console.error("Error capturado:", error);

            if (error?.response) {
                console.error("Error en la respuesta del servidor:", {
                    status: error.response.status,
                    data: error.response.data,
                    headers: error.response.headers,
                });
            }

            if (error?.response?.status === 400) {
                showToast(
                    error?.response?.data || "Error al consultar los datos.",
                    ToastType.Error
                );
            } else {
                showToast("Ocurrió un error inesperado al consultar los datos.", ToastType.Error);
            }

            return null; // Retorna null en caso de error
        }
    }

    async putAutorizarAdicionalPrivilege(
        codigoCliente: string,
        data: { Accion: boolean; UserAutoriza: string }
    ) {
        const urlBase = getUrlBase();
        const url = `${urlBase}${EndPoint.apiPrivilegedCustomerSegmentationEngine.vUno.put.autorizarAdicionalPrivilege.replace(':codigoCliente', codigoCliente)}`;
        console.log("URL generada:", url);

        try {
            const privilegeHeaders = await getPrivilegeHeaders();
            console.log("Encabezados obtenidos:", privilegeHeaders);

            if (!privilegeHeaders) {
                console.error("No se pudieron obtener los encabezados de Privilege.");
                throw new Error('No se pudieron obtener los encabezados de Privilege.');
            }

            const config: AxiosRequestConfig = {
                headers: privilegeHeaders,
            };

            console.log("Configuración para Axios:", config);
            console.log("Datos enviados:", data);

            const response = await axios.put(url, data, config);
            console.log("Respuesta completa del backend:", response);

            if (!response || !response.data) {
                console.warn('La respuesta no contiene datos.');
                return null;
            }

            console.log("Datos recibidos del backend:", response.data);
            return response.data;

        } catch (error: any) {
            console.error("Error capturado:", error);

            if (error?.response) {
                console.error("Error en la respuesta del servidor:", {
                    status: error.response.status,
                    data: error.response.data,
                    headers: error.response.headers,
                });
            }

            if (error?.response?.status === 400) {
                showToast(
                    error?.response?.data || "Error al autorizar la solicitud.",
                    ToastType.Error
                );
            } else {
                showToast("Ocurrió un error inesperado al autorizar la solicitud.", ToastType.Error);
            }

            return null; 
        }
    }


    async putExclusionAdicionalPrivilege(
        codigoCliente: string,
        data: { UserCarga: string }
    ) {
        const urlBase = getUrlBase();
        const url = `${urlBase}${EndPoint.apiPrivilegedCustomerSegmentationEngine.vUno.put.exclusionAdicionalPrivilege.replace(':codigoCliente', codigoCliente)}`;
        console.log("URL generada:", url);
    
        try {
            const privilegeHeaders = await getPrivilegeHeaders();
            console.log("Encabezados obtenidos:", privilegeHeaders);
    
            if (!privilegeHeaders) {
                console.error("No se pudieron obtener los encabezados de Privilege.");
                throw new Error('No se pudieron obtener los encabezados de Privilege.');
            }
    
            const config: AxiosRequestConfig = {
                headers: privilegeHeaders,
            };
    
            console.log("Configuración para Axios:", config);
            console.log("Datos enviados:", data);
    
            const response = await axios.put(url, data, config);
            console.log("Respuesta completa del backend:", response);
    
            if (!response || !response.data) {
                console.warn('La respuesta no contiene datos.');
                return null;
            }
    
            console.log("Datos recibidos del backend:", response.data);
            return response.data;
    
        } catch (error: any) {
            console.error("Error capturado:", error);
    
            if (error?.response) {
                console.error("Error en la respuesta del servidor:", {
                    status: error.response.status,
                    data: error.response.data,
                    headers: error.response.headers,
                });
            }
    
            if (error?.response?.status === 400) {
                showToast(
                    error?.response?.data || "Error al procesar la exclusión del cliente.",
                    ToastType.Error
                );
            } else {
                showToast("Ocurrió un error inesperado al procesar la exclusión del cliente.", ToastType.Error);
            }
    
            return null; // Retorna null en caso de error
        }
    }
    
    async putAutorizarExclusionPrivilege(
        codigoCliente: string,
        data: { Accion: boolean; UserAutoriza: string }
    ) {
        const urlBase = getUrlBase();
        const url = `${urlBase}${EndPoint.apiPrivilegedCustomerSegmentationEngine.vUno.put.autorizaExlcusion.replace(':codigoCliente', codigoCliente)}`;
        console.log("URL generada:", url);
    
        try {
            const privilegeHeaders = await getPrivilegeHeaders();
            console.log("Encabezados obtenidos:", privilegeHeaders);
    
            if (!privilegeHeaders) {
                console.error("No se pudieron obtener los encabezados de Privilege.");
                throw new Error('No se pudieron obtener los encabezados de Privilege.');
            }
    
            const config: AxiosRequestConfig = {
                headers: privilegeHeaders,
            };
    
            console.log("Configuración para Axios:", config);
            console.log("Datos enviados:", data);
    
            const response = await axios.put(url, data, config);
            console.log("Respuesta completa del backend:", response);
    
            if (!response || !response.data) {
                console.warn('La respuesta no contiene datos.');
                return null;
            }
    
            console.log("Datos recibidos del backend:", response.data);
            return response.data;
    
        } catch (error: any) {
            console.error("Error capturado:", error);
    
            if (error?.response) {
                console.error("Error en la respuesta del servidor:", {
                    status: error.response.status,
                    data: error.response.data,
                    headers: error.response.headers,
                });
            }
    
            if (error?.response?.status === 400) {
                showToast(
                    error?.response?.data || "Error al autorizar la exclusión.",
                    ToastType.Error
                );
            } else {
                showToast("Ocurrió un error inesperado al autorizar la exclusión.", ToastType.Error);
            }
    
            return null; 
        }
    }
    

}

export default PrivilegeServices;