import React, { useState } from 'react';
import { <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap';
import { DatosProvider, Dispatch, useProveedor } from '../contextProvider/datosProveedorContext';
import ModalNexoAltaProveedor from '../abmProveedores/ModalNexoAltaProveedor';
import { getAmbienteContexto, getUrlBase } from '../../utilities/contextInfo';
import axios from 'axios';
import EndPoint from '../../comm/EndPoint.Config';
import { getApiProveedorFacturaHeaders } from '../../comm/contracts/ApiBpmPoliticasProcesosRequestHeaders';
import { ClienteProveedor } from '../../entities/ProveedorGet';
import { Estados } from '../../entities/Enums/estadosRespuesta';

interface ModalProps {
  mostrar: boolean;
  cerrar: () => void;
}

interface ModalIntermedioProps extends ModalProps {
  handler: Dispatch;
}

const ModalIntermedioAltaProveedor: React.FC<ModalIntermedioProps> = ({ mostrar, cerrar }) => {

  const { state, dispatch } = useProveedor();

  const [mostrarModalAltaProveedor, setMostrarModalAltaProveedor] = useState(false);

  const Params = {
    urlBase: getUrlBase(),
    ambiente: getAmbienteContexto(),
    request: axios.create(),
    endpoints: EndPoint.apiProveedorFactura
  }

  const obtenerDatosClienteProveedor = async ({ handler }: { handler: Dispatch }) => {
    let _cacheControl = 'max-age=60*60*10, private';
    let _headers = await getApiProveedorFacturaHeaders(_cacheControl);
    let _sturlEndpoint = Params.endpoints.vUno.get.clienteProveedor;
    let urlEndpoint = _sturlEndpoint.replace("${numeroDocumento}", state.nroD);
    let tipoDocumento = `?tipoDocumento=`;

    let config = {
      method: 'get',
      url: Params.urlBase + urlEndpoint + tipoDocumento + `${state.tipD}`,
      headers: _headers
    }

    let promise = Params.request<ClienteProveedor>(config)
      .then(function (response) {
        state.tipoDocumento = response.data.tipoDocumento;
        state.cedula = response.data.cedula;
        state.nombre = response.data.nombre;
        state.nombreDos = response.data.nombreDos;
        state.apellidoUno = response.data.apellidoUno;
        state.apellidoDos = response.data.apellidoDos;
        state.tipoPersona = response.data.tipoPersona;
        state.direccion = response.data.direccion;
        state.telefono = response.data.telefono;
        state.ciudad = response.data.ciudad;
        state.pais = response.data.pais;
        state.tipoProveedor = response.data.tipoProveedor;
        state.criticidad = response.data.criticidad;
        state.declaracionJurada = response.data.declaracionJurada;
        state.fechaDeclaracionJurada = response.data.fechaDeclaracionJurada;

        state.codigoClienteGCP = response.data.codigoCliente;

        if (response.status === Estados.estadoExitoso) {
          state.responseDataObtenerDatosClienteProveedor = true;
        }

        if (response.status === Estados.estadoValidacionIncorrecta) {
          alert("Por favor, verificar los datos.");
        }

        if (response.status === Estados.estadoNoAutorizado) {
          alert("Favor, refrescar la página y volver a intentar.");
        }

        if (response.status === Estados.estadoErrorServidores) {
          alert("Ocurrió un error.");
        }

        state.responseObtenerStatusCliente = response.status.toString();
        console.log("state.responseObtenerStatusCliente:", state.responseObtenerStatusCliente);

        console.log("Response data Cliente Proveedor:", response.data);

        handler('datos');

        return response.data;
      }).catch(function (error) {
        console.log("Error Get Cliente Proveedor:", error.response);
        return error.response;
      });

    let result = await promise;
    return result;
  }

  const abrirModalAltaProveedor = () => setMostrarModalAltaProveedor(true);
  const cerrarModalAltaProveedor = () => setMostrarModalAltaProveedor(false);

  const handleMostrarModalAltaProveedor = async (e: any) => {
    e.preventDefault();
    await obtenerDatosClienteProveedor({ handler: dispatch });
    abrirModalAltaProveedor();
  }

  return (
    <div>
      <Modal show={mostrar} onHide={cerrar}>
        <Modal.Header>
          <Modal.Title className='text-secondary'></Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p className='text-center font-weight-bold'>No se encontraron datos.</p>
          <p className='text-center font-weight-bold'>Desea dar de alta a un proveedor?</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={cerrar}>Cerrar</Button>
          <Button variant="primary" onClick={handleMostrarModalAltaProveedor}>Dar de alta</Button>
        </Modal.Footer>
      </Modal>
      {<DatosProvider><ModalNexoAltaProveedor
        handler={dispatch} {...state} mostrarModal={mostrarModalAltaProveedor} cerrarModal={cerrarModalAltaProveedor}
        datoStatusCliente={state.responseObtenerStatusCliente.toString()}
        tipoDocumento={state.tipoDocumento} cedula={state.cedula}
        nombre={state.nombre} nombreDos={state.nombreDos} apellidoUno={state.apellidoUno}
        apellidoDos={state.apellidoDos} tipoPersona={state.tipoPersona} direccion={state.direccion}
        telefono={state.telefono} ciudad={state.ciudad} pais={state.pais} tipoProveedor={state.tipoProveedor}
        criticidad={state.criticidad} declaracionJurada={state.declaracionJurada}
        fechaDeclaracionJurada={state.fechaDeclaracionJurada} codigoCliente={state.codigoClienteGCP} />
      </DatosProvider>}
    </div>
  )
}

export default ModalIntermedioAltaProveedor