import React from 'react'
import { getUrlSeguimientoFactura } from '../../utilities/contextInfo';

const delegarDe = async (idTarea: any, usuarioADelegar: any, listaSharepoint: any) => {

  let url = getUrlSeguimientoFactura() + "/_vti_bin/NintexWorkflow/Workflow.asmx";
  let xhr = new XMLHttpRequest();

  if ($.type(listaSharepoint) === "string") {
    console.log("Valor de listaSharepoint: ", listaSharepoint);
  } else {
    listaSharepoint = "Tareas de flujo de trabajo";
    console.log("Valor por defecto para listaSharepoint 'Tareas de flujo de trabajo'.");
  }

  let mensaje = `<?xml version="1.0" encoding="utf-8"?>
    <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
      <soap:Body>
        <DelegateTask xmlns="http://nintex.com">
          <spTaskId>${idTarea}</spTaskId>
          <taskListName>${listaSharepoint}</taskListName>
          <targetUsername>${usuarioADelegar}</targetUsername>
          <comments>string</comments>
          <sendNotification>false</sendNotification>
        </DelegateTask>
      </soap:Body>
    </soap:Envelope>`

  xhr.onloadend = function () {
    if (this.readyState == 4 && xhr.status == 200) {
      console.log("this.responseXML if:", this.responseXML);
      alert("Delegación realizada correctamente.");
      return false;
    } else {
      console.log("this.responseXML else:", this.responseXML);
      alert("Hubo un error al realizar la delegación.");
      return false;
    };
  };

  xhr.open("POST", url, true);
  xhr.setRequestHeader('Content-Type', 'text/xml');
  xhr.withCredentials = true;
  try {
    xhr.send(mensaje);
  } catch (e) {
    console.log("ERROR delegarTarea: ", e.message);
  }

  return (
    <div></div>
  )
}

export default delegarDe