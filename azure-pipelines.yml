trigger:
  - main

pool:
 vmImage: 'ubuntu-latest'

variables:
  - group: front-bpm-consola-factura-PROD  # favor cambiar valor de la variable --BpmConsola-- dentro del grupo de variables desa para el nombre del .js
steps:
- checkout: self
  fetchDepth: 0
  persistCredentials: true
- task: UseNode@1
  inputs:
    version: '$(node_version)'
  displayName: 'Instalar Node.js $(node_version)'
- script: |
    npm install
  displayName: 'Instalar dependencias con npm'
- task: replacetokens@5
  inputs:
    rootDirectory: '$(System.DefaultWorkingDirectory)'
    targetFiles: '$(build_dir)/index.aspx'
    encoding: 'auto'
    tokenPattern: 'custom'
    tokenPrefix: '$('
    tokenSuffix: ')'
    writeBOM: true
    actionOnMissing: 'warn'
    keepToken: false
    actionOnNoFiles: 'continue'
    enableTransforms: false
    enableRecursion: false
    useLegacyPattern: false
    enableTelemetry: true
- script: |
    cat $(System.DefaultWorkingDirectory)/$(build_dir)/index.aspx
- script: |
    echo "Reemplazando 'index' por '$(BpmConsola)' en webpack.config.js"
    sed -i "s/index: '.\/src\/index.tsx'/$(BpmConsola): '.\/src\/index.tsx'/" webpack.config.js
  displayName: 'Reemplazar clave dinámica en entry'
- script: |
    npx webpack
  displayName: 'Ejecutar Webpack (npx webpack)'
- task: DeleteFiles@1
  inputs:
    SourceFolder: '$(build_dir)'
    Contents: |
      IndexPrivilege.main.js
      indexRodneyPreMain.main.js
      finasys.main.js
      index.main.js
      index.main.js.LICENSE.txt
      init.main.js
      init.main.js.LICENSE.txt
      list.main.js
      test-bandeja.html
      c_css
      index.aspx
    RemoveDotFiles: true
- task: ArchiveFiles@2
  inputs:
    rootFolderOrFile: '$(build_dir)'
    includeRootFolder: true
    archiveType: 'zip'
    archiveFile: '$(build_dir).zip'
    replaceExistingArchive: true
- task: PublishBuildArtifacts@1
  inputs:
    PathtoPublish: '$(build_dir).zip'
    ArtifactName: 'webpack-output'
    publishLocation: 'Container'
  displayName: 'Publicar artefacto generado por Webpack'
  
- script: |
    echo "Detectando cambios en Maqueta/css y Maqueta/img..."
    mkdir -p extras/css
    mkdir -p extras/img

    echo "Rama origen: $BUILD_SOURCEBRANCH"
    echo "Rama destino (si es PR): $SYSTEM_PULLREQUEST_TARGETBRANCH"

    if [ -n "${SYSTEM_PULLREQUEST_TARGETBRANCH}" ]; then
      echo "Pipeline disparado por PR -> comparando ${SYSTEM_PULLREQUEST_TARGETBRANCH} con HEAD"
      git fetch origin ${SYSTEM_PULLREQUEST_TARGETBRANCH#refs/heads/}
      git diff --name-only origin/${SYSTEM_PULLREQUEST_TARGETBRANCH#refs/heads/} HEAD -- Maqueta/css Maqueta/img > modified_files.txt
    elif [[ "$BUILD_SOURCEBRANCH" == "refs/heads/main" ]]; then
      echo "Pipeline en main -> comparando último commit"
      git fetch origin main
      git diff --name-only origin/main~1 origin/main -- Maqueta/css Maqueta/img > modified_files.txt
    else
      BRANCH_NAME=${BUILD_SOURCEBRANCH#refs/heads/}
      echo "Pipeline en rama feature ($BRANCH_NAME) -> comparando contra origin/main"
      git fetch origin main
      git fetch origin $BRANCH_NAME
      git diff --name-only origin/main origin/$BRANCH_NAME -- Maqueta/css Maqueta/img > modified_files.txt
    fi

    echo "Archivos modificados:"
    cat modified_files.txt || true

    while IFS= read -r file; do
      if [[ $file == Maqueta/css* ]]; then
        cp --parents "$file" extras/css/
      elif [[ $file == Maqueta/img* ]]; then
        cp --parents "$file" extras/img/
      fi
    done < modified_files.txt

    echo "Contenido final de extras:"
    ls -R extras
  displayName: 'Preparar artefacto extras con modificados'

- task: PublishBuildArtifacts@1
  inputs:
    PathtoPublish: 'extras'
    ArtifactName: 'extras'
    publishLocation: 'Container'
  displayName: 'Publicar artefacto extras'