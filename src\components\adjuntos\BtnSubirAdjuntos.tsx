/// <reference types="webpack/module" />

import _ from "lodash";
import React, { useMemo, useState } from "react";
import ModalAdjuntoNotificacionSeleccionarArchivo from "../modales/ModalAdjuntoNotificacionSeleccionarArchivo";
import BtnVisualizarArchivo from "./BtnVisualizarArchivo";
import { useProveedor } from "../contextProvider/datosProveedorContext";
import { hadoop } from "../../comm/WSdocumentos";

export const BtnSubirAdjuntos = (props: any) => {

  const { state } = useProveedor();

  const [pdfFile, setPdfFile] = useState<File | null>(null);

  const [abrirModalNotificacionAdjuntarArchivo, setAbrirModalNotificacionAdjuntarArchivo] = useState(false);

  const guardarArchivo = useMemo(() => {
    try {
      const _input = document.getElementById('subirAdjuntos') as HTMLInputElement;
      if (_input) {
        window.appGlobal = _input.files;
      }
    } catch (error) {
      console.log("ERROR catch guardar archivo:", error);
    }
  }, [pdfFile]);
  useMemo(() => {
    try {
      setTimeout(() => {
        (document.getElementById('subirAdjuntos') as HTMLInputElement).files = props.adjuntos
      }, 500)
    }
    catch (e) {
      console.log("catch e UseMemo:", e);
    }

  }, [])

  const handleChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const fileInput = e.target;
    const files: any = fileInput.files;

    if (files) {
      setPdfFile(files);

      await props.onFileChange(files);
    }

    guardarArchivo
    console.log("guardarArchivo:", guardarArchivo);
  }

  const abrirModalNotificacionSeleccionarArchivo = () => setAbrirModalNotificacionAdjuntarArchivo(true);
  const cerrarModalNotificacionSeleccionarArchivo = () => setAbrirModalNotificacionAdjuntarArchivo(false);

  const subirArchivoPrueba = async () => {
    console.log("state.numeroOperacionCliente:", state.numeroOperacionCliente);
    let archivo = document.getElementById('subirAdjuntos') as HTMLInputElement;
    if (archivo.value === "" || !archivo.files || archivo.files.length === 0) {
      abrirModalNotificacionSeleccionarArchivo();
    } else {
      localStorage.setItem("archivosAdjuntos", "1");
      console.log("state.inicioDeSeguimientoCondicion:", state.inicioDeSeguimientoCondicion);
      console.log("state.condicionInicioSeguimiento:", state.condicionInicioSeguimiento);
      console.log("archivo.files", archivo.files);
      if (state.inicioDeSeguimientoCondicion === false) {
        await hadoop(archivo.files);
      }
      alert("Archivos adjuntados.");
      console.log("Archivos adjuntados.");
    }
  }

  return (
    <>
      <input id="subirAdjuntos" onChange={handleChange} type="file" name="subirAdjuntos" multiple />
      <div className="d-flex justify-content-end mt-2">
        <BtnVisualizarArchivo />
        <button type="button" onClick={subirArchivoPrueba} className="btn btn-primary btn-close" data-dismiss="modal">Confirmar</button>
      </div>
      {<ModalAdjuntoNotificacionSeleccionarArchivo abrir={abrirModalNotificacionAdjuntarArchivo} cerrar={cerrarModalNotificacionSeleccionarArchivo} />}
    </>
  )
}

