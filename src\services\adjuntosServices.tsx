import { getListadoAdjuntos } from "../comm/apiBpmPoliticasProcesos"
import { isDocumentoAdjuntoResponse } from "../comm/contracts/DocumentoAdjuntoResponse"


export const subirAdjuntos = async (files: FileList, codigoCliente: string) => {
  console.log("files:", files);
  let archivo = document.getElementById('subirAdjuntos') as HTMLInputElement;
  if (archivo.value === "" || !archivo.files || archivo.files.length === 0) {
    alert("Debe seleccionar un archivo.");
  } else {
    try {
      console.log("Subiendo archivo:", files);
    } catch (error) {
      console.error("Error al subir el archivo:", error);
      throw error;
    }
  }
};

export const getAdjuntos = async (nroOperacion: string, tipoDoc: string) => {
  const _tipoDoc: number = + tipoDoc;
  console.log("nroOperacion:", nroOperacion);
  console.log("_tipoDoc:", _tipoDoc);
  const _listadoAdjuntos = await getListadoAdjuntos(_tipoDoc, nroOperacion);
  console.log("_listadoAdjuntos:", _listadoAdjuntos);
  if (isDocumentoAdjuntoResponse(_listadoAdjuntos)) {
    console.log("_listadoAdjuntos:", _listadoAdjuntos);
    return _listadoAdjuntos
  }
  else {
    console.log('Problemas al traer listado de adjuntos.');
  }
}