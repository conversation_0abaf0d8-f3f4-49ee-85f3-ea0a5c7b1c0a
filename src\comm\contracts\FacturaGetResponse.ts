import { Articulo } from "../../entities/Articulo"
import { MontoInsert } from "../../entities/MontoInsert"
import { NumeroFactura } from "../../entities/NumeroFactura"
import { ProveedorGet } from "../../entities/ProveedorGet"

export type FacturaGetResponse =
  {
    idCarga: number,
    idLote: number,
    numeroLoteRelacional: number,
    idCargaRelacional: number,
    numeroFactura: NumeroFactura,
    proveedores: ProveedorGet,
    monto: MontoInsert,
    exentas: number,
    provision: string
    fechaEmision: string,
    timbrado: string,
    items: Articulo[]
  }

export function isFacturaGetResponseArr(FacturaGetResponse: any): FacturaGetResponse is FacturaGetResponse[] {
  return (FacturaGetResponse as FacturaGetResponse[]).length > 0;
}