{"version": 3, "file": "toast.js", "sources": ["../src/toast.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.4.1): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME               = 'toast'\nconst VERSION            = '4.4.1'\nconst DATA_KEY           = 'bs.toast'\nconst EVENT_KEY          = `.${DATA_KEY}`\nconst JQUERY_NO_CONFLICT = $.fn[NAME]\n\nconst Event = {\n  CLICK_DISMISS : `click.dismiss${EVENT_KEY}`,\n  HIDE          : `hide${EVENT_KEY}`,\n  HIDDEN        : `hidden${EVENT_KEY}`,\n  SHOW          : `show${EVENT_KEY}`,\n  SHOWN         : `shown${EVENT_KEY}`\n}\n\nconst ClassName = {\n  FADE    : 'fade',\n  HIDE    : 'hide',\n  SHOW    : 'show',\n  SHOWING : 'showing'\n}\n\nconst DefaultType = {\n  animation : 'boolean',\n  autohide  : 'boolean',\n  delay     : 'number'\n}\n\nconst Default = {\n  animation : true,\n  autohide  : true,\n  delay     : 500\n}\n\nconst Selector = {\n  DATA_DISMISS : '[data-dismiss=\"toast\"]'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Toast {\n  constructor(element, config) {\n    this._element = element\n    this._config  = this._getConfig(config)\n    this._timeout = null\n    this._setListeners()\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  // Public\n\n  show() {\n    const showEvent = $.Event(Event.SHOW)\n\n    $(this._element).trigger(showEvent)\n    if (showEvent.isDefaultPrevented()) {\n      return\n    }\n\n    if (this._config.animation) {\n      this._element.classList.add(ClassName.FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(ClassName.SHOWING)\n      this._element.classList.add(ClassName.SHOW)\n\n      $(this._element).trigger(Event.SHOWN)\n\n      if (this._config.autohide) {\n        this._timeout = setTimeout(() => {\n          this.hide()\n        }, this._config.delay)\n      }\n    }\n\n    this._element.classList.remove(ClassName.HIDE)\n    Util.reflow(this._element)\n    this._element.classList.add(ClassName.SHOWING)\n    if (this._config.animation) {\n      const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  hide() {\n    if (!this._element.classList.contains(ClassName.SHOW)) {\n      return\n    }\n\n    const hideEvent = $.Event(Event.HIDE)\n\n    $(this._element).trigger(hideEvent)\n    if (hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    this._close()\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n\n    if (this._element.classList.contains(ClassName.SHOW)) {\n      this._element.classList.remove(ClassName.SHOW)\n    }\n\n    $(this._element).off(Event.CLICK_DISMISS)\n\n    $.removeData(this._element, DATA_KEY)\n    this._element = null\n    this._config  = null\n  }\n\n  // Private\n\n  _getConfig(config) {\n    config = {\n      ...Default,\n      ...$(this._element).data(),\n      ...typeof config === 'object' && config ? config : {}\n    }\n\n    Util.typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _setListeners() {\n    $(this._element).on(\n      Event.CLICK_DISMISS,\n      Selector.DATA_DISMISS,\n      () => this.hide()\n    )\n  }\n\n  _close() {\n    const complete = () => {\n      this._element.classList.add(ClassName.HIDE)\n      $(this._element).trigger(Event.HIDDEN)\n    }\n\n    this._element.classList.remove(ClassName.SHOW)\n    if (this._config.animation) {\n      const transitionDuration = Util.getTransitionDurationFromElement(this._element)\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(transitionDuration)\n    } else {\n      complete()\n    }\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      const $element = $(this)\n      let data       = $element.data(DATA_KEY)\n      const _config  = typeof config === 'object' && config\n\n      if (!data) {\n        data = new Toast(this, _config)\n        $element.data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME]             = Toast._jQueryInterface\n$.fn[NAME].Constructor = Toast\n$.fn[NAME].noConflict  = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Toast._jQueryInterface\n}\n\nexport default Toast\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "Event", "CLICK_DISMISS", "HIDE", "HIDDEN", "SHOW", "SHOWN", "ClassName", "FADE", "SHOWING", "DefaultType", "animation", "autohide", "delay", "<PERSON><PERSON><PERSON>", "Selector", "DATA_DISMISS", "Toast", "element", "config", "_element", "_config", "_getConfig", "_timeout", "_setListeners", "show", "showEvent", "trigger", "isDefaultPrevented", "classList", "add", "complete", "remove", "setTimeout", "hide", "<PERSON><PERSON>", "reflow", "transitionDuration", "getTransitionDurationFromElement", "one", "TRANSITION_END", "emulateTransitionEnd", "contains", "hideEvent", "_close", "dispose", "clearTimeout", "off", "removeData", "data", "typeCheckConfig", "constructor", "on", "_jQueryInterface", "each", "$element", "TypeError", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAUA;;;;;;EAMA,IAAMA,IAAI,GAAiB,OAA3B;EACA,IAAMC,OAAO,GAAc,OAA3B;EACA,IAAMC,QAAQ,GAAa,UAA3B;EACA,IAAMC,SAAS,SAAgBD,QAA/B;EACA,IAAME,kBAAkB,GAAGC,CAAC,CAACC,EAAF,CAAKN,IAAL,CAA3B;EAEA,IAAMO,KAAK,GAAG;EACZC,EAAAA,aAAa,oBAAmBL,SADpB;EAEZM,EAAAA,IAAI,WAAmBN,SAFX;EAGZO,EAAAA,MAAM,aAAmBP,SAHb;EAIZQ,EAAAA,IAAI,WAAmBR,SAJX;EAKZS,EAAAA,KAAK,YAAmBT;EALZ,CAAd;EAQA,IAAMU,SAAS,GAAG;EAChBC,EAAAA,IAAI,EAAM,MADM;EAEhBL,EAAAA,IAAI,EAAM,MAFM;EAGhBE,EAAAA,IAAI,EAAM,MAHM;EAIhBI,EAAAA,OAAO,EAAG;EAJM,CAAlB;EAOA,IAAMC,WAAW,GAAG;EAClBC,EAAAA,SAAS,EAAG,SADM;EAElBC,EAAAA,QAAQ,EAAI,SAFM;EAGlBC,EAAAA,KAAK,EAAO;EAHM,CAApB;EAMA,IAAMC,OAAO,GAAG;EACdH,EAAAA,SAAS,EAAG,IADE;EAEdC,EAAAA,QAAQ,EAAI,IAFE;EAGdC,EAAAA,KAAK,EAAO;EAHE,CAAhB;EAMA,IAAME,QAAQ,GAAG;EACfC,EAAAA,YAAY,EAAG;EADA,CAAjB;EAIA;;;;;;MAMMC;;;EACJ,iBAAYC,OAAZ,EAAqBC,MAArB,EAA6B;EAC3B,SAAKC,QAAL,GAAgBF,OAAhB;EACA,SAAKG,OAAL,GAAgB,KAAKC,UAAL,CAAgBH,MAAhB,CAAhB;EACA,SAAKI,QAAL,GAAgB,IAAhB;;EACA,SAAKC,aAAL;EACD;;;;;EAgBD;WAEAC,OAAA,gBAAO;EAAA;;EACL,QAAMC,SAAS,GAAG3B,CAAC,CAACE,KAAF,CAAQA,KAAK,CAACI,IAAd,CAAlB;EAEAN,IAAAA,CAAC,CAAC,KAAKqB,QAAN,CAAD,CAAiBO,OAAjB,CAAyBD,SAAzB;;EACA,QAAIA,SAAS,CAACE,kBAAV,EAAJ,EAAoC;EAClC;EACD;;EAED,QAAI,KAAKP,OAAL,CAAaV,SAAjB,EAA4B;EAC1B,WAAKS,QAAL,CAAcS,SAAd,CAAwBC,GAAxB,CAA4BvB,SAAS,CAACC,IAAtC;EACD;;EAED,QAAMuB,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,MAAA,KAAI,CAACX,QAAL,CAAcS,SAAd,CAAwBG,MAAxB,CAA+BzB,SAAS,CAACE,OAAzC;;EACA,MAAA,KAAI,CAACW,QAAL,CAAcS,SAAd,CAAwBC,GAAxB,CAA4BvB,SAAS,CAACF,IAAtC;;EAEAN,MAAAA,CAAC,CAAC,KAAI,CAACqB,QAAN,CAAD,CAAiBO,OAAjB,CAAyB1B,KAAK,CAACK,KAA/B;;EAEA,UAAI,KAAI,CAACe,OAAL,CAAaT,QAAjB,EAA2B;EACzB,QAAA,KAAI,CAACW,QAAL,GAAgBU,UAAU,CAAC,YAAM;EAC/B,UAAA,KAAI,CAACC,IAAL;EACD,SAFyB,EAEvB,KAAI,CAACb,OAAL,CAAaR,KAFU,CAA1B;EAGD;EACF,KAXD;;EAaA,SAAKO,QAAL,CAAcS,SAAd,CAAwBG,MAAxB,CAA+BzB,SAAS,CAACJ,IAAzC;;EACAgC,IAAAA,IAAI,CAACC,MAAL,CAAY,KAAKhB,QAAjB;;EACA,SAAKA,QAAL,CAAcS,SAAd,CAAwBC,GAAxB,CAA4BvB,SAAS,CAACE,OAAtC;;EACA,QAAI,KAAKY,OAAL,CAAaV,SAAjB,EAA4B;EAC1B,UAAM0B,kBAAkB,GAAGF,IAAI,CAACG,gCAAL,CAAsC,KAAKlB,QAA3C,CAA3B;EAEArB,MAAAA,CAAC,CAAC,KAAKqB,QAAN,CAAD,CACGmB,GADH,CACOJ,IAAI,CAACK,cADZ,EAC4BT,QAD5B,EAEGU,oBAFH,CAEwBJ,kBAFxB;EAGD,KAND,MAMO;EACLN,MAAAA,QAAQ;EACT;EACF;;WAEDG,OAAA,gBAAO;EACL,QAAI,CAAC,KAAKd,QAAL,CAAcS,SAAd,CAAwBa,QAAxB,CAAiCnC,SAAS,CAACF,IAA3C,CAAL,EAAuD;EACrD;EACD;;EAED,QAAMsC,SAAS,GAAG5C,CAAC,CAACE,KAAF,CAAQA,KAAK,CAACE,IAAd,CAAlB;EAEAJ,IAAAA,CAAC,CAAC,KAAKqB,QAAN,CAAD,CAAiBO,OAAjB,CAAyBgB,SAAzB;;EACA,QAAIA,SAAS,CAACf,kBAAV,EAAJ,EAAoC;EAClC;EACD;;EAED,SAAKgB,MAAL;EACD;;WAEDC,UAAA,mBAAU;EACRC,IAAAA,YAAY,CAAC,KAAKvB,QAAN,CAAZ;EACA,SAAKA,QAAL,GAAgB,IAAhB;;EAEA,QAAI,KAAKH,QAAL,CAAcS,SAAd,CAAwBa,QAAxB,CAAiCnC,SAAS,CAACF,IAA3C,CAAJ,EAAsD;EACpD,WAAKe,QAAL,CAAcS,SAAd,CAAwBG,MAAxB,CAA+BzB,SAAS,CAACF,IAAzC;EACD;;EAEDN,IAAAA,CAAC,CAAC,KAAKqB,QAAN,CAAD,CAAiB2B,GAAjB,CAAqB9C,KAAK,CAACC,aAA3B;EAEAH,IAAAA,CAAC,CAACiD,UAAF,CAAa,KAAK5B,QAAlB,EAA4BxB,QAA5B;EACA,SAAKwB,QAAL,GAAgB,IAAhB;EACA,SAAKC,OAAL,GAAgB,IAAhB;EACD;;;WAIDC,aAAA,oBAAWH,MAAX,EAAmB;EACjBA,IAAAA,MAAM,sBACDL,OADC,MAEDf,CAAC,CAAC,KAAKqB,QAAN,CAAD,CAAiB6B,IAAjB,EAFC,MAGD,OAAO9B,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAH/C,CAAN;EAMAgB,IAAAA,IAAI,CAACe,eAAL,CACExD,IADF,EAEEyB,MAFF,EAGE,KAAKgC,WAAL,CAAiBzC,WAHnB;EAMA,WAAOS,MAAP;EACD;;WAEDK,gBAAA,yBAAgB;EAAA;;EACdzB,IAAAA,CAAC,CAAC,KAAKqB,QAAN,CAAD,CAAiBgC,EAAjB,CACEnD,KAAK,CAACC,aADR,EAEEa,QAAQ,CAACC,YAFX,EAGE;EAAA,aAAM,MAAI,CAACkB,IAAL,EAAN;EAAA,KAHF;EAKD;;WAEDU,SAAA,kBAAS;EAAA;;EACP,QAAMb,QAAQ,GAAG,SAAXA,QAAW,GAAM;EACrB,MAAA,MAAI,CAACX,QAAL,CAAcS,SAAd,CAAwBC,GAAxB,CAA4BvB,SAAS,CAACJ,IAAtC;;EACAJ,MAAAA,CAAC,CAAC,MAAI,CAACqB,QAAN,CAAD,CAAiBO,OAAjB,CAAyB1B,KAAK,CAACG,MAA/B;EACD,KAHD;;EAKA,SAAKgB,QAAL,CAAcS,SAAd,CAAwBG,MAAxB,CAA+BzB,SAAS,CAACF,IAAzC;;EACA,QAAI,KAAKgB,OAAL,CAAaV,SAAjB,EAA4B;EAC1B,UAAM0B,kBAAkB,GAAGF,IAAI,CAACG,gCAAL,CAAsC,KAAKlB,QAA3C,CAA3B;EAEArB,MAAAA,CAAC,CAAC,KAAKqB,QAAN,CAAD,CACGmB,GADH,CACOJ,IAAI,CAACK,cADZ,EAC4BT,QAD5B,EAEGU,oBAFH,CAEwBJ,kBAFxB;EAGD,KAND,MAMO;EACLN,MAAAA,QAAQ;EACT;EACF;;;UAIMsB,mBAAP,0BAAwBlC,MAAxB,EAAgC;EAC9B,WAAO,KAAKmC,IAAL,CAAU,YAAY;EAC3B,UAAMC,QAAQ,GAAGxD,CAAC,CAAC,IAAD,CAAlB;EACA,UAAIkD,IAAI,GAASM,QAAQ,CAACN,IAAT,CAAcrD,QAAd,CAAjB;;EACA,UAAMyB,OAAO,GAAI,OAAOF,MAAP,KAAkB,QAAlB,IAA8BA,MAA/C;;EAEA,UAAI,CAAC8B,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIhC,KAAJ,CAAU,IAAV,EAAgBI,OAAhB,CAAP;EACAkC,QAAAA,QAAQ,CAACN,IAAT,CAAcrD,QAAd,EAAwBqD,IAAxB;EACD;;EAED,UAAI,OAAO9B,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAO8B,IAAI,CAAC9B,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIqC,SAAJ,wBAAkCrC,MAAlC,QAAN;EACD;;EAED8B,QAAAA,IAAI,CAAC9B,MAAD,CAAJ,CAAa,IAAb;EACD;EACF,KAjBM,CAAP;EAkBD;;;;0BApJoB;EACnB,aAAOxB,OAAP;EACD;;;0BAEwB;EACvB,aAAOe,WAAP;EACD;;;0BAEoB;EACnB,aAAOI,OAAP;EACD;;;;;EA6IH;;;;;;;EAMAf,CAAC,CAACC,EAAF,CAAKN,IAAL,IAAyBuB,KAAK,CAACoC,gBAA/B;EACAtD,CAAC,CAACC,EAAF,CAAKN,IAAL,EAAW+D,WAAX,GAAyBxC,KAAzB;;EACAlB,CAAC,CAACC,EAAF,CAAKN,IAAL,EAAWgE,UAAX,GAAyB,YAAM;EAC7B3D,EAAAA,CAAC,CAACC,EAAF,CAAKN,IAAL,IAAaI,kBAAb;EACA,SAAOmB,KAAK,CAACoC,gBAAb;EACD,CAHD;;;;;;;;"}