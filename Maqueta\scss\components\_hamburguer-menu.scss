$hamburger-button-size     : 40px;
$hamburger-button-radius   : 4px;
$hamburger-bar-color       : $fun-blue;
$hamgurger-bar-width       : 30px;
$hamgurger-bar-height      : 3px;
$hamgurger-bar-spacing     : 1px;
$hamgurger-transition-speed: 0.4s;

.bt-menu {
  margin: auto;
  border: none;
  padding: 0;
  background: none;
  cursor: pointer;
  border-radius: $hamburger-button-radius;
  width: $hamburger-button-size;
  height: $hamburger-button-size;
  transition: $hamgurger-transition-speed / 2;
  z-index: 11;

  &:active, &:focus {
    outline: none;
  }

  .hamburguer {
    width: $hamgurger-bar-width;
    height: ($hamgurger-bar-height * 3) + ($hamgurger-bar-spacing * 2);
    transition: $hamgurger-transition-speed;

    &, .bar {
      display: block;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      margin: auto;
    }

    .bar {
      width: $hamgurger-bar-width;
      height: $hamgurger-bar-height;
      background: $hamburger-bar-color;
      transition: $hamgurger-transition-speed / 2;
      outline: 1px solid transparent;
      border-radius: $border-radius;
    }

    .bar-1 {
      transform: rotate(0) translate3d(0, -($hamgurger-bar-height * 2), 0);
      transform-origin: center;
    }

    .bar-2 {
        width: ($hamgurger-bar-width - 10px);
      }
    
    .bar-3 {
      transform: rotate(0) translate3d(0, ($hamgurger-bar-height * 2), 0);
      transform-origin: center;
    }

    .menu-open & {
      transform: rotate(90deg);

      .bar-1 { 
          transform: rotate(45deg) translate3d(0, 4px, 0);
          transform-origin: center;
        }
      .bar-2 { opacity: 0; }
      .bar-3 { 
          transform: rotate(-45deg) translate3d(0, -4px, 0);
          transform-origin: center;
        }
    }
  }
}