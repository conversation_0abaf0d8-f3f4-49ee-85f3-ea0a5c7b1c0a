import React, { useState, useEffect } from 'react';

interface ModalVisualizadorPDFProps {
  mostrar: boolean;
  cerrar: () => void;
  base64Data: string;
  nombreArchivo: string;
}

const ModalVisualizadorPDF: React.FC<ModalVisualizadorPDFProps> = ({ mostrar, cerrar, base64Data, nombreArchivo }) => {
  const [pdfUrl, setPdfUrl] = useState<string>('');
  const [cargando, setCargando] = useState<boolean>(true);
  const [error, setError] = useState<boolean>(false);
  const [tipoArchivo, setTipoArchivo] = useState<string>('pdf');

  useEffect(() => {
    if (base64Data) {
      setCargando(true);
      setError(false);
      
      // Determinar el tipo MIME basado en la extensión del archivo
      let mimeType = 'application/pdf';
      let tipo = 'pdf';
      
      if (nombreArchivo) {
        const extension = nombreArchivo.split('.').pop()?.toLowerCase();
        switch (extension) {
          case 'pdf': 
            mimeType = 'application/pdf'; 
            tipo = 'pdf';
            break;
          case 'jpg': case 'jpeg': 
            mimeType = 'image/jpeg'; 
            tipo = 'imagen';
            break;
          case 'png': 
            mimeType = 'image/png'; 
            tipo = 'imagen';
            break;
          case 'docx': 
            mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'; 
            tipo = 'office';
            break;
          case 'xlsx': 
            mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'; 
            tipo = 'office';
            break;
          default: 
            mimeType = 'application/pdf';
            tipo = 'pdf';
        }
      }
      
      setTipoArchivo(tipo);
      
      try {
        // Si es un archivo de Office, descargarlo automáticamente
        if (tipo === 'office') {
          descargarArchivo();
          setCargando(false);
          cerrar(); // Cerrar el modal después de iniciar la descarga
          return;
        }
        
        // Crear URL del objeto para el visor
        const blob = base64ToBlob(base64Data, mimeType);
        
        // Crear un objeto File con el nombre correcto
        const file = new File([blob], nombreArchivo, { type: mimeType });
        
        // Crear URL para el archivo
        const url = URL.createObjectURL(file);
        setPdfUrl(url);
        setCargando(false);
      } catch (error) {
        console.error("Error al procesar el documento:", error);
        setCargando(false);
        setError(true);
      }
      
      // Limpiar URL al desmontar
      return () => {
        if (pdfUrl) URL.revokeObjectURL(pdfUrl);
      };
    }
  }, [base64Data, nombreArchivo]);

  // Función para convertir base64 a Blob
  const base64ToBlob = (base64: string, mimeType: string): Blob => {
    const byteCharacters = atob(base64);
    const byteArrays = [];
    
    for (let offset = 0; offset < byteCharacters.length; offset += 512) {
      const slice = byteCharacters.slice(offset, offset + 512);
      
      const byteNumbers = new Array(slice.length);
      for (let i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }
      
      const byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }
    
    return new Blob(byteArrays, { type: mimeType });
  };

  // Función para descargar el archivo con el nombre correcto
  const descargarArchivo = () => {
    if (base64Data) {
      // Determinar el tipo MIME
      let mimeType = 'application/pdf';
      const extension = nombreArchivo.split('.').pop()?.toLowerCase();
      switch (extension) {
        case 'pdf': mimeType = 'application/pdf'; break;
        case 'jpg': case 'jpeg': mimeType = 'image/jpeg'; break;
        case 'png': mimeType = 'image/png'; break;
        case 'docx': mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'; break;
        case 'xlsx': mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'; break;
        default: mimeType = 'application/pdf';
      }
      
      // Crear el blob y forzar la descarga
      const blob = base64ToBlob(base64Data, mimeType);
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = nombreArchivo;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  if (!mostrar) return null;

  // Crear un documento HTML personalizado con el PDF incrustado y el nombre correcto
  const createPdfViewer = () => {
    if (!pdfUrl) return null;
    
    // Crear un iframe con un documento HTML personalizado
    const iframeContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>${nombreArchivo}</title>
        <style>
          body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
          }
          #pdf-container {
            width: 100%;
            height: 100%;
          }
          embed {
            width: 100%;
            height: 100%;
          }
        </style>
      </head>
      <body>
        <div id="pdf-container">
          <embed src="${pdfUrl}#toolbar=1&navpanes=1&scrollbar=1&view=FitH" type="application/pdf" width="100%" height="100%" />
        </div>
        <script>
          // Establecer el nombre del archivo para la descarga
          document.title = "${nombreArchivo}";
          
          // Intentar modificar el nombre del archivo en la descarga
          const embedElement = document.querySelector('embed');
          if (embedElement) {
            embedElement.setAttribute('name', "${nombreArchivo}");
          }
        </script>
      </body>
      </html>
    `;
    
    // Crear un blob con el contenido HTML
    const htmlBlob = new Blob([iframeContent], { type: 'text/html' });
    const htmlUrl = URL.createObjectURL(htmlBlob);
    
    return (
      <iframe 
        src={htmlUrl} 
        style={{ width: '100%', height: '100%', border: 'none' }}
        title={nombreArchivo}
      />
    );
  };

  // Crear un visor de imágenes
  const createImageViewer = () => {
    if (!pdfUrl) return null;
    
    return (
      <div style={{ width: '100%', height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', backgroundColor: '#f5f5f5' }}>
        <img 
          src={pdfUrl} 
          alt={nombreArchivo} 
          style={{ maxWidth: '100%', maxHeight: '100%', objectFit: 'contain' }} 
        />
      </div>
    );
  };

  return (
    <div className="modal fade show" style={{ display: 'block' }} tabIndex={-1} role="dialog">
      <div className="modal-dialog modal-xl" style={{ maxWidth: '95%', margin: '10px auto' }} role="document">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">{nombreArchivo}</h5>
            <button type="button" className="close" onClick={cerrar}>
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div className="modal-body" style={{ height: '85vh' }}>
            {cargando ? (
              <div className="d-flex justify-content-center align-items-center" style={{ height: '100%' }}>
                <div className="spinner-border text-primary" role="status" style={{ width: '3rem', height: '3rem' }}>
                  <span className="sr-only">Cargando...</span>
                </div>
              </div>
            ) : error ? (
              <div className="text-center">
                <p className="text-danger font-weight-bold" style={{ fontSize: '1.2rem' }}>Error al cargar el archivo</p>
              </div>
            ) : pdfUrl ? (
              tipoArchivo === 'pdf' ? createPdfViewer() : 
              tipoArchivo === 'imagen' ? createImageViewer() : 
              <div className="text-center">
                <p>Descargando archivo...</p>
              </div>
            ) : (
              <div className="text-center">
                <p className="text-danger">No se pudo cargar el documento.</p>
              </div>
            )}
          </div>
          <div className="modal-footer">
            <button type="button" className="btn btn-primary" onClick={descargarArchivo}>Descargar</button>
            <button type="button" className="btn btn-secondary" onClick={cerrar}>Cerrar</button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModalVisualizadorPDF;
