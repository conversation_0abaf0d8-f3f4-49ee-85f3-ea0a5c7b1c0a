export const getSitioPrivilege = () => {
    if (_spPageContextInfo.webAbsoluteUrl.includes("dev-intranet")) {
        return "https://dev-intranet/sitios/Privilege";
    } else {
        return "https://intranet/sitios/Privilege";
    }
}

export const insertarEnListaSharePoint = async (element, data) => {
    try {
        let clientContext = new SP.ClientContext(getSitioPrivilege());
        let oList = clientContext.get_web().get_lists().getByTitle('bpmcabecera');
        let itemCreateInfo = new SP.ListItemCreationInformation();
        let oListItem = oList.addItem(itemCreateInfo);
        oListItem.set_item('Title', element.codigoOperacion);
        oListItem.set_item('Producto', '7');
        oListItem.set_item('SubProducto', data.codigoSubProducto);
        oListItem.set_item('Cliente', data.codigoCliente);
        oListItem.set_item('Aplica', '0');
        oListItem.set_item('Estado', '1');
        oListItem.update();
        clientContext.load(oListItem);
        return await new Promise((resolve, reject) => {
            clientContext.executeQueryAsync(
                function () {
                    resolve(oListItem.get_id());
                },
                function (sender, args) {
                    reject(new Error(args.get_message()));
                }
            );
        });
    } catch (error) {
        console.error('Error al insertar en SharePoint:', error);
        throw error;
    }
}