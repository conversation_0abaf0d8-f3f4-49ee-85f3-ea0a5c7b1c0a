/// <reference types="webpack/module" />
import _ from 'lodash'
import React from "react";  
import { ContainerRender } from '../Container';
import { PanelListado } from '../listado/crearDivisiones';
import { FacturaInicioDeSeguimiento } from '../seguimiento_de_facturas/FacturaInicioDeSeguimiento';
import { DatosProvider } from '../contextProvider/datosProveedorContext';
import InicioDeSeguimientoFormWrapper from '../Privilege/InicioDeSeguimientoFormWrapper';
import BtnDelegacionDeTareas from './BtnDelegacionDeTareas';
import AdicionalesPrivilegeWrapper from '../Privilege/AdicionalesPrivilegeWrapper';

'use strict'

export class MenuElementoComp
  extends React.Component<{
    codigo: string,
    nombre: string,
    descripcion: string,
    tieneHijo: boolean,
    tienePadre: boolean,
    productoNombre: string
    idNumber: string
  }, any> {
  productoNombreId; hrefStart: string

  constructor(props: any) {
    super(props)
    this.productoNombreId = this.props.productoNombre + this.props.idNumber
    this.hrefStart = '#'
  }

  hijoRender() {

    let _cursorcss = { 'cursor': 'pointer' }

    let _codigo = this.props.codigo

    return (<a style={_cursorcss} className="list-group-item list-group-item-action bg-dark text-white"
      onClick={(e) => this.switchModulo(e, { 'codigoMenu': _codigo })}>
      <span className="menu-collapsed">{this.props.nombre}</span>
    </a>
    )
  }

  switchModulo(e: any, props: any) {
    let _component;
    
    const currentComponent = document.querySelector('#main-container')?.getAttribute('data-current-component');
    
    if (props.codigoMenu === 'BANSDF') {
      if ((window as any).limpiarBusquedaYVolverBandeja) {
        (window as any).limpiarBusquedaYVolverBandeja();
      }
      
      if (currentComponent === 'BANSDF') {
        return;
      }
    }
    
    switch (props.codigoMenu) {
      case 'BANSDF':
        _component = <DatosProvider><PanelListado /></DatosProvider>
        break;
      case 'IDSSDF':
        _component = <DatosProvider><FacturaInicioDeSeguimiento /></DatosProvider>
        break;
      case 'PRVINI':
        _component = <DatosProvider>< InicioDeSeguimientoFormWrapper /></DatosProvider>
        break;
      case 'SEGADI':
        _component = <DatosProvider>< AdicionalesPrivilegeWrapper /></DatosProvider>
        break;
      case 'DTSEGF':
        _component = <BtnDelegacionDeTareas />
        break;
      default:
        _component = <div id='hola'></div>
        break;
    }

    const container = document.querySelector('#main-container');
    if (container) {
      container.setAttribute('data-current-component', props.codigoMenu);
    }

    ContainerRender({ 'component': _component })
  }


  padreRender() {
    let dataToggleValue = '', ariaExpandedValue = null, _hrefValue = this.hrefStart, subMenuIconEl = <> </>;

    if (this.props.tieneHijo) {
      dataToggleValue = "collapse"
      ariaExpandedValue = false
      _hrefValue = _hrefValue + this.productoNombreId;
      subMenuIconEl = <span className="submenu-icon ml-auto"></span>;
    }

    let el = <a href={_hrefValue} data-toggle={dataToggleValue} aria-expanded={ariaExpandedValue}
      className="bg-dark list-group-item list-group-item-action flex-column align-items-start">
      <div className="d-flex w-100 justify-content-start align-items-center">
        <span className="fas fa-toolbox mr-3"></span>
        <span className="menu-collapsed">{this.props.nombre}</span>
        {subMenuIconEl}
      </div>
    </a>
    return el
  }

  render() {
    const tienePadre = this.props.tienePadre;
    let el = (tienePadre) ? this.hijoRender() : this.padreRender();
    return (<React.StrictMode>{el}</React.StrictMode>)
  }
}
