/// <reference types="webpack/module" />
import * as _s from 'lodash'
import { createRoot } from 'react-dom/client'
import { CabeceraIdentificador } from './components/CabeceraIdentificador'
import React from 'react'
import { Menu } from './components/menu/Menu'

export const init = async () => {
  const displayNameEl = createRoot(document.getElementById('displayNameNAV'))
  const menuEl = createRoot(document.getElementById('sidebar-container'))
  displayNameEl.render(<CabeceraIdentificador />)
  menuEl.render(<Menu />)
}