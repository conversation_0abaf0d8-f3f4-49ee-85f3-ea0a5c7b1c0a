import type { ReactNode } from "react";
import React, { createContext, useContext, useReducer } from "react";
import { PaginacionData } from '../../entities/PaginacionData';

const defaultState = {
  nombre: '', apellido: '', nombreDos: '', apellidoUno: '', apellidoDos: '', tipoPersona: '',
  tipoProveedor: '', pais: '', telefono: '', direccion: '', ciudad: '', numeroDocumento: '',
  timbrado: [{}], fechaCarga: '', fechaVencimiento: '', estado: '', tipoDocumento: '', criticidad: '',
  declaracionJurada: '', fechaDeclaracionJurada: '', codigoCliente: '', numeroTimbrado: '', mes: '',
  anho: '', estadoTimbrado: '', cedula: '', estadoPerfilMensaje: '', DVTipoDocumento: '',
  DVEstadoDocumento: '', responseDataObtenerDatosClienteProveedor: false, responseStatus: '',
  codigoClienteGCP: '', documentoPLD: '', fechaPLD: '', accion: '', nroD: '', tipD: '',
  responseObtenerStatusCliente: '', responseObtenerStatusProveedor: '', responseStatusPostAltaProveedor: '',
  numTres: '', responseStatusRegistroFactura: '', responseStatusModificarProveedor: '',
  operacionCliente: [{}], propsDataIdTarea: {}, numeroOperacionCliente: '', operacionClienteNumero: '',
  propsDataOperacion: [{}], itemTitulo: [{}], itemSubTitulo: [{}], itemMonto: [{}], itemFecha: [{}],
  data_datos: [{}], operacionNumero: '', datoIdTarea: '', obtenerDatosFactura: [{}], obtenerAreas: [{}],
  datosDepartamentos: [{}], selectedArea: '', autorizantes: {}, obtenerNumeroOperacion: [{}],
  statusObtenerOperaciones: '', tipoMoneda: '',
  datosMonto: [{}],
  montoTotal: '', montoCondicionVenta: '', montoCinco: '', montoDiez: '', montoGravadasCinco: '', montoGravadasDiez: '',
  montoIvaCinco: '', montoIvaDiez: '', montoDescripcion: '', montoExentas: '',
  bandejaNombre: '', bandejaPais: '', bandejaTelefono: '', bandejaNroDocumento: '', bandejaDireccion: '',
  bandejaCiudad: '', bandejaFechaEmision: '', bandejaTimbrado: '', bandejaNroFactura: '', bandejaFacturaParteUno: '',
  bandejaFacturaParteDos: '', bandejaFacturaParteTres: '', bandejaTipoMoneda: '', bandejaTipoComprobante: '',
  loteId: '', idTareaSeguimiento: [{}], datoInputSearch: '', arrayAutorizantes: [''], listaAutorizantesActualizada: [""],
  obtenerDatoDepartamentos: '', areaSeleccionada: '', departamentoSeleccionado: '', monedaSeleccionada: '',
  inicioDeSeguimientoCondicion: false, obtenerCodigoArea: '', descripcionAreaBandeja: '', descripcionDepartamentoBandeja: '',
  condicionInicioSeguimiento: false, bandejaProvision: '', fechaActual: '', inicioDeSeguimiento: true,
  respuestaEstadoPerfilMensaje: '', archivoAdjunto: '', listadoItemFacturas: [{}],
  paginacionBusqueda: null as PaginacionData | null,
};

export type Action = 'datos';
export type Dispatch = (action: Action) => void;
export type State = typeof defaultState;

const ProveedorContext = createContext<{ state: State, dispatch: Dispatch } | undefined>(undefined);

function datosProveedor(state: State, action: Action) {
  switch (action) {
    case 'datos':
      return {
        nombre: state.nombre,
        apellido: state.apellido,
        nombreDos: state.nombreDos,
        apellidoDos: state.apellidoDos,
        apellidoUno: state.apellidoUno,
        tipoPersona: state.tipoPersona,
        tipoProveedor: state.tipoProveedor,
        pais: state.pais,
        telefono: state.telefono,
        direccion: state.direccion,
        ciudad: state.ciudad,
        numeroDocumento: state.numeroDocumento,
        timbrado: state.timbrado,
        fechaCarga: state.fechaCarga,
        fechaVencimiento: state.fechaVencimiento,
        estado: state.estado,
        tipoDocumento: state.tipoDocumento,
        criticidad: state.criticidad,
        declaracionJurada: state.declaracionJurada,
        fechaDeclaracionJurada: state.fechaDeclaracionJurada,
        codigoCliente: state.codigoCliente,
        numeroTimbrado: state.numeroTimbrado,
        mes: state.mes,
        anho: state.anho,
        estadoTimbrado: state.estadoTimbrado,
        cedula: state.cedula,
        estadoPerfilMensaje: state.estadoPerfilMensaje,
        DVTipoDocumento: state.DVTipoDocumento,
        DVEstadoDocumento: state.DVEstadoDocumento,
        responseDataObtenerDatosClienteProveedor: state.responseDataObtenerDatosClienteProveedor,
        responseStatus: state.responseStatus,
        codigoClienteGCP: state.codigoClienteGCP,
        documentoPLD: state.documentoPLD,
        fechaPLD: state.fechaPLD,
        accion: state.accion,
        nroD: state.nroD,
        tipD: state.tipD,
        responseObtenerStatusCliente: state.responseObtenerStatusCliente,
        responseObtenerStatusProveedor: state.responseObtenerStatusProveedor,
        responseStatusPostAltaProveedor: state.responseStatusPostAltaProveedor,
        numTres: state.numTres,
        responseStatusRegistroFactura: state.responseStatusRegistroFactura,
        responseStatusModificarProveedor: state.responseStatusModificarProveedor,
        operacionCliente: state.operacionCliente,
        propsDataIdTarea: state.propsDataIdTarea,
        numeroOperacionCliente: state.numeroOperacionCliente,

        operacionClienteNumero: state.operacionClienteNumero,

        propsDataOperacion: state.propsDataOperacion,
        itemTitulo: state.itemTitulo,
        itemSubTitulo: state.itemSubTitulo,
        itemMonto: state.itemMonto,
        itemFecha: state.itemFecha,
        data_datos: state.data_datos,
        operacionNumero: state.operacionNumero,
        datoIdTarea: state.datoIdTarea,
        obtenerDatosFactura: state.obtenerDatosFactura,
        obtenerAreas: state.obtenerAreas,
        datosDepartamentos: state.datosDepartamentos,
        selectedArea: state.selectedArea,
        autorizantes: state.autorizantes,
        obtenerNumeroOperacion: state.obtenerNumeroOperacion,
        statusObtenerOperaciones: state.statusObtenerOperaciones,
        tipoMoneda: state.tipoMoneda,

        datosMonto: state.datosMonto,
        montoTotal: state.montoTotal,
        montoCondicionVenta: state.montoCondicionVenta,
        montoCinco: state.montoCinco,
        montoDiez: state.montoDiez,
        montoGravadasCinco: state.montoGravadasCinco,
        montoGravadasDiez: state.montoGravadasDiez,
        montoIvaCinco: state.montoIvaCinco,
        montoIvaDiez: state.montoIvaDiez,
        montoDescripcion: state.montoDescripcion,
        montoExentas: state.montoExentas,

        bandejaNombre: state.bandejaNombre,
        bandejaPais: state.bandejaPais,
        bandejaTelefono: state.bandejaTelefono,
        bandejaNroDocumento: state.bandejaNroDocumento,
        bandejaDireccion: state.bandejaDireccion,
        bandejaCiudad: state.bandejaCiudad,
        bandejaFechaEmision: state.bandejaFechaEmision,
        bandejaTimbrado: state.bandejaTimbrado,
        bandejaNroFactura: state.bandejaNroFactura,
        bandejaFacturaParteUno: state.bandejaFacturaParteUno,
        bandejaFacturaParteDos: state.bandejaFacturaParteDos,
        bandejaFacturaParteTres: state.bandejaFacturaParteTres,
        bandejaTipoMoneda: state.bandejaTipoMoneda,
        bandejaTipoComprobante: state.bandejaTipoComprobante,

        loteId: state.loteId,

        idTareaSeguimiento: state.idTareaSeguimiento,

        datoInputSearch: state.datoInputSearch,

        arrayAutorizantes: state.arrayAutorizantes,

        listaAutorizantesActualizada: state.listaAutorizantesActualizada,

        obtenerDatoDepartamentos: state.obtenerDatoDepartamentos,
        areaSeleccionada: state.areaSeleccionada,
        departamentoSeleccionado: state.departamentoSeleccionado,

        monedaSeleccionada: state.monedaSeleccionada,

        inicioDeSeguimientoCondicion: state.inicioDeSeguimientoCondicion,

        obtenerCodigoArea: state.obtenerCodigoArea,

        descripcionAreaBandeja: state.descripcionAreaBandeja,
        descripcionDepartamentoBandeja: state.descripcionDepartamentoBandeja,

        condicionInicioSeguimiento: state.condicionInicioSeguimiento,

        bandejaProvision: state.bandejaProvision,

        fechaActual: state.fechaActual,

        inicioDeSeguimiento: state.inicioDeSeguimiento,

        respuestaEstadoPerfilMensaje: state.respuestaEstadoPerfilMensaje,

        archivoAdjunto: state.archivoAdjunto,
        paginacionBusqueda: state.paginacionBusqueda,
        listadoItemFacturas: state.listadoItemFacturas
      }
  }
}

export function DatosProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(datosProveedor, defaultState);

  return (
    <ProveedorContext.Provider value={{ state, dispatch }}>
      {children}
    </ProveedorContext.Provider>
  )
}

export function useProveedor() {
  const context = useContext(ProveedorContext);
  if (!context) throw new Error("useProveedor debe usarse en DatosProvider.");
  return context;
}
