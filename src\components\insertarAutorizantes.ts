import { spCrearItem } from "../comm/apiSharePoint";
import { getUrlSPBase } from "../utilities/contextInfo";
import { SharepointListData } from "../entities/SharepointListData";

export const insertarAutorizantes = async (operacionSeguimiento: string, autorizantes: []): Promise<void> => {
  console.log("ENTRE A INSERTAR AUTORIZANTES.");

  try {

    for (const autorizante of autorizantes) {
      console.log("autorizante:", autorizante);
      console.log("autorizantes:", autorizantes);

      let datos: SharepointListData[] = [
        {
          columna: 'Title',
          valor: JSON.parse(operacionSeguimiento).toString()
        },
        {
          columna: 'Usuario',
          valor: JSON.parse(autorizante).toString()
        }
      ];

      const onSuccess = () => { console.log("INSERTADO EXITOSAMENTE!!!") }
      const onFail = () => { console.log("NO SE PUDO INSERTAR!!!") }
      console.log("datos insertar autorizantes:", datos);
      await spCrearItem(`${getUrlSPBase()}` + 'Sistemas/SeguimientoFacturas', 'AutorizantesSeguimiento', datos, onSuccess, onFail);
    }
  } catch (error) {
    console.log("Error insertarAutorizantes:", error);
    throw error;
  }
}