import { Ambiente } from "../../entities/Enums/enumAmbientes"

export class TokenInternoRequestHeader {
  constructor(_ambiente: number) {
    switch (_ambiente) {
      case Ambiente.dev:
        this['Subscription-key'] = '2d489b65ea374662b3c6c6929dd62f9a'
        this['Grant-Type'] = 'client_credentials'
        this['Client-Id'] = 'sharepoint'
        this['Client-Secret'] = 'ad97a622-c001-4bc4-830d-c6490c2cb9e7'
        this['Scope'] = 'profile'
        this['Accept'] = 'application/json'
        break;

      case Ambiente.qa:
        this['Subscription-key'] = 'a1d6ebd894334156a5bf2ed7f38bb599'
        this['Grant-Type'] = 'client_credentials'
        this['Client-Id'] = 'sharepoint'
        this['Client-Secret'] = 'ad97a622-c001-4bc4-830d-c6490c2cb9e7'
        this['Scope'] = 'profile'
        this['Accept'] = 'application/json'
        break;

      case Ambiente.prod:
        this['Subscription-key'] = '2966f3b11c15480cb45c4aacb2f8965e'
        this['Grant-Type'] = 'client_credentials'
        this['Client-Id'] = 'sharepoint'
        this['Client-Secret'] = 'ad97a622-c001-4bc4-830d-c6490c2cb9e7'
        this['Scope'] = 'profile'
        this['Accept'] = 'application/json'
        break;
    }
  }
  'Subscription-key': string
  'Grant-Type': string
  'Client-Id': string
  'Client-Secret': string
  'Scope': string
  'Accept': string
}