{"version": 3, "file": "dropdown.js", "sources": ["../src/dropdown.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.4.1): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport $ from 'jquery'\nimport Popper from 'popper.js'\nimport Util from './util'\n\n/**\n * ------------------------------------------------------------------------\n * Constants\n * ------------------------------------------------------------------------\n */\n\nconst NAME                     = 'dropdown'\nconst VERSION                  = '4.4.1'\nconst DATA_KEY                 = 'bs.dropdown'\nconst EVENT_KEY                = `.${DATA_KEY}`\nconst DATA_API_KEY             = '.data-api'\nconst JQUERY_NO_CONFLICT       = $.fn[NAME]\nconst ESCAPE_KEYCODE           = 27 // KeyboardEvent.which value for Escape (Esc) key\nconst SPACE_KEYCODE            = 32 // KeyboardEvent.which value for space key\nconst TAB_KEYCODE              = 9 // KeyboardEvent.which value for tab key\nconst ARROW_UP_KEYCODE         = 38 // KeyboardEvent.which value for up arrow key\nconst ARROW_DOWN_KEYCODE       = 40 // KeyboardEvent.which value for down arrow key\nconst RIGHT_MOUSE_BUTTON_WHICH = 3 // MouseEvent.which value for the right button (assuming a right-handed mouse)\nconst REGEXP_KEYDOWN           = new RegExp(`${ARROW_UP_KEYCODE}|${ARROW_DOWN_KEYCODE}|${ESCAPE_KEYCODE}`)\n\nconst Event = {\n  HIDE             : `hide${EVENT_KEY}`,\n  HIDDEN           : `hidden${EVENT_KEY}`,\n  SHOW             : `show${EVENT_KEY}`,\n  SHOWN            : `shown${EVENT_KEY}`,\n  CLICK            : `click${EVENT_KEY}`,\n  CLICK_DATA_API   : `click${EVENT_KEY}${DATA_API_KEY}`,\n  KEYDOWN_DATA_API : `keydown${EVENT_KEY}${DATA_API_KEY}`,\n  KEYUP_DATA_API   : `keyup${EVENT_KEY}${DATA_API_KEY}`\n}\n\nconst ClassName = {\n  DISABLED        : 'disabled',\n  SHOW            : 'show',\n  DROPUP          : 'dropup',\n  DROPRIGHT       : 'dropright',\n  DROPLEFT        : 'dropleft',\n  MENURIGHT       : 'dropdown-menu-right',\n  MENULEFT        : 'dropdown-menu-left',\n  POSITION_STATIC : 'position-static'\n}\n\nconst Selector = {\n  DATA_TOGGLE   : '[data-toggle=\"dropdown\"]',\n  FORM_CHILD    : '.dropdown form',\n  MENU          : '.dropdown-menu',\n  NAVBAR_NAV    : '.navbar-nav',\n  VISIBLE_ITEMS : '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n}\n\nconst AttachmentMap = {\n  TOP       : 'top-start',\n  TOPEND    : 'top-end',\n  BOTTOM    : 'bottom-start',\n  BOTTOMEND : 'bottom-end',\n  RIGHT     : 'right-start',\n  RIGHTEND  : 'right-end',\n  LEFT      : 'left-start',\n  LEFTEND   : 'left-end'\n}\n\nconst Default = {\n  offset       : 0,\n  flip         : true,\n  boundary     : 'scrollParent',\n  reference    : 'toggle',\n  display      : 'dynamic',\n  popperConfig : null\n}\n\nconst DefaultType = {\n  offset       : '(number|string|function)',\n  flip         : 'boolean',\n  boundary     : '(string|element)',\n  reference    : '(string|element)',\n  display      : 'string',\n  popperConfig : '(null|object)'\n}\n\n/**\n * ------------------------------------------------------------------------\n * Class Definition\n * ------------------------------------------------------------------------\n */\n\nclass Dropdown {\n  constructor(element, config) {\n    this._element  = element\n    this._popper   = null\n    this._config   = this._getConfig(config)\n    this._menu     = this._getMenuElement()\n    this._inNavbar = this._detectNavbar()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  // Public\n\n  toggle() {\n    if (this._element.disabled || $(this._element).hasClass(ClassName.DISABLED)) {\n      return\n    }\n\n    const isActive = $(this._menu).hasClass(ClassName.SHOW)\n\n    Dropdown._clearMenus()\n\n    if (isActive) {\n      return\n    }\n\n    this.show(true)\n  }\n\n  show(usePopper = false) {\n    if (this._element.disabled || $(this._element).hasClass(ClassName.DISABLED) || $(this._menu).hasClass(ClassName.SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n    const showEvent = $.Event(Event.SHOW, relatedTarget)\n    const parent = Dropdown._getParentFromElement(this._element)\n\n    $(parent).trigger(showEvent)\n\n    if (showEvent.isDefaultPrevented()) {\n      return\n    }\n\n    // Disable totally Popper.js for Dropdown in Navbar\n    if (!this._inNavbar && usePopper) {\n      /**\n       * Check for Popper dependency\n       * Popper - https://popper.js.org\n       */\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap\\'s dropdowns require Popper.js (https://popper.js.org/)')\n      }\n\n      let referenceElement = this._element\n\n      if (this._config.reference === 'parent') {\n        referenceElement = parent\n      } else if (Util.isElement(this._config.reference)) {\n        referenceElement = this._config.reference\n\n        // Check if it's jQuery element\n        if (typeof this._config.reference.jquery !== 'undefined') {\n          referenceElement = this._config.reference[0]\n        }\n      }\n\n      // If boundary is not `scrollParent`, then set position to `static`\n      // to allow the menu to \"escape\" the scroll parent's boundaries\n      // https://github.com/twbs/bootstrap/issues/24251\n      if (this._config.boundary !== 'scrollParent') {\n        $(parent).addClass(ClassName.POSITION_STATIC)\n      }\n      this._popper = new Popper(referenceElement, this._menu, this._getPopperConfig())\n    }\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement &&\n        $(parent).closest(Selector.NAVBAR_NAV).length === 0) {\n      $(document.body).children().on('mouseover', null, $.noop)\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    $(this._menu).toggleClass(ClassName.SHOW)\n    $(parent)\n      .toggleClass(ClassName.SHOW)\n      .trigger($.Event(Event.SHOWN, relatedTarget))\n  }\n\n  hide() {\n    if (this._element.disabled || $(this._element).hasClass(ClassName.DISABLED) || !$(this._menu).hasClass(ClassName.SHOW)) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n    const hideEvent = $.Event(Event.HIDE, relatedTarget)\n    const parent = Dropdown._getParentFromElement(this._element)\n\n    $(parent).trigger(hideEvent)\n\n    if (hideEvent.isDefaultPrevented()) {\n      return\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    $(this._menu).toggleClass(ClassName.SHOW)\n    $(parent)\n      .toggleClass(ClassName.SHOW)\n      .trigger($.Event(Event.HIDDEN, relatedTarget))\n  }\n\n  dispose() {\n    $.removeData(this._element, DATA_KEY)\n    $(this._element).off(EVENT_KEY)\n    this._element = null\n    this._menu = null\n    if (this._popper !== null) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper !== null) {\n      this._popper.scheduleUpdate()\n    }\n  }\n\n  // Private\n\n  _addEventListeners() {\n    $(this._element).on(Event.CLICK, (event) => {\n      event.preventDefault()\n      event.stopPropagation()\n      this.toggle()\n    })\n  }\n\n  _getConfig(config) {\n    config = {\n      ...this.constructor.Default,\n      ...$(this._element).data(),\n      ...config\n    }\n\n    Util.typeCheckConfig(\n      NAME,\n      config,\n      this.constructor.DefaultType\n    )\n\n    return config\n  }\n\n  _getMenuElement() {\n    if (!this._menu) {\n      const parent = Dropdown._getParentFromElement(this._element)\n\n      if (parent) {\n        this._menu = parent.querySelector(Selector.MENU)\n      }\n    }\n    return this._menu\n  }\n\n  _getPlacement() {\n    const $parentDropdown = $(this._element.parentNode)\n    let placement = AttachmentMap.BOTTOM\n\n    // Handle dropup\n    if ($parentDropdown.hasClass(ClassName.DROPUP)) {\n      placement = AttachmentMap.TOP\n      if ($(this._menu).hasClass(ClassName.MENURIGHT)) {\n        placement = AttachmentMap.TOPEND\n      }\n    } else if ($parentDropdown.hasClass(ClassName.DROPRIGHT)) {\n      placement = AttachmentMap.RIGHT\n    } else if ($parentDropdown.hasClass(ClassName.DROPLEFT)) {\n      placement = AttachmentMap.LEFT\n    } else if ($(this._menu).hasClass(ClassName.MENURIGHT)) {\n      placement = AttachmentMap.BOTTOMEND\n    }\n    return placement\n  }\n\n  _detectNavbar() {\n    return $(this._element).closest('.navbar').length > 0\n  }\n\n  _getOffset() {\n    const offset = {}\n\n    if (typeof this._config.offset === 'function') {\n      offset.fn = (data) => {\n        data.offsets = {\n          ...data.offsets,\n          ...this._config.offset(data.offsets, this._element) || {}\n        }\n\n        return data\n      }\n    } else {\n      offset.offset = this._config.offset\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const popperConfig = {\n      placement: this._getPlacement(),\n      modifiers: {\n        offset: this._getOffset(),\n        flip: {\n          enabled: this._config.flip\n        },\n        preventOverflow: {\n          boundariesElement: this._config.boundary\n        }\n      }\n    }\n\n    // Disable Popper.js if we have a static display\n    if (this._config.display === 'static') {\n      popperConfig.modifiers.applyStyle = {\n        enabled: false\n      }\n    }\n\n    return {\n      ...popperConfig,\n      ...this._config.popperConfig\n    }\n  }\n\n  // Static\n\n  static _jQueryInterface(config) {\n    return this.each(function () {\n      let data = $(this).data(DATA_KEY)\n      const _config = typeof config === 'object' ? config : null\n\n      if (!data) {\n        data = new Dropdown(this, _config)\n        $(this).data(DATA_KEY, data)\n      }\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n        data[config]()\n      }\n    })\n  }\n\n  static _clearMenus(event) {\n    if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH ||\n      event.type === 'keyup' && event.which !== TAB_KEYCODE)) {\n      return\n    }\n\n    const toggles = [].slice.call(document.querySelectorAll(Selector.DATA_TOGGLE))\n\n    for (let i = 0, len = toggles.length; i < len; i++) {\n      const parent = Dropdown._getParentFromElement(toggles[i])\n      const context = $(toggles[i]).data(DATA_KEY)\n      const relatedTarget = {\n        relatedTarget: toggles[i]\n      }\n\n      if (event && event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      if (!context) {\n        continue\n      }\n\n      const dropdownMenu = context._menu\n      if (!$(parent).hasClass(ClassName.SHOW)) {\n        continue\n      }\n\n      if (event && (event.type === 'click' &&\n          /input|textarea/i.test(event.target.tagName) || event.type === 'keyup' && event.which === TAB_KEYCODE) &&\n          $.contains(parent, event.target)) {\n        continue\n      }\n\n      const hideEvent = $.Event(Event.HIDE, relatedTarget)\n      $(parent).trigger(hideEvent)\n      if (hideEvent.isDefaultPrevented()) {\n        continue\n      }\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        $(document.body).children().off('mouseover', null, $.noop)\n      }\n\n      toggles[i].setAttribute('aria-expanded', 'false')\n\n      if (context._popper) {\n        context._popper.destroy()\n      }\n\n      $(dropdownMenu).removeClass(ClassName.SHOW)\n      $(parent)\n        .removeClass(ClassName.SHOW)\n        .trigger($.Event(Event.HIDDEN, relatedTarget))\n    }\n  }\n\n  static _getParentFromElement(element) {\n    let parent\n    const selector = Util.getSelectorFromElement(element)\n\n    if (selector) {\n      parent = document.querySelector(selector)\n    }\n\n    return parent || element.parentNode\n  }\n\n  // eslint-disable-next-line complexity\n  static _dataApiKeydownHandler(event) {\n    // If not input/textarea:\n    //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n    // If input/textarea:\n    //  - If space key => not a dropdown command\n    //  - If key is other than escape\n    //    - If key is not up or down => not a dropdown command\n    //    - If trigger inside the menu => not a dropdown command\n    if (/input|textarea/i.test(event.target.tagName)\n      ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE &&\n      (event.which !== ARROW_DOWN_KEYCODE && event.which !== ARROW_UP_KEYCODE ||\n        $(event.target).closest(Selector.MENU).length) : !REGEXP_KEYDOWN.test(event.which)) {\n      return\n    }\n\n    event.preventDefault()\n    event.stopPropagation()\n\n    if (this.disabled || $(this).hasClass(ClassName.DISABLED)) {\n      return\n    }\n\n    const parent   = Dropdown._getParentFromElement(this)\n    const isActive = $(parent).hasClass(ClassName.SHOW)\n\n    if (!isActive && event.which === ESCAPE_KEYCODE) {\n      return\n    }\n\n    if (!isActive || isActive && (event.which === ESCAPE_KEYCODE || event.which === SPACE_KEYCODE)) {\n      if (event.which === ESCAPE_KEYCODE) {\n        const toggle = parent.querySelector(Selector.DATA_TOGGLE)\n        $(toggle).trigger('focus')\n      }\n\n      $(this).trigger('click')\n      return\n    }\n\n    const items = [].slice.call(parent.querySelectorAll(Selector.VISIBLE_ITEMS))\n      .filter((item) => $(item).is(':visible'))\n\n    if (items.length === 0) {\n      return\n    }\n\n    let index = items.indexOf(event.target)\n\n    if (event.which === ARROW_UP_KEYCODE && index > 0) { // Up\n      index--\n    }\n\n    if (event.which === ARROW_DOWN_KEYCODE && index < items.length - 1) { // Down\n      index++\n    }\n\n    if (index < 0) {\n      index = 0\n    }\n\n    items[index].focus()\n  }\n}\n\n/**\n * ------------------------------------------------------------------------\n * Data Api implementation\n * ------------------------------------------------------------------------\n */\n\n$(document)\n  .on(Event.KEYDOWN_DATA_API, Selector.DATA_TOGGLE, Dropdown._dataApiKeydownHandler)\n  .on(Event.KEYDOWN_DATA_API, Selector.MENU, Dropdown._dataApiKeydownHandler)\n  .on(`${Event.CLICK_DATA_API} ${Event.KEYUP_DATA_API}`, Dropdown._clearMenus)\n  .on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n    event.preventDefault()\n    event.stopPropagation()\n    Dropdown._jQueryInterface.call($(this), 'toggle')\n  })\n  .on(Event.CLICK_DATA_API, Selector.FORM_CHILD, (e) => {\n    e.stopPropagation()\n  })\n\n/**\n * ------------------------------------------------------------------------\n * jQuery\n * ------------------------------------------------------------------------\n */\n\n$.fn[NAME] = Dropdown._jQueryInterface\n$.fn[NAME].Constructor = Dropdown\n$.fn[NAME].noConflict = () => {\n  $.fn[NAME] = JQUERY_NO_CONFLICT\n  return Dropdown._jQueryInterface\n}\n\n\nexport default Dropdown\n"], "names": ["NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "$", "fn", "ESCAPE_KEYCODE", "SPACE_KEYCODE", "TAB_KEYCODE", "ARROW_UP_KEYCODE", "ARROW_DOWN_KEYCODE", "RIGHT_MOUSE_BUTTON_WHICH", "REGEXP_KEYDOWN", "RegExp", "Event", "HIDE", "HIDDEN", "SHOW", "SHOWN", "CLICK", "CLICK_DATA_API", "KEYDOWN_DATA_API", "KEYUP_DATA_API", "ClassName", "DISABLED", "DROPUP", "DROPRIGHT", "DROPLEFT", "MENURIGHT", "MENULEFT", "POSITION_STATIC", "Selector", "DATA_TOGGLE", "FORM_CHILD", "MENU", "NAVBAR_NAV", "VISIBLE_ITEMS", "AttachmentMap", "TOP", "TOPEND", "BOTTOM", "BOTTOMEND", "RIGHT", "RIGHTEND", "LEFT", "LEFTEND", "<PERSON><PERSON><PERSON>", "offset", "flip", "boundary", "reference", "display", "popperConfig", "DefaultType", "Dropdown", "element", "config", "_element", "_popper", "_config", "_getConfig", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "_addEventListeners", "toggle", "disabled", "hasClass", "isActive", "_clearMenus", "show", "usePopper", "relatedTarget", "showEvent", "parent", "_getParentFromElement", "trigger", "isDefaultPrevented", "<PERSON><PERSON>", "TypeError", "referenceElement", "<PERSON><PERSON>", "isElement", "j<PERSON>y", "addClass", "_getPopperConfig", "document", "documentElement", "closest", "length", "body", "children", "on", "noop", "focus", "setAttribute", "toggleClass", "hide", "hideEvent", "destroy", "dispose", "removeData", "off", "update", "scheduleUpdate", "event", "preventDefault", "stopPropagation", "constructor", "data", "typeCheckConfig", "querySelector", "_getPlacement", "$parentDropdown", "parentNode", "placement", "_getOffset", "offsets", "modifiers", "enabled", "preventOverflow", "boundariesElement", "applyStyle", "_jQueryInterface", "each", "which", "type", "toggles", "slice", "call", "querySelectorAll", "i", "len", "context", "clickEvent", "dropdownMenu", "test", "target", "tagName", "contains", "removeClass", "selector", "getSelectorFromElement", "_dataApiKeydownHandler", "items", "filter", "item", "is", "index", "indexOf", "e", "<PERSON><PERSON><PERSON><PERSON>", "noConflict"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAWA;;;;;;EAMA,IAAMA,IAAI,GAAuB,UAAjC;EACA,IAAMC,OAAO,GAAoB,OAAjC;EACA,IAAMC,QAAQ,GAAmB,aAAjC;EACA,IAAMC,SAAS,SAAsBD,QAArC;EACA,IAAME,YAAY,GAAe,WAAjC;EACA,IAAMC,kBAAkB,GAASC,CAAC,CAACC,EAAF,CAAKP,IAAL,CAAjC;EACA,IAAMQ,cAAc,GAAa,EAAjC;;EACA,IAAMC,aAAa,GAAc,EAAjC;;EACA,IAAMC,WAAW,GAAgB,CAAjC;;EACA,IAAMC,gBAAgB,GAAW,EAAjC;;EACA,IAAMC,kBAAkB,GAAS,EAAjC;;EACA,IAAMC,wBAAwB,GAAG,CAAjC;;EACA,IAAMC,cAAc,GAAa,IAAIC,MAAJ,CAAcJ,gBAAd,SAAkCC,kBAAlC,SAAwDJ,cAAxD,CAAjC;EAEA,IAAMQ,KAAK,GAAG;EACZC,EAAAA,IAAI,WAAsBd,SADd;EAEZe,EAAAA,MAAM,aAAsBf,SAFhB;EAGZgB,EAAAA,IAAI,WAAsBhB,SAHd;EAIZiB,EAAAA,KAAK,YAAsBjB,SAJf;EAKZkB,EAAAA,KAAK,YAAsBlB,SALf;EAMZmB,EAAAA,cAAc,YAAanB,SAAb,GAAyBC,YAN3B;EAOZmB,EAAAA,gBAAgB,cAAapB,SAAb,GAAyBC,YAP7B;EAQZoB,EAAAA,cAAc,YAAarB,SAAb,GAAyBC;EAR3B,CAAd;EAWA,IAAMqB,SAAS,GAAG;EAChBC,EAAAA,QAAQ,EAAU,UADF;EAEhBP,EAAAA,IAAI,EAAc,MAFF;EAGhBQ,EAAAA,MAAM,EAAY,QAHF;EAIhBC,EAAAA,SAAS,EAAS,WAJF;EAKhBC,EAAAA,QAAQ,EAAU,UALF;EAMhBC,EAAAA,SAAS,EAAS,qBANF;EAOhBC,EAAAA,QAAQ,EAAU,oBAPF;EAQhBC,EAAAA,eAAe,EAAG;EARF,CAAlB;EAWA,IAAMC,QAAQ,GAAG;EACfC,EAAAA,WAAW,EAAK,0BADD;EAEfC,EAAAA,UAAU,EAAM,gBAFD;EAGfC,EAAAA,IAAI,EAAY,gBAHD;EAIfC,EAAAA,UAAU,EAAM,aAJD;EAKfC,EAAAA,aAAa,EAAG;EALD,CAAjB;EAQA,IAAMC,aAAa,GAAG;EACpBC,EAAAA,GAAG,EAAS,WADQ;EAEpBC,EAAAA,MAAM,EAAM,SAFQ;EAGpBC,EAAAA,MAAM,EAAM,cAHQ;EAIpBC,EAAAA,SAAS,EAAG,YAJQ;EAKpBC,EAAAA,KAAK,EAAO,aALQ;EAMpBC,EAAAA,QAAQ,EAAI,WANQ;EAOpBC,EAAAA,IAAI,EAAQ,YAPQ;EAQpBC,EAAAA,OAAO,EAAK;EARQ,CAAtB;EAWA,IAAMC,OAAO,GAAG;EACdC,EAAAA,MAAM,EAAS,CADD;EAEdC,EAAAA,IAAI,EAAW,IAFD;EAGdC,EAAAA,QAAQ,EAAO,cAHD;EAIdC,EAAAA,SAAS,EAAM,QAJD;EAKdC,EAAAA,OAAO,EAAQ,SALD;EAMdC,EAAAA,YAAY,EAAG;EAND,CAAhB;EASA,IAAMC,WAAW,GAAG;EAClBN,EAAAA,MAAM,EAAS,0BADG;EAElBC,EAAAA,IAAI,EAAW,SAFG;EAGlBC,EAAAA,QAAQ,EAAO,kBAHG;EAIlBC,EAAAA,SAAS,EAAM,kBAJG;EAKlBC,EAAAA,OAAO,EAAQ,QALG;EAMlBC,EAAAA,YAAY,EAAG;EANG,CAApB;EASA;;;;;;MAMME;;;EACJ,oBAAYC,OAAZ,EAAqBC,MAArB,EAA6B;EAC3B,SAAKC,QAAL,GAAiBF,OAAjB;EACA,SAAKG,OAAL,GAAiB,IAAjB;EACA,SAAKC,OAAL,GAAiB,KAAKC,UAAL,CAAgBJ,MAAhB,CAAjB;EACA,SAAKK,KAAL,GAAiB,KAAKC,eAAL,EAAjB;EACA,SAAKC,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;EAEA,SAAKC,kBAAL;EACD;;;;;EAgBD;WAEAC,SAAA,kBAAS;EACP,QAAI,KAAKT,QAAL,CAAcU,QAAd,IAA0B/D,CAAC,CAAC,KAAKqD,QAAN,CAAD,CAAiBW,QAAjB,CAA0B7C,SAAS,CAACC,QAApC,CAA9B,EAA6E;EAC3E;EACD;;EAED,QAAM6C,QAAQ,GAAGjE,CAAC,CAAC,KAAKyD,KAAN,CAAD,CAAcO,QAAd,CAAuB7C,SAAS,CAACN,IAAjC,CAAjB;;EAEAqC,IAAAA,QAAQ,CAACgB,WAAT;;EAEA,QAAID,QAAJ,EAAc;EACZ;EACD;;EAED,SAAKE,IAAL,CAAU,IAAV;EACD;;WAEDA,OAAA,cAAKC,SAAL,EAAwB;EAAA,QAAnBA,SAAmB;EAAnBA,MAAAA,SAAmB,GAAP,KAAO;EAAA;;EACtB,QAAI,KAAKf,QAAL,CAAcU,QAAd,IAA0B/D,CAAC,CAAC,KAAKqD,QAAN,CAAD,CAAiBW,QAAjB,CAA0B7C,SAAS,CAACC,QAApC,CAA1B,IAA2EpB,CAAC,CAAC,KAAKyD,KAAN,CAAD,CAAcO,QAAd,CAAuB7C,SAAS,CAACN,IAAjC,CAA/E,EAAuH;EACrH;EACD;;EAED,QAAMwD,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,KAAKhB;EADA,KAAtB;EAGA,QAAMiB,SAAS,GAAGtE,CAAC,CAACU,KAAF,CAAQA,KAAK,CAACG,IAAd,EAAoBwD,aAApB,CAAlB;;EACA,QAAME,MAAM,GAAGrB,QAAQ,CAACsB,qBAAT,CAA+B,KAAKnB,QAApC,CAAf;;EAEArD,IAAAA,CAAC,CAACuE,MAAD,CAAD,CAAUE,OAAV,CAAkBH,SAAlB;;EAEA,QAAIA,SAAS,CAACI,kBAAV,EAAJ,EAAoC;EAClC;EACD,KAfqB;;;EAkBtB,QAAI,CAAC,KAAKf,SAAN,IAAmBS,SAAvB,EAAkC;EAChC;;;;EAIA,UAAI,OAAOO,MAAP,KAAkB,WAAtB,EAAmC;EACjC,cAAM,IAAIC,SAAJ,CAAc,mEAAd,CAAN;EACD;;EAED,UAAIC,gBAAgB,GAAG,KAAKxB,QAA5B;;EAEA,UAAI,KAAKE,OAAL,CAAaT,SAAb,KAA2B,QAA/B,EAAyC;EACvC+B,QAAAA,gBAAgB,GAAGN,MAAnB;EACD,OAFD,MAEO,IAAIO,IAAI,CAACC,SAAL,CAAe,KAAKxB,OAAL,CAAaT,SAA5B,CAAJ,EAA4C;EACjD+B,QAAAA,gBAAgB,GAAG,KAAKtB,OAAL,CAAaT,SAAhC,CADiD;;EAIjD,YAAI,OAAO,KAAKS,OAAL,CAAaT,SAAb,CAAuBkC,MAA9B,KAAyC,WAA7C,EAA0D;EACxDH,UAAAA,gBAAgB,GAAG,KAAKtB,OAAL,CAAaT,SAAb,CAAuB,CAAvB,CAAnB;EACD;EACF,OApB+B;EAuBhC;EACA;;;EACA,UAAI,KAAKS,OAAL,CAAaV,QAAb,KAA0B,cAA9B,EAA8C;EAC5C7C,QAAAA,CAAC,CAACuE,MAAD,CAAD,CAAUU,QAAV,CAAmB9D,SAAS,CAACO,eAA7B;EACD;;EACD,WAAK4B,OAAL,GAAe,IAAIqB,MAAJ,CAAWE,gBAAX,EAA6B,KAAKpB,KAAlC,EAAyC,KAAKyB,gBAAL,EAAzC,CAAf;EACD,KA/CqB;EAkDtB;EACA;EACA;;;EACA,QAAI,kBAAkBC,QAAQ,CAACC,eAA3B,IACApF,CAAC,CAACuE,MAAD,CAAD,CAAUc,OAAV,CAAkB1D,QAAQ,CAACI,UAA3B,EAAuCuD,MAAvC,KAAkD,CADtD,EACyD;EACvDtF,MAAAA,CAAC,CAACmF,QAAQ,CAACI,IAAV,CAAD,CAAiBC,QAAjB,GAA4BC,EAA5B,CAA+B,WAA/B,EAA4C,IAA5C,EAAkDzF,CAAC,CAAC0F,IAApD;EACD;;EAED,SAAKrC,QAAL,CAAcsC,KAAd;;EACA,SAAKtC,QAAL,CAAcuC,YAAd,CAA2B,eAA3B,EAA4C,IAA5C;;EAEA5F,IAAAA,CAAC,CAAC,KAAKyD,KAAN,CAAD,CAAcoC,WAAd,CAA0B1E,SAAS,CAACN,IAApC;EACAb,IAAAA,CAAC,CAACuE,MAAD,CAAD,CACGsB,WADH,CACe1E,SAAS,CAACN,IADzB,EAEG4D,OAFH,CAEWzE,CAAC,CAACU,KAAF,CAAQA,KAAK,CAACI,KAAd,EAAqBuD,aAArB,CAFX;EAGD;;WAEDyB,OAAA,gBAAO;EACL,QAAI,KAAKzC,QAAL,CAAcU,QAAd,IAA0B/D,CAAC,CAAC,KAAKqD,QAAN,CAAD,CAAiBW,QAAjB,CAA0B7C,SAAS,CAACC,QAApC,CAA1B,IAA2E,CAACpB,CAAC,CAAC,KAAKyD,KAAN,CAAD,CAAcO,QAAd,CAAuB7C,SAAS,CAACN,IAAjC,CAAhF,EAAwH;EACtH;EACD;;EAED,QAAMwD,aAAa,GAAG;EACpBA,MAAAA,aAAa,EAAE,KAAKhB;EADA,KAAtB;EAGA,QAAM0C,SAAS,GAAG/F,CAAC,CAACU,KAAF,CAAQA,KAAK,CAACC,IAAd,EAAoB0D,aAApB,CAAlB;;EACA,QAAME,MAAM,GAAGrB,QAAQ,CAACsB,qBAAT,CAA+B,KAAKnB,QAApC,CAAf;;EAEArD,IAAAA,CAAC,CAACuE,MAAD,CAAD,CAAUE,OAAV,CAAkBsB,SAAlB;;EAEA,QAAIA,SAAS,CAACrB,kBAAV,EAAJ,EAAoC;EAClC;EACD;;EAED,QAAI,KAAKpB,OAAT,EAAkB;EAChB,WAAKA,OAAL,CAAa0C,OAAb;EACD;;EAEDhG,IAAAA,CAAC,CAAC,KAAKyD,KAAN,CAAD,CAAcoC,WAAd,CAA0B1E,SAAS,CAACN,IAApC;EACAb,IAAAA,CAAC,CAACuE,MAAD,CAAD,CACGsB,WADH,CACe1E,SAAS,CAACN,IADzB,EAEG4D,OAFH,CAEWzE,CAAC,CAACU,KAAF,CAAQA,KAAK,CAACE,MAAd,EAAsByD,aAAtB,CAFX;EAGD;;WAED4B,UAAA,mBAAU;EACRjG,IAAAA,CAAC,CAACkG,UAAF,CAAa,KAAK7C,QAAlB,EAA4BzD,QAA5B;EACAI,IAAAA,CAAC,CAAC,KAAKqD,QAAN,CAAD,CAAiB8C,GAAjB,CAAqBtG,SAArB;EACA,SAAKwD,QAAL,GAAgB,IAAhB;EACA,SAAKI,KAAL,GAAa,IAAb;;EACA,QAAI,KAAKH,OAAL,KAAiB,IAArB,EAA2B;EACzB,WAAKA,OAAL,CAAa0C,OAAb;;EACA,WAAK1C,OAAL,GAAe,IAAf;EACD;EACF;;WAED8C,SAAA,kBAAS;EACP,SAAKzC,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;EACA,QAAI,KAAKN,OAAL,KAAiB,IAArB,EAA2B;EACzB,WAAKA,OAAL,CAAa+C,cAAb;EACD;EACF;;;WAIDxC,qBAAA,8BAAqB;EAAA;;EACnB7D,IAAAA,CAAC,CAAC,KAAKqD,QAAN,CAAD,CAAiBoC,EAAjB,CAAoB/E,KAAK,CAACK,KAA1B,EAAiC,UAACuF,KAAD,EAAW;EAC1CA,MAAAA,KAAK,CAACC,cAAN;EACAD,MAAAA,KAAK,CAACE,eAAN;;EACA,MAAA,KAAI,CAAC1C,MAAL;EACD,KAJD;EAKD;;WAEDN,aAAA,oBAAWJ,MAAX,EAAmB;EACjBA,IAAAA,MAAM,sBACD,KAAKqD,WAAL,CAAiB/D,OADhB,MAED1C,CAAC,CAAC,KAAKqD,QAAN,CAAD,CAAiBqD,IAAjB,EAFC,MAGDtD,MAHC,CAAN;EAMA0B,IAAAA,IAAI,CAAC6B,eAAL,CACEjH,IADF,EAEE0D,MAFF,EAGE,KAAKqD,WAAL,CAAiBxD,WAHnB;EAMA,WAAOG,MAAP;EACD;;WAEDM,kBAAA,2BAAkB;EAChB,QAAI,CAAC,KAAKD,KAAV,EAAiB;EACf,UAAMc,MAAM,GAAGrB,QAAQ,CAACsB,qBAAT,CAA+B,KAAKnB,QAApC,CAAf;;EAEA,UAAIkB,MAAJ,EAAY;EACV,aAAKd,KAAL,GAAac,MAAM,CAACqC,aAAP,CAAqBjF,QAAQ,CAACG,IAA9B,CAAb;EACD;EACF;;EACD,WAAO,KAAK2B,KAAZ;EACD;;WAEDoD,gBAAA,yBAAgB;EACd,QAAMC,eAAe,GAAG9G,CAAC,CAAC,KAAKqD,QAAL,CAAc0D,UAAf,CAAzB;EACA,QAAIC,SAAS,GAAG/E,aAAa,CAACG,MAA9B,CAFc;;EAKd,QAAI0E,eAAe,CAAC9C,QAAhB,CAAyB7C,SAAS,CAACE,MAAnC,CAAJ,EAAgD;EAC9C2F,MAAAA,SAAS,GAAG/E,aAAa,CAACC,GAA1B;;EACA,UAAIlC,CAAC,CAAC,KAAKyD,KAAN,CAAD,CAAcO,QAAd,CAAuB7C,SAAS,CAACK,SAAjC,CAAJ,EAAiD;EAC/CwF,QAAAA,SAAS,GAAG/E,aAAa,CAACE,MAA1B;EACD;EACF,KALD,MAKO,IAAI2E,eAAe,CAAC9C,QAAhB,CAAyB7C,SAAS,CAACG,SAAnC,CAAJ,EAAmD;EACxD0F,MAAAA,SAAS,GAAG/E,aAAa,CAACK,KAA1B;EACD,KAFM,MAEA,IAAIwE,eAAe,CAAC9C,QAAhB,CAAyB7C,SAAS,CAACI,QAAnC,CAAJ,EAAkD;EACvDyF,MAAAA,SAAS,GAAG/E,aAAa,CAACO,IAA1B;EACD,KAFM,MAEA,IAAIxC,CAAC,CAAC,KAAKyD,KAAN,CAAD,CAAcO,QAAd,CAAuB7C,SAAS,CAACK,SAAjC,CAAJ,EAAiD;EACtDwF,MAAAA,SAAS,GAAG/E,aAAa,CAACI,SAA1B;EACD;;EACD,WAAO2E,SAAP;EACD;;WAEDpD,gBAAA,yBAAgB;EACd,WAAO5D,CAAC,CAAC,KAAKqD,QAAN,CAAD,CAAiBgC,OAAjB,CAAyB,SAAzB,EAAoCC,MAApC,GAA6C,CAApD;EACD;;WAED2B,aAAA,sBAAa;EAAA;;EACX,QAAMtE,MAAM,GAAG,EAAf;;EAEA,QAAI,OAAO,KAAKY,OAAL,CAAaZ,MAApB,KAA+B,UAAnC,EAA+C;EAC7CA,MAAAA,MAAM,CAAC1C,EAAP,GAAY,UAACyG,IAAD,EAAU;EACpBA,QAAAA,IAAI,CAACQ,OAAL,sBACKR,IAAI,CAACQ,OADV,MAEK,MAAI,CAAC3D,OAAL,CAAaZ,MAAb,CAAoB+D,IAAI,CAACQ,OAAzB,EAAkC,MAAI,CAAC7D,QAAvC,KAAoD,EAFzD;EAKA,eAAOqD,IAAP;EACD,OAPD;EAQD,KATD,MASO;EACL/D,MAAAA,MAAM,CAACA,MAAP,GAAgB,KAAKY,OAAL,CAAaZ,MAA7B;EACD;;EAED,WAAOA,MAAP;EACD;;WAEDuC,mBAAA,4BAAmB;EACjB,QAAMlC,YAAY,GAAG;EACnBgE,MAAAA,SAAS,EAAE,KAAKH,aAAL,EADQ;EAEnBM,MAAAA,SAAS,EAAE;EACTxE,QAAAA,MAAM,EAAE,KAAKsE,UAAL,EADC;EAETrE,QAAAA,IAAI,EAAE;EACJwE,UAAAA,OAAO,EAAE,KAAK7D,OAAL,CAAaX;EADlB,SAFG;EAKTyE,QAAAA,eAAe,EAAE;EACfC,UAAAA,iBAAiB,EAAE,KAAK/D,OAAL,CAAaV;EADjB;EALR;EAFQ,KAArB,CADiB;;EAejB,QAAI,KAAKU,OAAL,CAAaR,OAAb,KAAyB,QAA7B,EAAuC;EACrCC,MAAAA,YAAY,CAACmE,SAAb,CAAuBI,UAAvB,GAAoC;EAClCH,QAAAA,OAAO,EAAE;EADyB,OAApC;EAGD;;EAED,8BACKpE,YADL,MAEK,KAAKO,OAAL,CAAaP,YAFlB;EAID;;;aAIMwE,mBAAP,0BAAwBpE,MAAxB,EAAgC;EAC9B,WAAO,KAAKqE,IAAL,CAAU,YAAY;EAC3B,UAAIf,IAAI,GAAG1G,CAAC,CAAC,IAAD,CAAD,CAAQ0G,IAAR,CAAa9G,QAAb,CAAX;;EACA,UAAM2D,OAAO,GAAG,OAAOH,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;EAEA,UAAI,CAACsD,IAAL,EAAW;EACTA,QAAAA,IAAI,GAAG,IAAIxD,QAAJ,CAAa,IAAb,EAAmBK,OAAnB,CAAP;EACAvD,QAAAA,CAAC,CAAC,IAAD,CAAD,CAAQ0G,IAAR,CAAa9G,QAAb,EAAuB8G,IAAvB;EACD;;EAED,UAAI,OAAOtD,MAAP,KAAkB,QAAtB,EAAgC;EAC9B,YAAI,OAAOsD,IAAI,CAACtD,MAAD,CAAX,KAAwB,WAA5B,EAAyC;EACvC,gBAAM,IAAIwB,SAAJ,wBAAkCxB,MAAlC,QAAN;EACD;;EACDsD,QAAAA,IAAI,CAACtD,MAAD,CAAJ;EACD;EACF,KAfM,CAAP;EAgBD;;aAEMc,cAAP,qBAAmBoC,KAAnB,EAA0B;EACxB,QAAIA,KAAK,KAAKA,KAAK,CAACoB,KAAN,KAAgBnH,wBAAhB,IACZ+F,KAAK,CAACqB,IAAN,KAAe,OAAf,IAA0BrB,KAAK,CAACoB,KAAN,KAAgBtH,WADnC,CAAT,EAC0D;EACxD;EACD;;EAED,QAAMwH,OAAO,GAAG,GAAGC,KAAH,CAASC,IAAT,CAAc3C,QAAQ,CAAC4C,gBAAT,CAA0BpG,QAAQ,CAACC,WAAnC,CAAd,CAAhB;;EAEA,SAAK,IAAIoG,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGL,OAAO,CAACtC,MAA9B,EAAsC0C,CAAC,GAAGC,GAA1C,EAA+CD,CAAC,EAAhD,EAAoD;EAClD,UAAMzD,MAAM,GAAGrB,QAAQ,CAACsB,qBAAT,CAA+BoD,OAAO,CAACI,CAAD,CAAtC,CAAf;;EACA,UAAME,OAAO,GAAGlI,CAAC,CAAC4H,OAAO,CAACI,CAAD,CAAR,CAAD,CAActB,IAAd,CAAmB9G,QAAnB,CAAhB;EACA,UAAMyE,aAAa,GAAG;EACpBA,QAAAA,aAAa,EAAEuD,OAAO,CAACI,CAAD;EADF,OAAtB;;EAIA,UAAI1B,KAAK,IAAIA,KAAK,CAACqB,IAAN,KAAe,OAA5B,EAAqC;EACnCtD,QAAAA,aAAa,CAAC8D,UAAd,GAA2B7B,KAA3B;EACD;;EAED,UAAI,CAAC4B,OAAL,EAAc;EACZ;EACD;;EAED,UAAME,YAAY,GAAGF,OAAO,CAACzE,KAA7B;;EACA,UAAI,CAACzD,CAAC,CAACuE,MAAD,CAAD,CAAUP,QAAV,CAAmB7C,SAAS,CAACN,IAA7B,CAAL,EAAyC;EACvC;EACD;;EAED,UAAIyF,KAAK,KAAKA,KAAK,CAACqB,IAAN,KAAe,OAAf,IACV,kBAAkBU,IAAlB,CAAuB/B,KAAK,CAACgC,MAAN,CAAaC,OAApC,CADU,IACsCjC,KAAK,CAACqB,IAAN,KAAe,OAAf,IAA0BrB,KAAK,CAACoB,KAAN,KAAgBtH,WADrF,CAAL,IAEAJ,CAAC,CAACwI,QAAF,CAAWjE,MAAX,EAAmB+B,KAAK,CAACgC,MAAzB,CAFJ,EAEsC;EACpC;EACD;;EAED,UAAMvC,SAAS,GAAG/F,CAAC,CAACU,KAAF,CAAQA,KAAK,CAACC,IAAd,EAAoB0D,aAApB,CAAlB;EACArE,MAAAA,CAAC,CAACuE,MAAD,CAAD,CAAUE,OAAV,CAAkBsB,SAAlB;;EACA,UAAIA,SAAS,CAACrB,kBAAV,EAAJ,EAAoC;EAClC;EACD,OA9BiD;EAiClD;;;EACA,UAAI,kBAAkBS,QAAQ,CAACC,eAA/B,EAAgD;EAC9CpF,QAAAA,CAAC,CAACmF,QAAQ,CAACI,IAAV,CAAD,CAAiBC,QAAjB,GAA4BW,GAA5B,CAAgC,WAAhC,EAA6C,IAA7C,EAAmDnG,CAAC,CAAC0F,IAArD;EACD;;EAEDkC,MAAAA,OAAO,CAACI,CAAD,CAAP,CAAWpC,YAAX,CAAwB,eAAxB,EAAyC,OAAzC;;EAEA,UAAIsC,OAAO,CAAC5E,OAAZ,EAAqB;EACnB4E,QAAAA,OAAO,CAAC5E,OAAR,CAAgB0C,OAAhB;EACD;;EAEDhG,MAAAA,CAAC,CAACoI,YAAD,CAAD,CAAgBK,WAAhB,CAA4BtH,SAAS,CAACN,IAAtC;EACAb,MAAAA,CAAC,CAACuE,MAAD,CAAD,CACGkE,WADH,CACetH,SAAS,CAACN,IADzB,EAEG4D,OAFH,CAEWzE,CAAC,CAACU,KAAF,CAAQA,KAAK,CAACE,MAAd,EAAsByD,aAAtB,CAFX;EAGD;EACF;;aAEMG,wBAAP,+BAA6BrB,OAA7B,EAAsC;EACpC,QAAIoB,MAAJ;EACA,QAAMmE,QAAQ,GAAG5D,IAAI,CAAC6D,sBAAL,CAA4BxF,OAA5B,CAAjB;;EAEA,QAAIuF,QAAJ,EAAc;EACZnE,MAAAA,MAAM,GAAGY,QAAQ,CAACyB,aAAT,CAAuB8B,QAAvB,CAAT;EACD;;EAED,WAAOnE,MAAM,IAAIpB,OAAO,CAAC4D,UAAzB;EACD;;;aAGM6B,yBAAP,gCAA8BtC,KAA9B,EAAqC;EACnC;EACA;EACA;EACA;EACA;EACA;EACA;EACA,QAAI,kBAAkB+B,IAAlB,CAAuB/B,KAAK,CAACgC,MAAN,CAAaC,OAApC,IACAjC,KAAK,CAACoB,KAAN,KAAgBvH,aAAhB,IAAiCmG,KAAK,CAACoB,KAAN,KAAgBxH,cAAhB,KAClCoG,KAAK,CAACoB,KAAN,KAAgBpH,kBAAhB,IAAsCgG,KAAK,CAACoB,KAAN,KAAgBrH,gBAAtD,IACCL,CAAC,CAACsG,KAAK,CAACgC,MAAP,CAAD,CAAgBjD,OAAhB,CAAwB1D,QAAQ,CAACG,IAAjC,EAAuCwD,MAFN,CADjC,GAGiD,CAAC9E,cAAc,CAAC6H,IAAf,CAAoB/B,KAAK,CAACoB,KAA1B,CAHtD,EAGwF;EACtF;EACD;;EAEDpB,IAAAA,KAAK,CAACC,cAAN;EACAD,IAAAA,KAAK,CAACE,eAAN;;EAEA,QAAI,KAAKzC,QAAL,IAAiB/D,CAAC,CAAC,IAAD,CAAD,CAAQgE,QAAR,CAAiB7C,SAAS,CAACC,QAA3B,CAArB,EAA2D;EACzD;EACD;;EAED,QAAMmD,MAAM,GAAKrB,QAAQ,CAACsB,qBAAT,CAA+B,IAA/B,CAAjB;;EACA,QAAMP,QAAQ,GAAGjE,CAAC,CAACuE,MAAD,CAAD,CAAUP,QAAV,CAAmB7C,SAAS,CAACN,IAA7B,CAAjB;;EAEA,QAAI,CAACoD,QAAD,IAAaqC,KAAK,CAACoB,KAAN,KAAgBxH,cAAjC,EAAiD;EAC/C;EACD;;EAED,QAAI,CAAC+D,QAAD,IAAaA,QAAQ,KAAKqC,KAAK,CAACoB,KAAN,KAAgBxH,cAAhB,IAAkCoG,KAAK,CAACoB,KAAN,KAAgBvH,aAAvD,CAAzB,EAAgG;EAC9F,UAAImG,KAAK,CAACoB,KAAN,KAAgBxH,cAApB,EAAoC;EAClC,YAAM4D,MAAM,GAAGS,MAAM,CAACqC,aAAP,CAAqBjF,QAAQ,CAACC,WAA9B,CAAf;EACA5B,QAAAA,CAAC,CAAC8D,MAAD,CAAD,CAAUW,OAAV,CAAkB,OAAlB;EACD;;EAEDzE,MAAAA,CAAC,CAAC,IAAD,CAAD,CAAQyE,OAAR,CAAgB,OAAhB;EACA;EACD;;EAED,QAAMoE,KAAK,GAAG,GAAGhB,KAAH,CAASC,IAAT,CAAcvD,MAAM,CAACwD,gBAAP,CAAwBpG,QAAQ,CAACK,aAAjC,CAAd,EACX8G,MADW,CACJ,UAACC,IAAD;EAAA,aAAU/I,CAAC,CAAC+I,IAAD,CAAD,CAAQC,EAAR,CAAW,UAAX,CAAV;EAAA,KADI,CAAd;;EAGA,QAAIH,KAAK,CAACvD,MAAN,KAAiB,CAArB,EAAwB;EACtB;EACD;;EAED,QAAI2D,KAAK,GAAGJ,KAAK,CAACK,OAAN,CAAc5C,KAAK,CAACgC,MAApB,CAAZ;;EAEA,QAAIhC,KAAK,CAACoB,KAAN,KAAgBrH,gBAAhB,IAAoC4I,KAAK,GAAG,CAAhD,EAAmD;EAAE;EACnDA,MAAAA,KAAK;EACN;;EAED,QAAI3C,KAAK,CAACoB,KAAN,KAAgBpH,kBAAhB,IAAsC2I,KAAK,GAAGJ,KAAK,CAACvD,MAAN,GAAe,CAAjE,EAAoE;EAAE;EACpE2D,MAAAA,KAAK;EACN;;EAED,QAAIA,KAAK,GAAG,CAAZ,EAAe;EACbA,MAAAA,KAAK,GAAG,CAAR;EACD;;EAEDJ,IAAAA,KAAK,CAACI,KAAD,CAAL,CAAatD,KAAb;EACD;;;;0BAlZoB;EACnB,aAAOhG,OAAP;EACD;;;0BAEoB;EACnB,aAAO+C,OAAP;EACD;;;0BAEwB;EACvB,aAAOO,WAAP;EACD;;;;;EA2YH;;;;;;;EAMAjD,CAAC,CAACmF,QAAD,CAAD,CACGM,EADH,CACM/E,KAAK,CAACO,gBADZ,EAC8BU,QAAQ,CAACC,WADvC,EACoDsB,QAAQ,CAAC0F,sBAD7D,EAEGnD,EAFH,CAEM/E,KAAK,CAACO,gBAFZ,EAE8BU,QAAQ,CAACG,IAFvC,EAE6CoB,QAAQ,CAAC0F,sBAFtD,EAGGnD,EAHH,CAGS/E,KAAK,CAACM,cAHf,SAGiCN,KAAK,CAACQ,cAHvC,EAGyDgC,QAAQ,CAACgB,WAHlE,EAIGuB,EAJH,CAIM/E,KAAK,CAACM,cAJZ,EAI4BW,QAAQ,CAACC,WAJrC,EAIkD,UAAU0E,KAAV,EAAiB;EAC/DA,EAAAA,KAAK,CAACC,cAAN;EACAD,EAAAA,KAAK,CAACE,eAAN;;EACAtD,EAAAA,QAAQ,CAACsE,gBAAT,CAA0BM,IAA1B,CAA+B9H,CAAC,CAAC,IAAD,CAAhC,EAAwC,QAAxC;EACD,CARH,EASGyF,EATH,CASM/E,KAAK,CAACM,cATZ,EAS4BW,QAAQ,CAACE,UATrC,EASiD,UAACsH,CAAD,EAAO;EACpDA,EAAAA,CAAC,CAAC3C,eAAF;EACD,CAXH;EAaA;;;;;;EAMAxG,CAAC,CAACC,EAAF,CAAKP,IAAL,IAAawD,QAAQ,CAACsE,gBAAtB;EACAxH,CAAC,CAACC,EAAF,CAAKP,IAAL,EAAW0J,WAAX,GAAyBlG,QAAzB;;EACAlD,CAAC,CAACC,EAAF,CAAKP,IAAL,EAAW2J,UAAX,GAAwB,YAAM;EAC5BrJ,EAAAA,CAAC,CAACC,EAAF,CAAKP,IAAL,IAAaK,kBAAb;EACA,SAAOmD,QAAQ,CAACsE,gBAAhB;EACD,CAHD;;;;;;;;"}