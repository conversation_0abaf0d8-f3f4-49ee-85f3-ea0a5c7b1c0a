/// <reference types="webpack/module" />

import React, { useEffect, useState } from "react";
import { IlistadoItemFactura } from "../../entities/Intefaces/IListado";
import { createRoot, Root } from "react-dom/client";
import { OperacionView } from "../operacion/OperacionView";
import {
  DatosProvider,
  useProveedor,
} from "../contextProvider/datosProveedorContext";
import {
  getDestinoFacturaDos,
  getFacturas,
} from "../../comm/apiProveedorFactura";
import { getOperacionesCabecera } from "../../comm/apiBpmPoliticasProcesos";
import { NumericFormat } from "react-number-format";

export const ListaItem = (datos: IlistadoItemFactura) => {
  const { state } = useProveedor();
  const [currentRoot, setCurrentRoot] = useState<null | Root>(null);
  const [flag, setFlag] = useState(false);
  const datosValidos = datos?.titulo;
  const fechaString = datos?.fecha?.string || 'Fecha no disponible';
  const settingPreviewDiv = () => {
    const _containerEl = document.getElementById("container");
    const _newContentEl = document.createElement("DIV");
    let _previewContentEl = _containerEl.appendChild(_newContentEl);
    _previewContentEl.id = "prevcontent";
    _previewContentEl.className = "preview preview--show";
    return _previewContentEl;
  };

  const llamarDetalles = async () => {
    if (!datosValidos) return;
    
    const nroOperacionRegex = /\d*$/;
    const nroOperacionMatch = nroOperacionRegex.exec(datos.titulo);
    if (!nroOperacionMatch?.[0]) {
      console.error("No se encontró número de operación en:", datos.titulo);
      return;
    }
    
    let nroOperacion = nroOperacionMatch[0];

    let operacionesPromise = getOperacionesCabecera("SEGFAC", undefined, nroOperacion);
    
    // Extraer idLote del subtítulo, no el número de factura formateado
    let _idLote: any = null;
    
    // Buscar en las operaciones el idLote real
    try {
      const operacionData = await operacionesPromise;
      if (operacionData && operacionData.length > 0) {
        const loteAtributo = operacionData[0].atributos?.find((attr: any) => attr.codigo === "ATRLFA");
        _idLote = loteAtributo ? parseInt(loteAtributo.valor) : null;
      }
    } catch (error) {
      console.error("Error obteniendo operación:", error);
      return;
    }

    if (!_idLote) {
      console.error("No se encontró idLote para la operación:", nroOperacion);
      return;
    }

    let datosMontoPromise = getFacturas(_idLote);

    let [operaciones, datosMonto]: any = await Promise.all([
      operacionesPromise,
      datosMontoPromise,
    ]);


    state.montoTotal = datosMonto[0].monto.total;
    state.montoCondicionVenta = datosMonto[0].condicionVenta;
    state.montoCinco = datosMonto[0].monto.montoTotalCinco;
    state.montoDiez = datosMonto[0].monto.montoTotalDiez;
    state.montoGravadasCinco = datosMonto[0].monto.gravadasCincoPorCiento;
    state.montoGravadasDiez = datosMonto[0].monto.gravadasDiezPorCiento;
    state.montoIvaCinco = datosMonto[0].monto.ivaCincoPorCiento;
    state.montoIvaDiez = datosMonto[0].monto.ivaDiezPorCiento;
    state.montoDescripcion = datosMonto[0].descripcion;
    state.montoExentas = datosMonto[0].exentas;
    state.bandejaNombre = datosMonto[0].proveedores.nombre;
    state.bandejaPais = datosMonto[0].proveedores.pais;
    state.bandejaTelefono = datosMonto[0].proveedores.telefono;
    state.bandejaNroDocumento = datosMonto[0].proveedores.cedula;
    state.bandejaDireccion = datosMonto[0].proveedores.direccion;
    state.bandejaCiudad = datosMonto[0].proveedores.ciudad;
    state.bandejaFechaEmision = datosMonto[0].fechaEmision.slice(0, 10);
    state.bandejaTimbrado = datosMonto[0].timbrado;
    state.bandejaNroFactura = datosMonto[0].numeroFactura.numero;
    state.bandejaFacturaParteUno =
      datosMonto[0].numeroFactura.idFacturaParteUno;
    state.bandejaFacturaParteDos =
      datosMonto[0].numeroFactura.idFacturaParteDos;
    state.bandejaFacturaParteTres =
      datosMonto[0].numeroFactura.idFacturaParteTres;
    state.bandejaProvision = datosMonto[0].provision;
    state.bandejaTipoMoneda = datosMonto[0].monto.moneda;
    state.bandejaTipoComprobante = datosMonto[0].tipoComprobante;

    let condicion = false;
    state.inicioDeSeguimiento = condicion;

    let _previewContentEl: HTMLElement;
    if (
      document.getElementById("prevcontent") == null ||
      document.getElementById("prevcontent") == undefined
    ) {
      _previewContentEl = settingPreviewDiv();
    } else {
      _previewContentEl = document.getElementById("prevcontent");
    }

    const _root = createRoot(_previewContentEl);
    const nroOperacionRegexFinal = /\d*$/;
    const _nroDeOperacion = nroOperacionRegexFinal.exec(datos.titulo);
    if (!_nroDeOperacion) {
      console.error(
        "No se encontró ningún número de operación en el título:",
        datos.titulo
      );
      return;
    }
    const containerId = `operacion-view-${Date.now()}`;
    const container = document.createElement('div');
    container.id = containerId;
    container.className = 'operacion-view-container';
    document.body.appendChild(container);
    
    _root.render(
      <DatosProvider>
        <OperacionView 
          operacion={_nroDeOperacion[0]} 
          data={datos} 
          onClose={() => {
            container.remove();
            _root.unmount();
          }}
        />
      </DatosProvider>
    );
    state.numeroOperacionCliente = _nroDeOperacion[0];
    localStorage.setItem("operacionSeguimiento", state.numeroOperacionCliente);
    setCurrentRoot(_root);
  };

  useEffect(() => {
    if (!flag) {
      let _previewContentEl: HTMLElement;
      const previewElement = document.getElementById("prevcontent");
      if (!previewElement) {
        _previewContentEl = settingPreviewDiv();
        const _root = createRoot(_previewContentEl);
        setCurrentRoot(_root);
        setFlag(true);
      }
    }
  }, [currentRoot]);

  if (!datosValidos) {
    console.error("Datos inválidos para ListaItem:", datos);
    return <div>Error: Datos inválidos</div>;
  }

  return (
    <div>
      <div onClick={llamarDetalles} className="tbl-item">
        <div className="tbl-item__check">
          <div className="form-group form-check">
            <input type="checkbox" className="form-check-input" id="exampleCheck1" />
          </div>
        </div>
        <div className="tbl-item__name">{datos.titulo}</div>
        <div className="tbl-item__subproduct" style={{ whiteSpace: 'nowrap' }}>{datos.subTitulo}</div>
        <div className='d-flex tbl-item__date'>
          <div className="mx-1">{fechaString}</div>
        </div>
        <div className="tbl-item__total">
          <NumericFormat value={datos.monto} displayType={'text'} thousandSeparator={true} />
          <span className='ml-1'>{datos.monedaTipo}</span>
        </div>
        <div className="tbl-item__state">
          {datos.estado === "Aprobado" ? <span className="badge badge-success">{datos.estado}</span> : ""}
          {datos.estado === "Pendiente" ? <span className="badge badge-warning">{datos.estado}</span> : ""}
          {datos.estado === "Rechazado" ? <span className="badge badge-danger">{datos.estado}</span> : ""}
          {datos.estado === "En Espera" ? <span className="badge badge-info">{datos.estado}</span> : ""}
          {datos.estado === "En Baja" ? <span className="badge badge-secondary">{datos.estado}</span> : ""}
          {datos.estado === "Inactivo" ? <span className="badge badge-dark">{datos.estado}</span> : ""}
          {datos.estado === "Realizado" ? <span className="badge badge-primary">{datos.estado}</span> : ""}
        </div>
      </div>
    </div>
  );
};
