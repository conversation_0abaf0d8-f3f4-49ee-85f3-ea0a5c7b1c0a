import React from "react"
import { createPortal } from 'react-dom'
import { ModalAdjuntos } from "./ModalAdjuntos"

export const ModalPortal = (props: any) => {
  const _nroOperacion = (props.operacion != null && props.operacion != undefined) ? props.operacion : '0'
  const jsx: React.ReactNode = (_nroOperacion === '0') ? <ModalAdjuntos /> : <ModalAdjuntos operacion={_nroOperacion} />

  return (createPortal(jsx, document.getElementById('ModalAdjuntos')))
}