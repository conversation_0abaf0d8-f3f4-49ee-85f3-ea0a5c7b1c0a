import React, { useState } from 'react';
import { useProveedor } from '../contextProvider/datosProveedorContext';
import { getUserName } from '../../utilities/_spPageContextInfo';
import { getAmbienteContexto, getUrlBase } from '../../utilities/contextInfo';
import axios, { AxiosResponse } from 'axios';
import EndPoint from '../../comm/EndPoint.Config';
import { getApiProveedorFacturaHeaders } from '../../comm/contracts/ApiBpmPoliticasProcesosRequestHeaders';
import { EditarTimbrado } from '../../entities/EditarTimbrado';
import { ErrorResponse } from '../../comm/contracts/ErrorResponse';
import { Estados } from '../../entities/Enums/estadosRespuesta';
import Loader from './Loader';
import { But<PERSON>, Form } from 'react-bootstrap';
import ModalValidacionAltaProveedor from '../modales/ModalValidacionAltaProveedor';
import ModalValidacionExitosaEditarTimbrado from '../modales/ModalValidacionExitosaEditarTimbrado';

interface ModalProps {
  cerrar: () => void;
}

const CampoTimbrado: React.FC<ModalProps> = ({ cerrar }) => {

  const { state } = useProveedor();

  const [datosTimbradoPut, setDatosTimbradoPut] = useState({
    numeroTimbrado: state.numeroTimbrado, numeroDocumento: state.numeroDocumento,
    tipoDocumento: state.tipoDocumento, usuario: getUserName(), timbradoState: false,
    codigoCliente: state.codigoCliente, statusTimbrado: ''
  });

  const [datoTimbradoFechaVencimiento, setDatoTimbradoFechaVencimiento] = useState({ fechaVencimiento: state.fechaVencimiento })

  let numeroTimbrado: any = datosTimbradoPut.numeroTimbrado;

  console.log("datosTimbradoPut:", datosTimbradoPut);

  console.log("numeroTimbrado:", numeroTimbrado);

  let fechaVencimiento: any = datoTimbradoFechaVencimiento.fechaVencimiento;

  console.log("fechaVencimiento:", fechaVencimiento);

  const [isLoading, setIsLoading] = useState(false);

  const [modalValidacion, setModalValidacion] = useState(false);
  const [validacionExitosa, setValidacionExitosa] = useState(false);
  const cerrarModalValidacion = () => { setModalValidacion(false); };
  const cerrarModalValidacionExitosa = () => { setValidacionExitosa(false); window.location.reload(); }
  const [mensajeValidacion, setMensajeValidacion] = useState("");
  const abrirModalIngresarFechaCorrecta = () => { setModalValidacion(true); setMensajeValidacion("Debe ingresar una fecha mayor o igual a la actual."); };
  const abrirModalValidacionTimbrado = () => { setModalValidacion(true); setMensajeValidacion("El número de timbrado debe contar con 8 dígitos."); };
  const abrirModalAgregarFechaVencimiento = () => { setModalValidacion(true); setMensajeValidacion("Debe agregar una fecha de vencimiento."); };
  const abrirModalValidacionExitosa = () => { setValidacionExitosa(true); setMensajeValidacion("El timbrado se ha modificado correctamente."); };
  const abrirModalValidacionError = () => { setModalValidacion(true); setMensajeValidacion("No se pudo modificar el timbrado."); };

  const Params = {
    urlBase: getUrlBase(),
    ambiente: getAmbienteContexto(),
    request: axios.create(),
    endpoints: EndPoint.apiProveedorFactura
  }

  const putModificarTimbrado = async () => {
    let _headers = await getApiProveedorFacturaHeaders('');
    let _sturlEndpoint = Params.endpoints.vUno.put.timbrado

    let _body = {
      numeroTimbrado: datosTimbradoPut.numeroTimbrado,
      numeroDocumento: datosTimbradoPut.numeroDocumento,
      tipoDocumento: datosTimbradoPut.tipoDocumento,
      fechaVencimiento: datoTimbradoFechaVencimiento.fechaVencimiento,
      usuario: datosTimbradoPut.usuario,
    }

    console.log("body putModificarTimbrado:", _body);

    let config = {
      method: 'put',
      url: Params.urlBase + _sturlEndpoint,
      headers: _headers
    }

    console.log("config url putModificarTimbrado:", config.url);

    const promise = Params.request.put<any, AxiosResponse<EditarTimbrado, ErrorResponse>>(config.url, _body, config)
      .then(function (response) {
        if (response.status === Estados.estadoExitoso) {
          console.log("Status Put Timbrado:", response.status);
          console.log("Response Put edit Timbrado:", response.data);
          datosTimbradoPut.statusTimbrado = response.status.toString();
        }

        if (response.status === Estados.estadoValidacionIncorrecta) {
          alert("Por favor, verificar los datos.");
        }

        if (response.status === Estados.estadoNoAutorizado) {
          alert("Favor, refrescar la página y volver a intentar.");
        }

        if (response.status === Estados.estadoErrorServidores) {
          alert("Ocurrió un error al modificar los datos.");
        }

        return response.data
      }).catch(function (error) {
        console.log("Error put edit timbrado:", error.response);
        return error.response;
      });

    if (promise) {
      setIsLoading(true);
      await promise;
    } else {
      setIsLoading(false);
    }

    if (Estados.estadoExitoso || Estados.estadoSinContenido || Estados.estadoErrorServidores || Estados.estadoTiempoDeEspera) {
      setIsLoading(false);
    }

    const result = await promise;
    return result;
  }

  const handleChangeTimbradoPut = (e: any) => {
    setDatosTimbradoPut({
      ...datosTimbradoPut,
      [e.target.name]: e.target.value
    })
  }

  const handleChangeFechaVencimiento = (e: React.ChangeEvent<HTMLInputElement>) => {
    const today = new Date().toISOString().split('T')[0];
    if (e.target.value < today) {
      abrirModalIngresarFechaCorrecta();
      e.target.value = "";
      return false;
    } else {
      setDatoTimbradoFechaVencimiento({
        ...datoTimbradoFechaVencimiento,
        [e.target.name]: e.target.value
      })
    }
  }

  const soloNumeros = (e: any) => {
    const key = e.key;
    if (!/[0-9]/.test(key) && key !== "ArrowLeft" && key !== "ArrowRight" && key !== "Delete" && key !== "Backspace" && key !== "Tab") {
      e.preventDefault();
    }
  }

  const editarTimbrado = async (e: any) => {
    e.preventDefault();
    if (numeroTimbrado.length < 8) {
      abrirModalValidacionTimbrado();
      setIsLoading(false);
      return false;
    }
    if (!fechaVencimiento) {
      abrirModalAgregarFechaVencimiento();
      setIsLoading(false);
      return false;
    } else {
      await putModificarTimbrado();
    }
    console.log("datosTimbradoPut.statusTimbrado:", datosTimbradoPut.statusTimbrado);
    if (datosTimbradoPut.statusTimbrado === Estados.estadoExitoso.toString()) {
      abrirModalValidacionExitosa();
    } else {
      abrirModalValidacionError();
      setIsLoading(false);
      return false;
    }
  }

  return (
    <div className='w-100'>
      <Form>
        <div className='d-flex row'>
          <Form.Group className='d-flex flex-column col'>
            <Form.Label>Número timbrado</Form.Label>
            <Form.Control type="text" minLength={0} maxLength={8} className='form-control mb-2 w-100' placeholder='Número Timbrado' name="numeroTimbrado" defaultValue={state.numeroTimbrado || datosTimbradoPut.numeroTimbrado} onChange={handleChangeTimbradoPut} onKeyDown={soloNumeros} />
            {numeroTimbrado.length > 8 ? <span className='text-danger'>Timbrado admite sólo hasta 8 números.</span> : ""}
          </Form.Group>
          <Form.Group className='d-flex flex-column col'>
            <Form.Label>Fecha de Vencimiento</Form.Label>
            <Form.Control type="date" className='form-control mb-2 w-100' name="fechaVencimiento" defaultValue={state.fechaVencimiento != null ? state.fechaVencimiento.slice(0, -9) : state.fechaVencimiento || datoTimbradoFechaVencimiento.fechaVencimiento} onChange={handleChangeFechaVencimiento} />
          </Form.Group>
        </div>
        {isLoading ?
          <div className='text-center mx-4 my-4'><Loader /></div>
          : ""}
        <div className="modal-footer d-flex justify-content-center">
          <Button variant="secondary" onClick={cerrar}>Cerrar</Button>
          <Button variant="primary" onClick={editarTimbrado}>Confirmar</Button>
        </div>
      </Form>
      {<ModalValidacionAltaProveedor show={modalValidacion} onClose={cerrarModalValidacion} message={mensajeValidacion} />}
      {<ModalValidacionExitosaEditarTimbrado show={validacionExitosa} onClose={cerrarModalValidacionExitosa} message={mensajeValidacion} />}
    </div>
  )
}

export default CampoTimbrado;