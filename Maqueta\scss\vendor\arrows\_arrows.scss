/*
 
  █████╗ ██████╗ ██████╗  ██████╗ ██╗    ██╗███████╗
 ██╔══██╗██╔══██╗██╔══██╗██╔═══██╗██║    ██║██╔════╝
 ███████║██████╔╝██████╔╝██║   ██║██║ █╗ ██║███████╗
 ██╔══██║██╔══██╗██╔══██╗██║   ██║██║███╗██║╚════██║
 ██║  ██║██║  ██║██║  ██║╚██████╔╝╚███╔███╔╝███████║
 ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝  ╚══╝╚══╝ ╚══════╝
 https://codepen.io/andrewjtait/pen/ejKHI

 // @include arrow(down, 50px, black);
 // @include arrow(up, 20px, black); 
 // @include arrow(left, 10px, black);
 // @include arrow(right, 5px, black);                                                   
 
*/

@mixin arrow($direction, $size, $color) {
    content: ""; // ensures the arrows are visible
  
    // ensures the size of the arrows is correct:
    width: 0;
    height: 0;
  
    // Lists for positions/directions
    $directions: ('down', 'left', 'up', 'right');
    $positions: ('top', 'right', 'bottom', 'left');
  
    // Loop through each position
    @each $position in $positions {
      // Calculate the index of the position in the list
      $index: index($positions, $position);
  
      // If the position matches the direction, render a colored border
      @if nth($directions, $index) == $direction {
        border-#{$position}: $size solid $color;
      } @else {
        border-#{$position}: $size solid transparent;
      }
    }
}
  