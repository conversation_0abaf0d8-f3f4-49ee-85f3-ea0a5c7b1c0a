/// <reference types="webpack/module" />

import React from "react";
import { Usuario } from "../entities/usuario";
import { getUsuarioDeContexto } from '../services/usuarioServices'
import BuscarClientes from "./BuscarClientes";
import { DatosProvider } from "./contextProvider/datosProveedorContext";

'use strict'

export class CabeceraIdentificador extends React.Component<any, { name: string, valorAtributo: any }> {

  imgURL: string;

  constructor(props: any, valorAtributo: any) {
    super(props);
    this.state = { name: '', valorAtributo: '' }
    this.imgURL = "https://w7.pngwing.com/pngs/81/570/png-transparent-profile-logo-computer-icons-user-user-blue-heroes-logo-thumbnail.png"
    valorAtributo = valorAtributo
  };

  async renderUsuario() {
    try {
      let _usuario: Usuario;
      _usuario = await getUsuarioDeContexto()
      this.setState({ name: _usuario.NombreParaMostrar })
    } catch (error) {
      this.setState
    }

  }

  componentDidMount(): void {
    this.renderUsuario()
  }

  render() {
    return (
      <React.StrictMode>
        <DatosProvider>
          <BuscarClientes />
        </DatosProvider>
        <span className="ml-auto navbar-text d-none d-lg-flex align-items-center flex-wrap">
          <figure className="avatar avatar--sm mr-2">
            <img src={this.imgURL} />
          </figure>
          {this.state.name}
        </span>
      </React.StrictMode>
    );
  }


}