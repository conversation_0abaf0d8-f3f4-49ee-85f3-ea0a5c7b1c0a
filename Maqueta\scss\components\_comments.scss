@include block('comment'){
    display: flex;

    @include element('content'){
        margin: 1rem 1.2rem;
        width: 100%;
        border: 1px solid #ccc;
        padding: 20px;
        border-radius: 5px;
        position: relative;

        h5{
            font-size: $font-size-base;
            margin-bottom: 0;
            text-transform: capitalize;
        }

        .text-muted{
            font-size: 0.85rem;
        }

        .charge{
            color: $gray-600;
            font-size: 0.85rem;
            margin-bottom: 5px;
        }
        p{
            margin-bottom: 0;
            text-transform: lowercase;

            &::first-letter{
                text-transform: uppercase;
            }
        }

        &::after {
            content: '';
            display: block;
            position: absolute;
            top: 25px;
            left: -5px;
            width: 10px;
            height: 10px;
            transform: rotate(45deg);
            background: $white;
            border-left: 1px solid $gray-500;
            border-bottom: 1px solid $gray-500;
        }
    }
    @include element('photo'){
        margin-top: 1.2rem;
    }

    @include modifier('owner'){
        flex-flow: row-reverse;
        
        ::after{
            display: none;
        }
        
        .comment__content::before {
            content: '';
            display: block;
            position: absolute;
            top: 25px;
            right: -5px;
            width: 10px;
            height: 10px;
            transform: rotate(45deg);
            background: #fff;
            border-right: 1px solid $gray-500;
            border-top: 1px solid $gray-500;
        }
    }
}