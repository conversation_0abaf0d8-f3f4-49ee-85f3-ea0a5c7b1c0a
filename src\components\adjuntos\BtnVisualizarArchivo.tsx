import React, { useState } from 'react'
import { subirAdjuntos } from '../../services/adjuntosServices';
import ModalAdjuntoNotificacionSeleccionarArchivo from '../modales/ModalAdjuntoNotificacionSeleccionarArchivo';

const BtnVisualizarArchivo = (props: any) => {
  const [pdfFile, setPdfFile] = useState<File | null>(null);
  const [abrirModalNotificacionAdjuntarArchivo, setAbrirModalNotificacionAdjuntarArchivo] = useState(false);

  const abrirModalNotificacionSeleccionarArchivo = () => setAbrirModalNotificacionAdjuntarArchivo(true);
  const cerrarModalNotificacionSeleccionarArchivo = () => setAbrirModalNotificacionAdjuntarArchivo(false);

  const archivoAdjuntado = async (files: FileList) => {
    try {
      await subirAdjuntos(files, 'codigoCliente');
      console.log("SUBIDA EXITOSA DEL ARCHIVO:", files);
    } catch (error) {
      console.error("Error al subir el archivo:", error);
    }
  }

  const verArchivoAdjunto = async () => {

    let archivo = (document.getElementById('subirAdjuntos') as HTMLInputElement);

    console.log("ARCHIVO.FILES:", archivo.files);
    console.log("ARCHIVO.FILES.LENGTH:", archivo.files.length);
    console.log("ARCHIVO.FILES[0]:", archivo.files[0]);

    if (archivo.files.length > 0) {
      window.appGlobal = archivo.files;
      setPdfFile(archivo.files[0]);
      console.log("ADJUNTADO");

      archivoAdjuntado(archivo.files);

      const fileURL: any = URL.createObjectURL(archivo.files[0]);
      window.open(fileURL, '_blank');
    } else {
      abrirModalNotificacionSeleccionarArchivo();
    }
    console.log("window.appGlobal:", window.appGlobal);
  }

  return (
    <div>
      <span className="pull-right hidden-print d-flex align-items-center">
        <button className="btn btn-primary mx-2" onClick={verArchivoAdjunto}>{window.appGlobal ? "Ver Archivo" : "Sin Archivo"}</button>
      </span>
      <ModalAdjuntoNotificacionSeleccionarArchivo abrir={abrirModalNotificacionAdjuntarArchivo} cerrar={cerrarModalNotificacionSeleccionarArchivo} />
    </div>
  )
}

export default BtnVisualizarArchivo

