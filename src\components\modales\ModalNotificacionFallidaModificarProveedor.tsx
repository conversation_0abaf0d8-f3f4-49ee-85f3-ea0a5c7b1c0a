import React from 'react'
import { But<PERSON>, Modal } from 'react-bootstrap';

interface ModalProps {
  abrir: boolean;
  cerrar: () => void;
}

const ModalNotificacionFallidaModificarProveedor: React.FC<ModalProps> = ({ abrir, cerrar }) => {
  return (
    <div>
      <Modal show={abrir} onHide={cerrar}>
        <Modal.Header>
          <Modal.Title className="text-secondary"></Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p className='text-center text-danger font-weight-bold'>No se pudo modificador los datos.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant='secondary' onClick={cerrar}>Cerrar</Button>
        </Modal.Footer>
      </Modal>
    </div>
  )
}

export default ModalNotificacionFallidaModificarProveedor