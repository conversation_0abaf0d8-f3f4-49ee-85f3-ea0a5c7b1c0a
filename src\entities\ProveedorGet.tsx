export type ClienteProveedor =
  {
    "id": number,
    "codigoCliente": string,
    "timbrado": string,
    "tipoDocumento": string,
    "cedula": string,
    "nombre": string,
    "nombreDos": string,
    "apellidoUno": string,
    "apellidoDos": string,
    "tipoPersona": string,
    "direccion": string,
    "telefono": string,
    "ciudad": string,
    "pais": string,
    "tipoProveedor": string,
    "criticidad": string,
    "declaracionJurada": string,
    "fechaDeclaracionJurada": string,
  }

export type ProveedorGet = {
  "id": number,
  "codigoCliente": string,
  "tipoDocumento": string,
  "cedula": string,
  "nombre": string,
  "nombreDos": string,
  "apellidoUno": string,
  "apellidoDos": string,
  "tipoPersona": string,
  "direccion": string,
  "telefono": string,
  "ciudad": string,
  "pais": string,
  "estado": string,
  "tipoProveedor": string,
  "documentoPLD": string,
  "fechaPLD": string
}