/*

███████  ██████  ██████  ███    ███ ██    ██ ██       █████  ██████  ██  ██████      ██████  ███████     ██    ██ ██ ███████ ██ ████████  █████  
██      ██    ██ ██   ██ ████  ████ ██    ██ ██      ██   ██ ██   ██ ██ ██    ██     ██   ██ ██          ██    ██ ██ ██      ██    ██    ██   ██ 
█████   ██    ██ ██████  ██ ████ ██ ██    ██ ██      ███████ ██████  ██ ██    ██     ██   ██ █████       ██    ██ ██ ███████ ██    ██    ███████ 
██      ██    ██ ██   ██ ██  ██  ██ ██    ██ ██      ██   ██ ██   ██ ██ ██    ██     ██   ██ ██           ██  ██  ██      ██ ██    ██    ██   ██ 
██       ██████  ██   ██ ██      ██  ██████  ███████ ██   ██ ██   ██ ██  ██████      ██████  ███████       ████   ██ ███████ ██    ██    ██   ██                                                                                                                                                

*/


// Archivos Base Bootstrap
@import "bootstrap/functions";
@import "bootstrap/variables";
@import "bootstrap/mixins";

// Base & Vendor
@import "base/font-face";
@import "vendor/bemify/bemify";

// Variables propias sobreescribe el reboot
@import "bootstrap/custom-variables";
@import "bootstrap/reboot";

// Componentes bootstrap que utilizamos
@import "bootstrap/progress";



.main-title{
    padding: 1rem 0 .5rem;
    border-top: solid 1px lighten($color: $blue, $amount: 50); 
    border-bottom: solid 1px lighten($color: $blue, $amount: 50);  
    
    h1{
        font-size: 1.5rem;
        line-height: 2rem;
    }
}


// TABLE 

table {
    border: 1px solid #ccc;
    border-collapse: collapse;
    margin: 0;
    padding: 0;
    width: 100%;
    table-layout: fixed;
  }
  
  table caption {
    font-size: 1.5em;
    margin: .5em 0 .75em;
  }
  
  table tr {
    background-color: #f8f8f8;
    border: 1px solid #ddd;
    padding: .35em;
  }
  
  table th,
  table td {
    padding: .625em;
    text-align: center;
  }
  
  table th {
    font-size: .85em;
    text-transform: uppercase;
  }
  
  @media screen and (max-width: 600px) {
    table {
      border: 0;
    }
  
    table caption {
      font-size: 1.3em;
    }
    
    table thead {
      border: none;
      clip: rect(0 0 0 0);
      height: 1px;
      margin: -1px;
      overflow: hidden;
      padding: 0;
      position: absolute;
      width: 1px;
    }
    
    table tr {
      border-bottom: 3px solid #ddd;
      display: block;
      margin-bottom: .625em;
    }
    
    table td {
      border-bottom: 1px solid #ddd;
      display: block;
      font-size: .8em;
      text-align: right;
    }
    
    table td::before {
      /*
      * aria-label has no advantage, it won't be read inside a table
      content: attr(aria-label);
      */
      content: attr(data-label);
      float: left;
      font-weight: bold;
      text-transform: uppercase;
    }
    
    table td:last-child {
      border-bottom: 0;
    }
  }

  .modal-footer{
    justify-content: center;
    
  }

  /* TEXTAREA AUTO RESIZE STYLE */
  .txta {
    width: 100%;
    //max-width: 500px;
    min-height: 100px;
    overflow: hidden;
    line-height: 1.4;
  }
