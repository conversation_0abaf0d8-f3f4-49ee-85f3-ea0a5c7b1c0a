/*
 
 ██╗  ██╗███████╗ █████╗ ██████╗ ███████╗██████╗       ███████╗███████╗██████╗ ██╗   ██╗██╗ ██████╗██╗ ██████╗ ███████╗
 ██║  ██║██╔════╝██╔══██╗██╔══██╗██╔════╝██╔══██╗      ██╔════╝██╔════╝██╔══██╗██║   ██║██║██╔════╝██║██╔═══██╗██╔════╝
 ███████║█████╗  ███████║██║  ██║█████╗  ██████╔╝█████╗███████╗█████╗  ██████╔╝██║   ██║██║██║     ██║██║   ██║███████╗
 ██╔══██║██╔══╝  ██╔══██║██║  ██║██╔══╝  ██╔══██╗╚════╝╚════██║██╔══╝  ██╔══██╗╚██╗ ██╔╝██║██║     ██║██║   ██║╚════██║
 ██║  ██║███████╗██║  ██║██████╔╝███████╗██║  ██║      ███████║███████╗██║  ██║ ╚████╔╝ ██║╚██████╗██║╚██████╔╝███████║
 ╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝╚═════╝ ╚══════╝╚═╝  ╚═╝      ╚══════╝╚══════╝╚═╝  ╚═╝  ╚═══╝  ╚═╝ ╚═════╝╚═╝ ╚═════╝ ╚══════╝
 - Estilos el header de servicios                                                                                                                       
 
*/

@include block('header-secundario') {
    
    padding: ($spacer * 1.2) 0.1rem;
    box-shadow: $box-shadow-sm;

    @include element('logo') {
        
        max-width: 150px;
        // A partir de 992px
        @include media-breakpoint-up(lg) {
            max-width: 170px;
        }
    }

}//HeaderPpa

.mobile-menu{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    position: fixed;
    width: 100%;
    height: 100vh;
    top: 0;
    right: -100vh;
    background-color: #fff;
    transition: .2s ease;
    z-index: 10;
    padding: 15px;

    &.mobile-menu--open{
        right: 0;
    }
}