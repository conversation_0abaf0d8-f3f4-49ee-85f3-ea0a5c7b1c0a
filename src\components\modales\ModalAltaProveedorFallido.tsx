import React from 'react';
import { <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap';

interface ModalProps {
  abrir: boolean;
  cerrar: () => void;
  mensaje?: string;
}

const ModalAltaProveedorFallido: React.FC<ModalProps> = ({ abrir, cerrar, mensaje }) => {
  const mensajeAMostrar = mensaje || "No se pudo dar de alta.";
  
  return (
    <div>
      <Modal show={abrir} onHide={cerrar}>
        <Modal.Header className='d-flex justify-content-between align-items-center'>
          <Modal.Title className='text-secondary'></Modal.Title>
          <Button variant='danger' onClick={cerrar}>x</Button>
        </Modal.Header>
        <Modal.Body>
          <p className='text-center font-weight-bold text-danger'>{mensajeAMostrar}</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={cerrar}>Cerrar</Button>
        </Modal.Footer>
      </Modal>
    </div>
  )
}

export default ModalAltaProveedorFallido