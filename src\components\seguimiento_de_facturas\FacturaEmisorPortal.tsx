/// <reference types="webpack/module" />

import React, { useState, useEffect } from "react";
import { Dispatch, useProveedor } from "../contextProvider/datosProveedorContext";
import { getDestinoFactura } from "../../comm/apiProveedorFactura";

export const FacturaEmisorPortal = ({ handler }: { handler: Dispatch }) => {

  const { state } = useProveedor();

  const [datoTipoComprobante, setDatoTipoComprobante] = useState(null);

  const [areas, setAreas] = useState([]);
  const [selectedArea, setSelectedArea] = useState("");
  const [departamentos, setDepartamentos] = useState([]);

  const [departamentoSeleccionado, setDepartamentoSeleccionado] = useState(null);

  const handleTipoFacturaChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setDatoTipoComprobante({
      ...datoTipoComprobante,
      [e.target.name]: e.target.value
    })
    console.log("e.target.value handleTipoFacturaChange:", e.target.value);
  }

  useEffect(() => {
    const fetchAreas = async () => {
      try {
        const id = await getDestinoFactura();
        setAreas(id);
        state.datosDepartamentos = id?.flatMap((item: any) => item.departamentos) || [];
      } catch (error) {
        console.error('Error fetching areas:', error);
      }
    };
    fetchAreas();
  }, []);

  const handleAreaChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedOption: any = event.target.value;
    const selectedArea = areas?.find((area: any) => area.codigo === Number(selectedOption));
    let codigoArea = selectedArea?.codigo || '';
    localStorage.setItem('codigoArea', codigoArea);
    const areaDescription = selectedArea?.descripcionArea || '';
    setSelectedArea(areaDescription);
    const filteredDepartamentos = state.datosDepartamentos?.filter(
      (departamento: any) => departamento.codigoArea === Number(selectedOption)
    ) || [];
    setDepartamentos(filteredDepartamentos);
    state.selectedArea = selectedArea.descripcion;
    handler('datos');
    localStorage.setItem("areaSeleccionada", selectedArea.descripcion);
  };

  const handleChangeDepartamento = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const depSeleccionado = event.target.value;
    setDepartamentoSeleccionado({
      ...departamentoSeleccionado,
      [event.target.name]: depSeleccionado
    });
    localStorage.setItem("depSeleccionado", depSeleccionado);
    const depCodSeleccionado = departamentos?.find((e: any) => e.descripcion === depSeleccionado)?.codigo;
    localStorage.setItem('codigoDepartamento', depCodSeleccionado);
    console.log("depSeleccionado:", depSeleccionado);
  }

  return (
    <address className="m-t-5 m-b-5">
      <strong className="text-inverse"></strong>
      <div className="d-flex">
        Pais: <div className="text-success ml-2">{state.pais || state.bandejaPais || ""}</div>
      </div>
      <div className="d-flex flex-wrap">
        Telefono: <div className="text-success ml-2">{state.telefono || state.bandejaTelefono || ""}</div>
      </div>
      <div className="d-flex align-items-center flex-wrap">
        Número de documento: <div className="text-success ml-2">{state.numeroDocumento || state.bandejaNroDocumento || ""}</div>
      </div>
      <div className="d-flex align-items-center">
        <div className="font-weight-bold">Estado Perfil:</div>
        {
          state.respuestaEstadoPerfilMensaje === "200" ?
            state.estadoPerfilMensaje === "NO" ? <div className="ml-2" style={{ color: "#198754" }}>ACTIVO</div> :
              state.estadoPerfilMensaje === "SI" ? <div className="text-danger ml-2">VENCIDO</div> : "" : ""
        }
      </div>
      <div className="d-flex align-items-center">
        <div className="font-weight-bold">Documento Vencido:</div>
        {state.DVTipoDocumento ?
          <div className="text-primary ml-2">{state.DVTipoDocumento || ""}</div> :
          <div></div>}
      </div>
      <div className="">
        {
          state.bandejaTipoComprobante ?
            <select id="tipoComprobante" name="tipoComprobante" className="custom-select my-2 mt-4" value={state.bandejaTipoComprobante} onChange={handleTipoFacturaChange} disabled>
              <option value="">TIPO COMPROBANTE</option>
              <option value="BOV">BOLETA DE VENTA</option>
              <option value="FAC">FACTURA</option>
              <option value="FAV">FACTURA VIRTUAL</option>
              <option value="FAE">FACTURA ELECTRONICA</option>
              <option value="ING">COMPROBANTE DE INGRESO ENTIDAD PÚBLICA, RELIGIOSAS O DE BENEFICIO PÚBLICO</option>
              <option value="REC">RECIBO</option>
              <option value="">PARAMETRICA DE FECHA</option>
              <option value="">AUTOFACTURAS</option>
              <option value="">DESPACHO</option>
              <option value="">NOTA DE CRÉDITO</option>
              <option value="">NOTA DE DÉBITO</option>
              <option value="">parametrica de fecha en extractos</option>
            </select>
            :
            <select id="tipoComprobante" name="tipoComprobante" className="custom-select my-2 mt-4" defaultValue={datoTipoComprobante} onChange={handleTipoFacturaChange}>
              <option value="">TIPO COMPROBANTE</option>
              <option value="BOV">BOLETA DE VENTA</option>
              <option value="FAC">FACTURA</option>
              <option value="FAV">FACTURA VIRTUAL</option>
              <option value="FAE">FACTURA ELECTRONICA</option>
              <option value="ING">COMPROBANTE DE INGRESO ENTIDAD PÚBLICA, RELIGIOSAS O DE BENEFICIO PÚBLICO</option>
              <option value="REC">RECIBO</option>
              <option value="">PARAMETRICA DE FECHA</option>
              <option value="">AUTOFACTURAS</option>
              <option value="">DESPACHO</option>
              <option value="">NOTA DE CRÉDITO</option>
              <option value="">NOTA DE DÉBITO</option>
              <option value="">parametrica de fecha en extractos</option>
            </select>
        }
        <div>
          {
            state.descripcionAreaBandeja ?
              <select
                name="area"
                id="area"
                defaultValue=""
                className="custom-select my-2"
                onChange={handleAreaChange}
                disabled
              >
                <option value="">SELECCIONE AREA</option>
                {areas?.map((item: any) => (
                  <option key={item.codigo} value={item.codigo} selected={state.descripcionAreaBandeja === item.descripcion}>
                    {item.descripcion}
                  </option>
                ))}
              </select> :
              <select
                name="area"
                id="area"
                defaultValue=""
                className="custom-select my-2"
                onChange={handleAreaChange}
              >
                <option value="">SELECCIONE AREA</option>
                {areas?.map((item: any) => (
                  <option key={item.codigo} value={item.codigo} selected={state.descripcionAreaBandeja === item.descripcion}>
                    {item.descripcion}
                  </option>
                ))}
              </select>
          }
          {
            state.descripcionDepartamentoBandeja ?
              <select
                name="departamento"
                id="departamento"
                className="custom-select my-2"
                onChange={handleChangeDepartamento}
                disabled
              >
                {departamentos && departamentos.length > 0 ? (
                  <>
                    <option value="">SELECCIONE DEPARTAMENTO</option>
                    {departamentos.map((departamento: any) => (
                      <option key={departamento.codigo} value={departamento.descripcion}>
                        {departamento.descripcion}
                      </option>
                    ))}
                  </>
                ) : (
                  <option value={state.descripcionDepartamentoBandeja || ""}>
                    {state.descripcionDepartamentoBandeja || "SELECCIONE DEPARTAMENTO"}
                  </option>
                )}
              </select> :
              <select
                name="departamento"
                id="departamento"
                className="custom-select my-2"
                onChange={handleChangeDepartamento}
              >
                {departamentos && departamentos.length > 0 ? (
                  <>
                    <option value="">SELECCIONE DEPARTAMENTO</option>
                    {departamentos.map((departamento: any) => (
                      <option key={departamento.codigo} value={departamento.descripcion}>
                        {departamento.descripcion}
                      </option>
                    ))}
                  </>
                ) : (
                  <option value={state.descripcionDepartamentoBandeja || ""}>
                    {state.descripcionDepartamentoBandeja || "SELECCIONE DEPARTAMENTO"}
                  </option>
                )}
              </select>
          }
        </div>
      </div>
    </address>
  )
}