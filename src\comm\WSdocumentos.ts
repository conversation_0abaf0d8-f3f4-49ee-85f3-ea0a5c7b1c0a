import { Ambiente } from "../entities/Enums/enumAmbientes";
import { TipoDocumentos } from "../entities/Enums/enumTipoDocumentos";
import { getAmbienteContexto } from "../utilities/contextInfo";
import { xml2Json3 } from "../utilities/Parsers";
import axios from "axios";
import { OnSuccessCarpetaDigital } from "./CarpetaDigitalWS";
import { getUsuarioDeContexto } from "../services/usuarioServices";

import { getSPWebAbsoluteUrl } from "../utilities/_spPageContextInfo";
import { Estados } from "../entities/Enums/estadosRespuesta";

export async function hadoop(files: FileList) {
  console.log("files en hadoop:", files);
  for (let i = 0; i < files.length; i++) {
    const FormData = require('form-data');
    let data = new FormData();

    data.append('file', files[i]);
    console.log("files[i]:", files[i]);

    let urlHadoop = getUrlApiHadoop();
    let urlCarga = urlHadoop.replace("{accion}", "upload?path_images=");

    let config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: urlCarga,
      headers: {},
      data: data
    };

    console.log("config post hadoop:", config);
    console.log("config.url post hadoop:", config.url);

    try {
      const response = await axios.request(config);
      console.log("response hadoop:", response);
      console.log("response.status hadoop:", response.status);
      if (response.status === Estados.estadoExitoso) {
        await insertIntoCarpetaDigital("", "", response.data.loc);
      }
    } catch (error) {
      console.log("catch error hadoop:", error);
    }
  }
}

export const prepararAdjuntos = async (files: FileList) => {

  let validacion = ';'
  for (let i = 0; i < files.length; ++i) {

    let esUltimo = i == files.length - 1;
    const file = files[i];

    let extencion = obtenerExtension(file.name);
    if (extencion == "pdf" || extencion == "PDF" ||
      extencion === "XLSX" || extencion === "xlsx" ||
      extencion === "xls" || extencion === "XLS" ||
      extencion === "JPG" || extencion === "jpg" ||
      extencion === "ODS" || extencion === "ods" ||
      extencion === "DOCX" || extencion === "docx") {
      insertFile(file, esUltimo, "");

    } else {
      validacion = validacion + files[i].name + ";";
    }
    if (esUltimo && validacion !== ";") {
      console.log("validacion:", validacion);
    } else if (esUltimo && validacion === ";") {
      console.log("Archivos adjuntados exitosamente.");
    }
  }
}

const obtenerExtension = (fileName: string) => {
  console.log("fileName:", fileName);
  var posicion;
  var extencion = "";
  for (var i = 0; i < fileName.length; i++) {
    if (fileName[i] == ".") {
      posicion = i;
    }
  }
  for (var e = posicion + 1; e < fileName.length; e++) {
    extencion = extencion + fileName[e];
  }
  return extencion;
}

const insertFile = async (file: File, esUltimo: boolean, codigoCliente: string) => {
  console.log("file:", file);
  let trueDate = new Date();
  let path = "digitalizacion_documentos\\" + codigoCliente + "\\" + armarFechaCarpeta() + "\\" + file.name;
  let formData = new FormData();
  formData.append("file", file);
  formData.append("path", path);

  formData.append("usuario", localStorage.getItem('contextNombreUsuario'));

  formData.append("noise", "");
  formData.append("stringResult", "");
  let data = {
    path: path,
    file: file
  }
  const url = getUrlWebService() + "api/nintex/uploadFileNintex";

  console.log("DATA:", data);

  XMLHttpfunctionFormData(url, 'POST', formData, insertIntoCarpetaDigital, onErrorService, data)
}


const armarFechaCarpeta = () => {
  const trueDate = new Date();
  let dia, mes, año;
  if (trueDate.getDate().toString().length == 1) {
    dia = "0" + trueDate.getDate();
  } else {
    dia = trueDate.getDate();
  }

  mes = trueDate.getMonth() + 1;
  año = trueDate.getFullYear();

  let fecha = dia + "" + mes + "" + año;

  return fecha;
}

const XMLHttpfunctionFormData = async (url: string, metodo: string, mensaje: FormData, CallbackSuccess: CallableFunction, CallbackError: CallableFunction, data: {}) => {
  var xhr = new XMLHttpRequest();
  xhr.onloadend = function () {
    if (this.readyState == 4 && xhr.status == 200) {
      console.log(this.responseText);
      CallbackSuccess(this.responseXML, data);
    } else {
      CallbackError(this.responseXML);
      console.log(this.responseText);
    };
  };
  xhr.open(metodo, url, true);

  xhr.send(mensaje);
}

const getUrlWebService = () => {

  if (getAmbienteContexto() === Ambiente.dev) {

    return "https://srvfuente-evo.bancontinental.com.py:5005/";


  } else {
    return "https://nintexservices.bancontinental.com.py:5006/";
  }
}

function getUrlApiHadoop() {
  if (getSPWebAbsoluteUrl().includes("dev-intranet")) {
    return "https://desa-docker01.bancontinental.com.py:8200/{accion}/datalake/Continental-desa/Aprovisionamiento/Datos_no_estruturados/Procesos_Digitales"
  } else {
    return "https://apihadoop.bancontinental.com.py:8300/{accion}/datalake/Continental/Aprovisionamiento/Datos_no_estruturados/Procesos_Digitales"
  }
}

export function obtenerHadoopDoc(params: string) {
  const ultimaBarraIndex = params.lastIndexOf("/");
  const tituloArchivo = params.substring(ultimaBarraIndex + 1);
  console.log("PARAMS:", params);

  let partes = params.split("Procesos_Digitales");
  if (partes.length > 1) {
    let directorioArchivo = partes[1];
    console.log("directorioArchivo:", directorioArchivo);

    let raw = "";
    let requestOptions: any = {
      method: 'GET',
      redirect: 'follow'
    };
    let urlDesc = getUrlApiHadoop();
    let urlDescarga = urlDesc.replace("{accion}", "download?downloadpath=");
    console.log("url true", urlDescarga)
    console.log("urlDescarga + params:", urlDescarga + directorioArchivo);
    fetch(urlDescarga + directorioArchivo, requestOptions)
      .then(response => response.json())
      .then(result => { OnSuccessCarpetaDigital(result.loc, tituloArchivo), console.log("result.loc:", result.loc) })
      .catch(error => console.log('error catch obtenerHadoopDoc:', error));
  } else {
    console.log("La cadena no contiene 'Procesos_Digitales'");
  }
}

export const insertIntoCarpetaDigital = async (xml: any, data: any, path: string) => {
  console.log("data:", data);
  let _codigoClienteProveedor = localStorage.getItem('codigoClienteProveedor');
  let _codigoOperacion = localStorage.getItem('operacionSeguimiento');
  console.log("_codigoOperacion:", _codigoOperacion);
  let _tresLetras = (await getUsuarioDeContexto()).TresLetras;
  console.log("TRES LETRAS:", _tresLetras);
  let trueDate = new Date();
  let d = trueDate.getMonth();
  if (d == 0) {
    d = d + 1;
  }

  let url = getUrlWebService() + "api/nintex/PostDigitalizarDocumento";

  console.log("url getUrlApiHadoop:", url);

  console.log("xml:", xml);

  console.log("path:", path);

  const ultimaBarraIndex = path.lastIndexOf("/");
  const textoSobrante = path.substring(ultimaBarraIndex + 1);

  let mensaje = '<?xml version ="1.0" encoding="UTF-8"?>' +
    '<Creditos.ArchivoCP>' +
    '<codigoDocumento>0</codigoDocumento>' +
    '<codRespuesta>0</codRespuesta>' +
    '<pi_path>' + path + '</pi_path>' +
    '<pi_tipodoc>' + TipoDocumentos.genericosFacuturas + '</pi_tipodoc>' +
    '<pi_rutaDoc>0</pi_rutaDoc>' +
    '<pi_fechaDoc>29/03/2022</pi_fechaDoc>' +
    '<pi_descriCarp>' + textoSobrante + '</pi_descriCarp>' +
    '<pi_cliente>' + _codigoClienteProveedor + '</pi_cliente>' +
    '<pi_cliente>' + "0" + '</pi_cliente>' +
    '<pi_producto>7</pi_producto>' +
    '<pi_subproducto>146</pi_subproducto>' +
    '<pi_temporal>NO</pi_temporal>' +
    '<pi_comentario>Adjunto desde la bandeja</pi_comentario>' +
    '<pi_cantidad_oper>0</pi_cantidad_oper>' +
    '<pi_oper>' + _codigoOperacion + '</pi_oper>' +
    '<pi_user>' + _tresLetras + '</pi_user>' +
    '</Creditos.ArchivoCP>'
  console.log("xml insert", mensaje);
  await XMLHttpfunction(url, "POST", mensaje, successInsertCarpetaDigital, onErrorService, true);
}

function onErrorService(data: any) {
  console.log("error", data);
  console.log("xml a json", xml2Json3(data));

}

const successInsertCarpetaDigital = async (data: any) => {
  console.log("data:", data);
}

const XMLHttpfunction = async (url: string, metodo: string, mensaje: string, CallbackSuccess: CallableFunction, CallbackError: CallableFunction, asincrono: boolean) => {
  var xhr = new XMLHttpRequest();
  xhr.onloadend = function () {
    if (this.readyState == 4 && xhr.status == 200) {
      CallbackSuccess(this.responseXML);
    } else {
      CallbackError(this.responseXML);
      console.log(this.responseText);
    };
  };
  console.log("asincrono: ", asincrono);
  xhr.open(metodo, url, asincrono);
  xhr.setRequestHeader('Content-Type', 'text/xml');
  xhr.send(mensaje);
};

export const bajarArchivo = async (path: string) => {
  console.log("path bajar archivo:", path);
  const _url = getUrlWebService() + 'api/nintex/postBajarArchivoPASV2'
  const _axios = axios.create({})
  const _data = '<?xml version="1.0" encoding="UTF-8"?><Pas.DocumentosByte><pathEntrada>' + path + '</pathEntrada></Pas.DocumentosByte>';

  const config =
  {
    method: 'POST',
    url: _url,
    headers: { 'Access-Control-Allow-Origin': '*', 'Content-Type': 'text/xml' },
  }

  _axios.post<any, any>(config.url, _data, config)
    .then(function (response) {
      console.log("response post bajarArchivo:", response)
      OnSuccessCarpetaDigital(response.data)
    })
    .catch(function (error) {
      console.log("Error catch post bajarArchivo:", error);
      return error.response
    });

}

export const obtenerHadoopDocBase64 = async (params: string): Promise<string | null> => {
  const ultimaBarraIndex = params.lastIndexOf("/");
  const tituloArchivo = params.substring(ultimaBarraIndex + 1);
  // console.log("PARAMS:", params);

  let partes = params.split("Procesos_Digitales");
  if (partes.length > 1) {
    let directorioArchivo = partes[1];
  // console.log("directorioArchivo:", directorioArchivo);

    let requestOptions: any = {
      method: 'GET',
      redirect: 'follow'
    };
    
    let urlDesc = getUrlApiHadoop();
    let urlDescarga = urlDesc.replace("{accion}", "download?downloadpath=");
    // console.log("url true", urlDescarga)
    // console.log("urlDescarga + params:", urlDescarga + directorioArchivo);
    
    try {
      const response = await fetch(urlDescarga + directorioArchivo, requestOptions);
      const result = await response.json();
      
      if (result && result.loc) {
        return result.loc; // Devuelve directamente el base64
      } else {
        console.error("No se encontró el contenido base64 en la respuesta");
        return null;
      }
    } catch (error) {
      console.log('error catch obtenerHadoopDocBase64:', error);
      return null;
    }
  } else {
    console.log("La cadena no contiene 'Procesos_Digitales'");
    return null;
  }
}
