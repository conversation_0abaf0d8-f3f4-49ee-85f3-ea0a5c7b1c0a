import React, { useState, useEffect } from 'react';
import { getAmbienteContexto, getUrlBase } from '../../utilities/contextInfo';
import axios from 'axios';
import EndPoint from '../../comm/EndPoint.Config';
import { getApiProveedorFacturaHeaders } from '../../comm/contracts/ApiBpmPoliticasProcesosRequestHeaders';
import { useProveedor } from '../contextProvider/datosProveedorContext';
import { Acciones } from '../../entities/Acciones';
import { JerarquiaDeEstado } from '../../entities/JerarquiaDeEstado';

type ComentariosType = [
  {
    idAccionGenerada: number;
    codigoAccion: string;
    accion: string;
    estado: string;
    fechaHoraCreacion: string;
    fechaHoraAccion: string;
    usuarioDominio: string;
    departamento: string;
    codigoRol: string;
    rol: string;
    atributos: [
      {
        idAccionGenerada: number;
        codigoAtributo: string;
        valor: string;
      }
    ]
  }
]

const Comentarios = () => {

  const { state } = useProveedor();

  let jsxEl: JSX.Element[];
  const [jsx, setJsx] = useState(jsxEl);

  const Params = {
    urlBase: getUrlBase(),
    ambiente: getAmbienteContexto(),
    request: axios.create(),
    endpoints: EndPoint.apiBpmPoliticaProcesos,
  }

  let operacionNro = localStorage.getItem("operacionNumero");

  const getComentarios = async () => {

    let _cacheControl = 'max-age=60*60*10, private';
    let _headers = await getApiProveedorFacturaHeaders(_cacheControl);
    let _sturlEndpoint = Params.endpoints.vUno.get.acciones;
    let urlEndpoint = _sturlEndpoint.replace("${idCabecera}", `${state.operacionClienteNumero || state.numeroOperacionCliente}`);
    let filtroComentario = "?filtroDatos=ATCOME";

    let config = {
      method: "get",
      url: Params.urlBase + urlEndpoint + filtroComentario,
      headers: _headers
    }

    let promise = Params.request<ComentariosType>(config)
      .then(function (response) {
        return response.data;
      }).catch(function (error) {
        console.log("Error getComenntarios:", error);
      });

    let result = await promise;
    return result;
  }

  const getJSX = (datos: ComentariosType) => {
    let jsx = datos.map((item) => {
      if (item.estado !== "ESTINA") {
        if (item.atributos.find((e: any) => e.codigoAtributo === "ATCOME")) {
          const fechaOriginal = new Date(item.fechaHoraAccion);
          const dia = fechaOriginal.getDate().toString().padStart(2, '0');
          const mes = (fechaOriginal.getMonth() + 1).toString().padStart(2, '0');
          const anio = fechaOriginal.getFullYear();
          const hora = item.fechaHoraAccion.slice(11, 19);
          const fechaFormateada = `${dia}/${mes}/${anio} ${hora}`;
          return (
            <div className='border rounded p-3 my-4' key={item.idAccionGenerada}>
              <div className='d-flex justify-content-between align-items-center'>
                <div className='font-weight-bold'>{item.usuarioDominio}</div>
                <div className='ml-2 text-secondary'>{fechaFormateada}</div>
              </div>
              <div className='text-secondary'>{item.departamento}</div>
              <div>{item.atributos ? <div>{item.atributos.map((i: any) => i.valor)}</div> : ""}</div>
            </div>
          )
        }
      }
    })
    return jsx;
  }

  useEffect(() => {
    getComentarios().then(result => {
      const sortedData = result as Acciones;
      sortedData.sort((a, b): any => {
        const jerarquia: JerarquiaDeEstado = {
          "ESTAPR": 1,
          "ESTREC": 1,
          "ESTPEN": 2,
          "ESTESP": 3
        };

        if (jerarquia[a.estado] - jerarquia[b.estado] === 0) {
          return a.idAccionGenerada - b.idAccionGenerada;
        }
        return jerarquia[a.estado] - jerarquia[b.estado];
      });
      setJsx(getJSX(sortedData));
    })
  }, []);

  return (
    <div className=''>
      <div className='text-primary text-center font-weight-bold mt-4'>COMENTARIOS</div>
      {jsx}
    </div>
  )
}

export default Comentarios;