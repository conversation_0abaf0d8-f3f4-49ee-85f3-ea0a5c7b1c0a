/*
 
 ██╗      ██████╗  ██████╗ ██╗███╗   ██╗
 ██║     ██╔═══██╗██╔════╝ ██║████╗  ██║
 ██║     ██║   ██║██║  ███╗██║██╔██╗ ██║
 ██║     ██║   ██║██║   ██║██║██║╚██╗██║
 ███████╗╚██████╔╝╚██████╔╝██║██║ ╚████║
 ╚══════╝ ╚═════╝  ╚═════╝ ╚═╝╚═╝  ╚═══╝
 
*/ 


// Archivos Base Bootstrap
@import "bootstrap/functions";
@import "bootstrap/variables";
@import "bootstrap/mixins";

// Base & Vendor
// Fonts utilizados - Librerias
@import "base/font-face";
@import "vendor/bemify/bemify";

// Variables propias sobreescribe el reboot
@import "bootstrap/custom-variables";
@import "bootstrap/reboot";

// Componentes bootstrap que utilizamos
@import "bootstrap/type";
@import "bootstrap/forms";
@import "bootstrap/custom-forms";
@import "bootstrap/buttons";
@import "bootstrap/transitions";
@import "bootstrap/input-group";
@import "bootstrap/nav";


// Utilidades
@import "bootstrap/utilities";

// Componentes con mixins propios
@import "components/labels";
@import "components/brand";
@import "components/close-icon";

// Page
@import "pages/page-login";

