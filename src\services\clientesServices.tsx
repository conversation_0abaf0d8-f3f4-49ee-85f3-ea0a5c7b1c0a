import axios, { AxiosRequestConfig } from 'axios';
import EndPoint from '../comm/EndPoint.Config';
import { getPrivilegeHeaders } from '../comm/privilegeEnv';
import { getUrlBase } from '../utilities/contextInfo';
import { ToastType, showToast } from '../utilities/toastUtils';
 
class ClientesServices {
    static readonly getDatosBasicosPorNroDocumento: any;

    async getDatosBasicosPorNroDocumento(nroDocumento: string) {
        let urlBase = getUrlBase();
        const url = `${urlBase}${EndPoint.apiClientesConsultasInterno.vUno.get.datosBasicosPorNroDocumento.replace(':nroDocumento', nroDocumento)}`;

        try {
            let response;

            let privilegeHeaders = await getPrivilegeHeaders();

            const config: AxiosRequestConfig = {
                headers: privilegeHeaders
            };

            response = await axios.get(url, config);
            return response?.data ?? {};
        } catch (error) {
            showToast(
                "Error al consultar los datos básicos del cliente.",
                ToastType.Error
            )
            console.error('Error al consultar los datos básicos del cliente:', error);
            throw error;
        }
    }
}

export default ClientesServices;