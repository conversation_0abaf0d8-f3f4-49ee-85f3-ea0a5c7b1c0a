import React from 'react';
import { <PERSON><PERSON>, But<PERSON> } from 'react-bootstrap';

interface ModalProps {
  show: boolean;
  onClose: () => void;
}

const ModalValidacionCamposBuscador: React.FC<ModalProps> = ({ show, onClose }) => {
  return (
    <div>
      <Modal show={show} onHide={onClose}>
        <Modal.Header>
          <Modal.Title className='text-secondary'></Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <p className='text-danger text-center font-weight-bold'>Debe completar los campos Número de documento y Tipo Documento.</p>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="primary" onClick={onClose}>
            Aceptar
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  )
}

export default ModalValidacionCamposBuscador;