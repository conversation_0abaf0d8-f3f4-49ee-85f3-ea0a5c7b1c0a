import _ from 'lodash'

function component() {
  const element = document.getElementById("s4-workspace");

  element.innerHTML = _.join(['Hello', 'webpack'], ' ');
  element.innerHTML = "<div><PERSON> fun<PERSON><PERSON></div>"

  return element;
}

document.addEventListener("DOMContentLoaded", (event) => {

  document.body.appendChild(component());
  var nombre = _spPageContextInfo.userDisplayName;
  console.log("hola WebPack");
  console.log(nombre);
})



