const EndPoint = {
  auth: {
    post: {
      tokenInterno: '/autenticarServicio/v1/realms/interno'
    }
  },
  apiBpmPoliticaProcesos: {
    vUno: {
      get: {
        menuElementos: '/bpm/v1/menu/elementos',
        rolesPorUsuario: '/bpm/v1/usuarios/${usuario}/roles',
        operaciones: '/bpm/v1/${proceso}/operacion',  
        listadoAdjuntos: '/bpm/v1/documento/${idTipoDocumento}/${operacion}',
        accionesPorEstado: '/bpm/v1/tareas/${idCabecera}/estado/${codEstado}',
        datoAcciones: '/bpm/v1/datos-acciones/${idAccionGenerada}/${codAtributo}',
        acciones: '/bpm/v1/acciones/${idCabecera}'
      },
      post: {
        inicioSeguimiento: '/bpm/v1/seguimiento/inicio',
        insertarAccionGenerada: '/bpm/v1/insertar/accion-generada',
        accionDatos: '/bpm/v1/insertar/accion-datos'
      },
      put: {
        responderTarea: '/bpm/v1/responder/accion-generada'
      }
    },
    vDos: {
      get: {
        operaciones: '/bpm/v2/${proceso}/operaciones',
      }
    }
  },
  apiProveedorFactura: {
    vUno: {
      get: {
        proveedor: '/proveedor/factura/v1/${codigoCliente}',
        proveedorRegistroFactura: '/proveedor/factura/v1/${tipoDocumento}/${numeroDocumento}/timbrado/${timbrado}/factura/${numeroFactura}',
        facturas: '/proveedor/factura/v1/proveedores/facturas',
        clienteProveedor: '/proveedor/factura/v1/clientes/${numeroDocumento}',
        perfilesVencidos: '/proveedor/factura/v1/${codigoCliente}/perfiles-vencidos',
        documentosVencidos: '/proveedor/factura/v1/${codigoCliente}/documentos-vencidos',
        destinoFactura: '/proveedor/factura/v1/destino-factura'
      },
      post: {
        facturas: '/proveedor/factura/v1/proveedores/facturas',
        proveedor: '/proveedor/factura/v1/proveedores',
        timbrado: '/proveedor/factura/v1/timbrado',
        migracion: '/proveedor/factura/v1/proveedor/{idLote}/facturas/migracion'
      },
      put: {
        timbrado: '/proveedor/factura/v1/timbrado',
        actualizarFactura: '/proveedor/factura/v1/proveedores/facturas/datosita'
      }
    }
  },
  apiClientProveedor: {
    vUno: {
      get: {
        clienteProveedor: '/proveedor/factura/v1/clientes/${codigoCliente}'
      }
    }
  },
  apiUsuario: {
    vUno: {
      get: {
        usuario: '/api-usuarios/v1/usuarios/${usuarioDominio}'
      }
    }
  },
  apiRestSP: {
    siteUser: "/_api/web/siteusers(@v)?@v='"
  },
  apiRRHHUtilitario: {
    vUno: {
      get: {
        areas: '/rrhh/Utilitarios/v1/areas',
        departamentos: '/rrhh/Utilitarios/v1/departamentos',
      }
    }
  },
  apiDivisas: {
    vUno: {
      get: {
        cotizacionId: '/divisas/v1/api/monedas/cotizacion?CodigoDivisa='
      }
    }
  },
  apiPrivilegedCustomerSegmentationEngine: {
    vUno:{
      get:{
        getReglasPrivilege: '/api-motor-privilege-interno/v1/:codigoCliente/reglas',
        getClientesPrivilegeDni: '/api-motor-privilege-interno/v1/clientes/:dni',
        getClientesPrivilegeCodigo: '/api-motor-privilege-interno/v1/clientes/:codigoCliente/obtener-cliente',
        getAdicionalesPrivilege: '/api-motor-privilege-interno/v1/clientes/privilege/:codigoCliente/adicionales',
        getSolicitudesprivilege: '/api-motor-privilege-interno/v1/clientes/privilege/solicitudes/adicionales?estado=:estado',
        getPrivilegeCedula: '/api-motor-privilege-interno/v1/clientes/privilege/documento/:codigoCliente/adicionales',
      },
      post:{
        iniciarSeguimiento: '/api-motor-privilege-interno/v1/privilege/seguimiento',
        evaluarClienteMotorDecisiones: '/api-motor-privilege-interno/v1/:codigoCliente/motor-de-decisiones'
      },
      put: {
        insertaCabeceraBPM: '/api-motor-privilege-interno/v1/clientes/privilege/bpm-cabecera',
        inlcusionAdicionalPrivilege: '/api-motor-privilege-interno/v1/clientes/privilege/:codigoCliente/adicionales/solicitud/inclusion',
        autorizarAdicionalPrivilege: '/api-motor-privilege-interno/v1/clientes/privilege/:codigoCliente/adicionales/autorizar/inclusion',
        exclusionAdicionalPrivilege: '/api-motor-privilege-interno/v1/clientes/privilege/:codigoCliente/adicionales/solicitud/exclusion',
        autorizaExlcusion:'/api-motor-privilege-interno/v1/clientes/privilege/:codigoCliente/adicionales/autorizar/exclusion',
      }
    }
  },
  apiClientesConsultasInterno: {
    vUno: {
      get: {
        datosBasicosPorNroDocumento: '/interno/clientes/datos/v1/datos?numeroDocumento=:nroDocumento',
      }
    }
  },
}

export default EndPoint;
