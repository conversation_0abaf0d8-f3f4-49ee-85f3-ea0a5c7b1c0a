/// <reference types="webpack/module" />

import React, { useMemo, useState } from "react";
import { IlistadoItemFactura } from "../../entities/Intefaces/IListado";
import { listarComentarios } from "../../services/operacionesService";
import { AbrilModalAdjuntos } from "../adjuntos/AbriModalAdjuntos";
import { FacturaListado } from "../seguimiento_de_facturas/FacturasListado";
import { BtnGroupDecision } from "./BtnGroupDecision";
import { getFacturas, postMigracionProveedorFacturas, putActualizarFactura } from "../../comm/apiProveedorFactura";
import { createRoot } from "react-dom/client";
import { Estados } from "../../entities/Enums/estadosRespuesta";
import { useProveedor } from "../contextProvider/datosProveedorContext";
import Loader from "../loader/Loader";

'use strict'

export const OperacionView = (props: any) => {

  const { state } = useProveedor();

  const _lote = props.data.subTitulo.match(/[0-9]*$/)[0];
  const _operacion = props.data.titulo.match(/[0-9]*$/)[0];
  const _listadoItemFactura = props.data as IlistadoItemFactura;

  const [listadoFactura, setListadoFactura] = useState<any>([<></>]);
  const [btnGroupDecision, setBtnGroupDecision] = useState(<></>);

  const [isLoading, setIsLoading] = useState(false);

  useMemo(async () => {
    setIsLoading(true);
    try {
      const _listadoFactura = await FacturaListado(_lote);

      setListadoFactura(_listadoFactura);

      console.log("_listadoItemFactura.idTarea:", _listadoItemFactura.idTarea);

      const _btnDecision = (_listadoItemFactura.idTarea) ? <BtnGroupDecision listadoItemFactura={_listadoItemFactura} btnGroupDecision={btnGroupDecision} /> : <></>
      setBtnGroupDecision(_btnDecision);
      listarComentarios(_operacion)
        .then(() => {
          setTimeout(() => {
            setIsLoading(false);
          }, 100);
        })
        .catch(error => {
          console.error("Error al listar comentarios:", error);
          setIsLoading(false);
        });
      console.log("MOSTRAR");
    } catch (error) {
      console.log("Error catch:", error)
    }
  }, []);

  state.numeroOperacionCliente = _operacion;

  let contextRolCodigo0 = localStorage.getItem("contextRolCodigo0");
  console.log("contextRolCodigo0:", contextRolCodigo0);

  const modalHandlerOk = async () => {
    let idLoteRelacion = localStorage.getItem("idLoteRelacion");
    console.log("idLoteRelacion:", idLoteRelacion);

    await postMigracionProveedorFacturas();

    let datosFactura: any = await getFacturas(parseInt(idLoteRelacion));
    let actualizarDatosFactura: any = [];
    datosFactura.forEach((item: any) => {
      actualizarDatosFactura.push(item.numeroLoteRelacional, item.idCargaRelacional);
    });

    let body = {
      datosFactura: {
        numeroLote: actualizarDatosFactura[0],
        idCarga: actualizarDatosFactura[1],
        estado: 'P',
        estadoCarga: 'P',
        marcaTipoPago: 'PR',
        marcaProvision: 'S',
      },
      codigoModificacion: '1'
    }

    console.log("body:", body);

    let bodyEstado: any = {};
    let opcion = 1;
    let response = await putActualizarFactura(body, bodyEstado, opcion);
    console.log("response:", response);
    if (response.status === Estados.estadoExitoso) {
      let operacionNumero = localStorage.getItem("operacionNumero");
      console.log("operacionNumero:", operacionNumero);
      window.location.reload();
    } else {
      alert("Error al provisionar. Por favor, intente de nuevo.");
      return false;
    }
  }

  const ModalProvisionFactura = () => {
    return (
      <div className="modal fade" id="modalProvisionFactura" tabIndex={-1} aria-labelledby="modalProvisionFacturaLabel" aria-hidden="true">
        <div className="modal-dialog">
          <div className="modal-content">
            <div className="modal-header d-flex align-items-center">
              <h1 className="modal-title fs-5" id="modalProvisionFacturaLabel"></h1>
              <button type="button" className="btn btn-outline-danger" data-dismiss="modal" aria-label="Close">
                &#x2715;
              </button>
            </div>
            <div className="modal-body d-flex flex-column text-center">
              <b>Desea provisionar factura?</b>
            </div>
            <div className="modal-footer">
              <button type="button" className="btn btn-primary" onClick={modalHandlerOk}>Si</button>
              <button type="button" className="btn btn-secondary" data-dismiss="modal">No</button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const abrirModalProvisionarFactura = () => {
    const node = document.createElement("div");
    node.id = "ModalProvisionFactura";
    let _root = document.getElementById("container");
    _root.appendChild(node);
    const _modalEl = document.getElementById("ModalProvisionFactura");
    const _rootModal = createRoot(_modalEl);
    _rootModal.render(<ModalProvisionFactura></ModalProvisionFactura>);
  }

  const provisionarFatura = (e: any) => {
    e.preventDefault();
    abrirModalProvisionarFactura();
    console.log("Provisionar.");
  }

  state.operacionClienteNumero = props.operacion;

  console.log("state.bandejaProvision:", state.bandejaProvision);

  return (
    <>
      {
        isLoading ?
          <div style={{ position: "absolute", top: "50%", left: "50%", right: "50%" }}><Loader /></div>
          :
          <div>
            <div className="px-4 py-3 d-flex justify-content-between bg-white sticky-top">
              <div className="d-flex align-items-center">
                <span 
                  className="pw-close" 
                  onClick={(e) => {
                    e.stopPropagation();
                    if (props.onClose) {
                      props.onClose();
                    } else {
                      const container = document.getElementById('operacion-view-container');
                      if (container) {
                        container.remove();
                      }
                    }
                  }}
                  style={{ cursor: 'pointer' }}
                  title="Cerrar"
                >
                  <span className="pw-close__bar-1"></span>
                  <span className="pw-close__bar-2"></span>
                </span>
                <p className="ml-3 mb-0">Operación No. {props.operacion || state.numeroOperacionCliente}</p>
              </div>
              {btnGroupDecision}
            </div>
            <div className="prw-cont__detail px-4">
              <div className="form-group">
                <button onClick={() => AbrilModalAdjuntos(props.operacion)} data-toggle="modal" data-target="#filtrosAvanzados" className="btn btn-secondary my-1 mx-1">Ver documentos</button>
                {state.bandejaProvision === "S" ? <div></div> : <button className={`btn btn-primary my-1 mx-1 ${contextRolCodigo0 === "ANACON" || contextRolCodigo0 === "AUXAD2" ? "visible" : "invisible"}`} data-toggle="modal" data-target="#modalProvisionFactura" onClick={provisionarFatura}>Provisionar factura</button>}
              </div>
              {
                listadoFactura
              }
            </div>
          </div>
      }
    </>
  )
}