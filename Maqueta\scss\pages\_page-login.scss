/*
 
 ██████╗  █████╗  ██████╗ ███████╗    ██╗      ██████╗  ██████╗ ██╗███╗   ██╗
 ██╔══██╗██╔══██╗██╔════╝ ██╔════╝    ██║     ██╔═══██╗██╔════╝ ██║████╗  ██║
 ██████╔╝███████║██║  ███╗█████╗█████╗██║     ██║   ██║██║  ███╗██║██╔██╗ ██║
 ██╔═══╝ ██╔══██║██║   ██║██╔══╝╚════╝██║     ██║   ██║██║   ██║██║██║╚██╗██║
 ██║     ██║  ██║╚██████╔╝███████╗    ███████╗╚██████╔╝╚██████╔╝██║██║ ╚████║
 ╚═╝     ╚═╝  ╚═╝ ╚═════╝ ╚══════╝    ╚══════╝ ╚═════╝  ╚═════╝ ╚═╝╚═╝  ╚═══╝
 
*/

// Page Login
/////////////////////////////////////////////////

@include block('login-box') {
      
    height: 100vh;
     
    // Image 
    @include element('image') {

        // Ocultamos por default
        display: none;
        position: relative;
        background-size: cover;

            // A partir de 992px
            @include media-breakpoint-up(lg) {
                display: flex;
                justify-content: center;
                background-size: cover;
            }
        

        // Ocupe el 50%
        flex:0 0 50%;
        
        background: $white url('/img/login-bg.png') no-repeat center;
           

        h1 { color:$white; }

        p  { 
            position: absolute;
            bottom: 10px;
            left: 0;
            right: 0;
            text-align: center;
            color:$white;
        }

    }
    
    // Box Campos
    @include element('campos') {
        
        // Ocupe hasta el 100%
        flex:1 0 0;
    
        section {

            padding: $spacer; 
            height: 100vh;
            height: calc(var(--vh, 1vh) * 100);
            border-bottom: 5px solid $blue;
            transition: height .1s ease-in-out;
                
                // A partir de 768px
                @include media-breakpoint-up(md) {
                    padding: ($spacer * 2);
                } 
        }

    }
    
    // Box Datos
    @include element('datos') {

        width: 100%;
        margin: 0 auto;
            
        @include media-breakpoint-up(md) {
            width: 55%;
        }
    }
}//endLoginBox
@include block('info-box'){
    width: 100%;
    height: 100%;
    position: absolute;
    color: $white;
    z-index: 10;
    left: -100%;
    transition: left 0.3s ease;

     @include media-breakpoint-up(md) {
        width: 50%;
        left: -50%;
    }

    @include modifier('open'){
        left: 0;
    }
}