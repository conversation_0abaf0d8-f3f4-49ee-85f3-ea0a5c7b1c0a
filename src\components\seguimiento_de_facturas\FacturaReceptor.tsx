/// <reference types="webpack/module" />

import React from "react";
import { DatosProvider, useProveedor } from "../contextProvider/datosProveedorContext";
import SeleccionarArea from "../SeleccionarArea";

export const FacturaReceptor = () => {

  const { state } = useProveedor();

  return (
    <div className="">
      <small>para</small>
      <address className="m-t-5 m-b-5 mb-5">
        <strong className="text-inverse">Banco Continental</strong><br />
        <div className="d-flex">
          Dirección: <div className="text-success ml-2">{state.direccion || state.bandejaDireccion}</div>
        </div>
        <div className="d-flex">
          Ciudad: <div className="text-success ml-2">{state.ciudad || state.bandejaCiudad}</div>
        </div>
        <div className="d-flex">
          Teléfono: <div className="text-success ml-2">{state.telefono || state.bandejaTelefono}</div>
        </div>
      </address>
      <div>
        {state.selectedArea && state.selectedArea !== "TECNOLOGIA" && state.condicionInicioSeguimiento === true ? <DatosProvider><SeleccionarArea/></DatosProvider> : ""}
      </div>
    </div>
  )
}