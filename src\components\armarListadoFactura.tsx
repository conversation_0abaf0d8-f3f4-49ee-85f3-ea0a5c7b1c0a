/// <reference types="webpack/module" />

import { List } from './listado/listado';
import React, { useState } from "react";
import { IlistadoItemFactura } from '../entities/Intefaces/IListado';
import { ListaItem } from './listado/listadoData';

'use strict'

export const ArmadoListaFactura = (props: any) => {

  try {
    let listas: JSX.Element[]
    let listadoDatos: IlistadoItemFactura[]
    let tareas: any[]
    let tareasOperacionID: any[]
    console.log("CantidadOperaciones: ", props.operaciones.length)
    if (props.operaciones == undefined || props.operaciones.length < 1) {
      listadoDatos = List
    } else {
      console.log('Else de ListArmado')
      listadoDatos = props.operaciones
      tareas = props.tareas

      tareasOperacionID = tareas.map(item => item.WorkflowLink.Description)
    }

    let listadoFiltrado: IlistadoItemFactura[] = []

    console.log("OperacionesFiltrado: ", listadoFiltrado)
    tareasOperacionID.forEach((element, index) => {
      listadoFiltrado.push(
        listadoDatos.filter((item) => {
          return item.titulo.toLowerCase().match(/[0-9]*$/)[0].indexOf(element.toLowerCase()) !== -1
        })[0])
      if (listadoFiltrado[index] != undefined) {
        listadoFiltrado[index].idTarea = tareas[index].ID
      }
    });

    if (listadoFiltrado[0] == undefined) {
      listadoFiltrado.shift();
    }

    listas = listadoFiltrado.map((element) => ListaItem(element));

    const [filteredList, setFilteredList] = useState(listas);

    const filterBySearch = (event: React.ChangeEvent<HTMLInputElement>) => {
      // Access input value
      const query = event.target.value;
      console.log(query)
      // Create copy of item list
      let updatedList: any
      updatedList = [...listadoDatos];
      // Include all elements which includes the search query
      console.log(updatedList)
      updatedList = updatedList.filter((item: any) => {
        return (item.subTitulo.toLowerCase().indexOf(query.toLowerCase()) !== -1 || item.titulo.toLowerCase().indexOf(query.toLowerCase()) !== -1);
      });
    
      updatedList = updatedList.map((element: any) => ListaItem(element));

      // Trigger render with updated values
      setFilteredList(updatedList);
    };

    return (<><div >
      <div >
      </div>
      <div id="item-list">
        <ol>
          {filteredList.map((item, index) => (
            <li id={'item-list' + index} key={index}>{item}</li>
          ))}
        </ol>
      </div>
    </div>
    </>);

  } catch (error) {
    console.log("error catch ListArmado:", error)
  }
}