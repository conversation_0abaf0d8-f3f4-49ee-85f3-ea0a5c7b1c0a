.title-section {
    text-align: center;
    margin-bottom: 20px;
}

.empty-section {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    padding: 20px;
    margin-bottom: 20px;
}

.cards-section {
    background-color: #e9ecef;
    border: 1px solid #dee2e6;
    padding: 20px;
}

.validation-indicator {
    font-size: 24px;
    font-weight: bold;
}

.bg-success {
    background-color: #d4edda !important;
}

.bg-danger {
    background-color: #f8d7da !important;
}

.amicable-color {
    background-color: #e0f7fa;
    border-color: #006064;
}

.container-seguimiento {
    background-color: none !important;
    max-width: 100% !important;
    width: 100% !important;
    height: max-content;
    padding-left: 30px;
    padding-right: 30px;
    margin-top: 20px;
}

.toast-custom {
    height: 320px !important;
}

.bg-success-regla {
    background-color: #d1f7c4 !important;
    color: #254725;
}

#style-4::-webkit-scrollbar {
    width: 10px;
}

#style-4::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0px 0px 6px rgba(0, 0, 0, .5);
    border-radius: 5px;
}

#style-4::-webkit-scrollbar-thumb {
    background: #2c376e;
    border-radius: 5px;
    background-image: -webkit-linear-gradient(90deg, rgba(255, 255, 255, .2) 50%, transparent 50%);
}

#scroll-4::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0px 0px 6px rgba(0, 0, 0.3);
    background: #f4f4f4;
}

#scroll-4::-webkit-scrollbar-thumb {
    background: #2c376e;
    border-radius: 5px;
    background-image: -webkit-linear-gradient(90deg, rgba(255, 255, 255, .2) 50%, transparent 50%);
}


#scroll-4::-webkit-scrollbar {
    width: 10px;
}

.scroll-bar {
    background: #fff;
    overflow: auto;
    margin-left: 50px;
}

.scroll-bar:first-child {
    margin-left: 0px;
}

.force-overflow {
    width: 100%;
    height: 600px;
    margin-right: 10px;
}

.tarjeta-atributos{
    border: 1px solid;
    border-radius: 12px;
}

.radio-container {
    margin-bottom: 15px;
}

.radio-container label {
    margin-right: 20px; 
    margin-left: 10px;
}