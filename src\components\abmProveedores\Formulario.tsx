import React, { useState } from 'react';
import DatosPersonales from './DatosPersonales';
import { getAmbienteContexto, getUrlBase } from '../../utilities/contextInfo';
import axios from 'axios';
import EndPoint from '../../comm/EndPoint.Config';
import { getApiProveedorFacturaHeaders } from '../../comm/contracts/ApiBpmPoliticasProcesosRequestHeaders';
import { ClienteProveedor } from '../../entities/ProveedorGet';

const Params = {
  urlBase: getUrlBase(),
  ambiente: getAmbienteContexto(),
  request: axios.create({}),
  endpoints: EndPoint.apiClientProveedor
}

const Formulario = () => {

  const [dato, setDato] = useState({ tipoDocumento: '', nroDocumento: '' });

  const obtenerDatosClienteProveedor = async () => {
    let _cacheControl = 'max=age=60*60*10, private';
    let _headers = await getApiProveedorFacturaHeaders(_cacheControl);
    let _sturlEndpoint = Params.endpoints.vUno.get.clienteProveedor;
    let urlEndpoint = _sturlEndpoint.replace('${codigoCliente}', `${dato.nroDocumento}`);
    let tipoDocumento = `?tipoDocumento=${dato.tipoDocumento}`;

    let config = {
      method: 'get',
      url: Params.urlBase + urlEndpoint + tipoDocumento,
      headers: _headers
    }

    console.log("Config:", config);

    let Promise = Params.request<ClienteProveedor>(config)
      .then(function (response) {
        console.log("Response Data:", response.data);
        return response.data;
      })
      .catch(function (error) {
        console.log("Error:", error);
      })

    let result = await Promise;
    return result;
  }

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement>) => {
    setDato({
      ...dato,
      [e.target.name]: e.target.value
    });
  }

  const handleData = (e: any) => {
    e.preventDefault();
    if (dato.nroDocumento === "800114051" && dato.tipoDocumento === '4') {
      obtenerDatosClienteProveedor();
    } else {
      confirm("Los datos no son válidos.");
      window.location.reload();
    }
  }

  const recargarPagina = () => {
    window.location.reload();
  }

  return (
    <div className='modal fade w-100 mt-2' id="exampleModal" tabIndex={-1} aria-labelledby="exampleModalLabel" aria-hidden="true">
      <div className='modal-dialog'>
        <div className='modal-content'>
          <div className='card m-2'>
            <div className='modal-header card-header font-weight-bold'>
              <div id="exampleModalLabel">Seleccione Tipo y Nro de Documento</div>
              <button className='btn btn-outline-danger btn-close' data-dismiss="modal" aria-label="Close" onClick={recargarPagina}>&#x2715;</button>
            </div>
            <div className='modal-body card-body'>
              <div className='d-flex row'>
                <div className='col-md-8'>
                  <div className='mb-3'>
                    <label htmlFor='disabledTextInput' className='form-label'>Tipo Documento: </label>
                    <select className='form-select mx-2' name="tipoDocumento" defaultValue="" onChange={handleChange}>
                      <option value="1">1</option>
                      <option value="2">2</option>
                      <option value="3">3</option>
                      <option value="4">4</option>
                    </select>
                  </div>
                  <div className='d-flex align-items justify-content-center mb-3'>
                    <label htmlFor='disabledTextInput' className='form-label'>Nro Documento:</label>
                    <input type="search" className='form-control mx-2' name="nroDocumento" onChange={handleChange} />
                  </div>
                  <div className='d-flex'>
                    <label>Nombre:</label><span className='mx-2'>[CLIEKEY][NOMBRE]</span>
                  </div>
                </div>
                <div className='text-center d-flex justify-content-center align-items-center col-md-4'>
                  <button className='btn btn-primary' onClick={handleData}>Buscar</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <DatosPersonales />
    </div>
  )
}

export default Formulario