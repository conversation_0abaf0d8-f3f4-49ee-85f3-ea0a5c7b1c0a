import React, { useState, useRef, useEffect, ReactNode } from 'react';

interface DropdownItem {
  id: string;
  label: string;
  onClick?: () => void;
  disabled?: boolean;
  divider?: boolean;
}

interface DropdownProps {
  trigger: ReactNode;
  items: DropdownItem[];
  className?: string;
  dropdownClassName?: string;
  placement?: 'bottom-start' | 'bottom-end' | 'top-start' | 'top-end';
  disabled?: boolean;
}

const Dropdown: React.FC<DropdownProps> = ({
  trigger,
  items,
  className = '',
  dropdownClassName = '',
  placement = 'bottom-start',
  disabled = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleDropdown = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  const closeDropdown = () => {
    setIsOpen(false);
  };

  const handleItemClick = (item: DropdownItem) => {
    if (!item.disabled && item.onClick) {
      item.onClick();
    }
    closeDropdown();
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        closeDropdown();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Close dropdown on Escape key
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        closeDropdown();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscapeKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isOpen]);

  const getPlacementClasses = () => {
    switch (placement) {
      case 'bottom-end':
        return 'dropdown-menu-right';
      case 'top-start':
        return 'dropup';
      case 'top-end':
        return 'dropup dropdown-menu-right';
      default:
        return '';
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      toggleDropdown();
    }
  };

  return (
    <div
      ref={dropdownRef}
      className={`dropdown ${isOpen ? 'show' : ''} ${className}`}
    >
      <button
        type="button"
        onClick={toggleDropdown}
        onKeyDown={handleKeyDown}
        className={`dropdown-toggle ${disabled ? 'disabled' : ''}`}
        style={{
          cursor: disabled ? 'not-allowed' : 'pointer',
          border: 'none',
          background: 'none',
          padding: 0
        }}
        aria-expanded={isOpen}
        aria-haspopup="true"
        disabled={disabled}
      >
        {trigger}
      </button>

      {isOpen && (
        <div 
          className={`dropdown-menu show ${getPlacementClasses()} ${dropdownClassName}`}
          style={{ position: 'absolute', zIndex: 1000 }}
        >
          {items.map((item) => (
            <React.Fragment key={item.id}>
              {item.divider ? (
                <div className="dropdown-divider" />
              ) : (
                <button
                  className={`dropdown-item ${item.disabled ? 'disabled' : ''}`}
                  type="button"
                  disabled={item.disabled}
                  onClick={() => handleItemClick(item)}
                >
                  {item.label}
                </button>
              )}
            </React.Fragment>
          ))}
        </div>
      )}
    </div>
  );
};

export default Dropdown;
