/*
 
 ██╗    ██╗ █████╗ ██████╗ ██████╗ ███████╗██████╗ 
 ██║    ██║██╔══██╗██╔══██╗██╔══██╗██╔════╝██╔══██╗
 ██║ █╗ ██║███████║██████╔╝██████╔╝█████╗  ██████╔╝
 ██║███╗██║██╔══██║██╔═══╝ ██╔═══╝ ██╔══╝  ██╔══██╗
 ╚███╔███╔╝██║  ██║██║     ██║     ███████╗██║  ██║
  ╚══╝╚══╝ ╚═╝  ╚═╝╚═╝     ╚═╝     ╚══════╝╚═╝  ╚═╝
                                                   
 
*/

@include block('wrapper') {
     max-width: 940px;
     margin: 0 auto;
     
     //@include screen(740px,1024px) { max-width: 940px; }
     @include screen(1280px,1366px) { max-width: 740px; } 
     @include min-screen(1920px) { max-width: 1100px; }

}

