import React, { useState } from "react";
import { obtenerHadoopDocBase64 } from "../../comm/WSdocumentos";
import ModalVisualizadorPDF from "../modales/ModalVisualizadorPDF";

export const BtnBajarAdjunto = (props: any) => {
  const [mostrarModal, setMostrarModal] = useState(false);
  const [documentoBase64, setDocumentoBase64] = useState('');
  const [cargando, setCargando] = useState(false);
  
  // Extraer el nombre del archivo de la ruta
  const obtenerNombreArchivo = (ruta: string) => {
    const ultimaBarraIndex = ruta.lastIndexOf("/");
    return ultimaBarraIndex !== -1 ? ruta.substring(ultimaBarraIndex + 1) : ruta;
  };
  
  // Usar el nombre del archivo de la ruta o el proporcionado por props
  const nombreDocumento = props.adjunto.descripcionCarpeta || obtenerNombreArchivo(props.adjunto.rutaDocumento);
  
  // Determinar si el archivo es de tipo Office (Word, Excel)
  const esArchivoOffice = () => {
    if (!nombreDocumento) return false;
    const extension = nombreDocumento.split('.').pop()?.toLowerCase();
    return extension === 'docx' || extension === 'xlsx' || extension === 'doc' || extension === 'xls';
  };
  
  // Determinar el texto del botón según el tipo de archivo
  const textoBoton = () => {
    if (cargando) return "Cargando";
    if (esArchivoOffice()) return "Descargar";
    return "Previsualizar";
  };
  
  const previsualizarDocumento = async () => {
    try {
      setCargando(true);
      const base64Data = await obtenerHadoopDocBase64(props.adjunto.rutaDocumento);
      if (base64Data) {
        setDocumentoBase64(base64Data);
        setMostrarModal(true);
      } else {
        alert("No se pudo cargar el documento para previsualizar.");
      }
    } catch (error) {
      console.error("Error al previsualizar el documento:", error);
      alert("Error al cargar el documento.");
    } finally {
      setCargando(false);
    }
  };
  
  const cerrarModal = () => {
    setMostrarModal(false);
  };
  
  return (
    <div className="file-action">
      <button 
        onClick={previsualizarDocumento} 
        className="btn btn-outline-secondary"
        disabled={cargando}
      >
        {cargando ? (
          <span>
            <span className="spinner-border spinner-border-sm mr-1" role="status" aria-hidden="true"></span>
            Cargando
          </span>
        ) : textoBoton()}
      </button>
      
      <ModalVisualizadorPDF 
        mostrar={mostrarModal} 
        cerrar={cerrarModal} 
        base64Data={documentoBase64} 
        nombreArchivo={nombreDocumento}
      />
    </div>
  );
}
