import _ from 'lodash';
import { GetAccionGeneradas, GetDatosAcciones, getOperacionesCabecera, postAccionDatos, postAccionGenerada, postOperacionCabecera, ResponderAccion } from '../comm/apiBpmPoliticasProcesos';
import { FacturaInsert } from '../comm/contracts/FacturaInsert';
import { InicioSeguimientoRequest } from '../comm/contracts/InicioSeguimientoRequest';
import { InicioSeguimientoSuccess, isInicioSeguimientoSuccess } from '../comm/contracts/InicioSeguimientoSuccess';
import { isOperacionArr } from '../entities/Operacion';
import { insertFactura } from './facturaServices';
import { subirAdjuntos } from './adjuntosServices';
import { bpmprocesosParams } from '../entities/Objects/bpmprocesosParams';
import { ProcesartareaNintex } from '../comm/apiSharePoint';
import { GetAccionGeneradaResponse, isGetAccionGeneradaResponseArr } from '../comm/contracts/GetAccionGeneradaResponse';
import { PutResponderAccionBody } from '../comm/contracts/PutResponderAccion';
import { GetDatosAccionesResponse } from '../comm/contracts/GetDatosAccionesResponse';
import { ComentarioCard } from '../entities/ComentarioCard';
import { getUserFromStorage } from './usuarioServices';
import { InsertAccionGeneradaBody } from '../comm/contracts/InsertAccionGeneradaBody';
import { Estados } from '../entities/Enums/estadosRespuesta';
import { getFacturas, postMigracionProveedorFacturas, putActualizarFactura } from '../comm/apiProveedorFactura';
import { FacturaGetResponse } from '../comm/contracts/FacturaGetResponse';
import { InsertAccionDatosBody } from '../comm/contracts/InsertAccionDatosBody';
import { getCotizacion } from '../comm/apiDivisas';
import { getApiBpmPoliticasProcesosRequestHeaders } from '../comm/contracts/ApiBpmPoliticasProcesosRequestHeaders';
import { getUrlBase } from '../utilities/contextInfo';
import axios from 'axios';
import EndPoint from '../comm/EndPoint.Config';
import { getListadoMisTareas } from '../comm/ApiSPNintexType';
import { PaginacionData } from '../entities/PaginacionData';
import { formatearFechaConHora } from '../utilities/dateUtils';
import { formatearNumeroFactura } from '../utilities/formatUtils';

export const fetchOperaciones = async (proceso: string, estado?: string) => {
  try {
    const estadoFiltro = estado || "";

    const _operacionArr = await getOperacionesCabecera(proceso, estadoFiltro);

    if (isOperacionArr(_operacionArr)) {
      return _operacionArr;
    } else {
      throw new Error("La respuesta no es un arreglo de operaciones");
    }
  } catch (error) {
    console.error("Error al obtener operaciones:", error);
    throw error;
  }
};

export const insertOperacionSeguimiento = async (bodyInicSeguimiento: InicioSeguimientoRequest) => {
  console.log("bodyInicSeguimiento:", bodyInicSeguimiento)
  let _operacionResponse = await postOperacionCabecera(bodyInicSeguimiento)
  if (isInicioSeguimientoSuccess(_operacionResponse)) {
    localStorage.setItem('codigoOperacionIniciada', _operacionResponse.id)
    console.log("_operacionResponse 1:", _operacionResponse);
    return _operacionResponse
  }
}

export const inicioDeSegumientoFacturas = async (bodyInicSeguimiento: InicioSeguimientoRequest, factura: FacturaInsert[]) => {
  let _facturaInsertResponse = await insertFactura(factura);
  let df = _facturaInsertResponse.data.idLote;
  console.log("df:", df);
  let idlote = parseInt(df);
  console.log("idlote:", idlote);
  let datoFactura: void | FacturaGetResponse[] | any = await getFacturas(idlote);
  console.log("datoFactura:", datoFactura);
  let nroFactura = datoFactura[0].numeroFactura.numero;
  console.log("nroFactura:", nroFactura);
  let codCliente = datoFactura[0].proveedores.codigoCliente;
  console.log("codCliente:", codCliente);
  let codigoArea = localStorage.getItem('codigoArea');
  console.log("codigoArea:", codigoArea);
  let codigoDepartamento = localStorage.getItem('codigoDepartamento');
  console.log("codigoDepartamento:", codigoDepartamento);
  let tipoMonedaSeleccionada = datoFactura[0].monto.moneda;
  console.log("tipoMonedaSeleccionada:", tipoMonedaSeleccionada);
  let _operacionResponse: InicioSeguimientoSuccess
  console.log("_facturaInsertResponse:", _facturaInsertResponse);
  let _checkExcepcionFactura = document.getElementById('facturaExcepcionHidden');
  let _provisionamientoFactura = factura[0].provision ? "S" : "N";
  
  // Obtener nombre dinámico del DOM o state
  let nombreEmisor = '';
  try {
    nombreEmisor = document.querySelector('.text-success')?.textContent?.trim() || '';
  } catch (error) {
    console.log("No se pudo obtener nombre del DOM:", error);
  }

  // Obtener número de documento dinámico del DOM
  let numeroDocumento = '';
  try {
    const inputDocumento = document.getElementById('buscadorProveedor') as HTMLInputElement;
    if (inputDocumento && inputDocumento.value) {
      // Remover ceros iniciales del número de documento
      numeroDocumento = inputDocumento.value.trim().replace(/^0+/, '') || inputDocumento.value.trim();
    }
  } catch (error) {
    console.log("No se pudo obtener número de documento del DOM:", error);
  }
  
  if (_facturaInsertResponse.status === Estados.estadoDeSolicitudProcesada) {
    bodyInicSeguimiento.atributos[0].valor = _facturaInsertResponse.data.idLote.toString();
    bodyInicSeguimiento.atributos[0].codigo = bpmprocesosParams.IDlote
    if (bodyInicSeguimiento.atributos[0 + 1] !== undefined) {
      bodyInicSeguimiento.atributos[0 + 1].codigo = bpmprocesosParams.atributo[0].cod
      if (tipoMonedaSeleccionada === "USD") {
        let responseCotizacion = await getCotizacion(tipoMonedaSeleccionada);
        console.log("responseCotizacion:", responseCotizacion);
        let venta: number = responseCotizacion.data.venta;
        console.log("monto venta:", venta);
        console.log("monto multiplicación:", venta * factura[0].monto.total);
        let montoGuaranies = venta * factura[0].monto.total;
        console.log("montoGuaranies:", montoGuaranies);
        let montoFinalGuaranies: any = montoGuaranies.toString().replace('.', ',');
        console.log("montoFinalGuaranies:", montoFinalGuaranies);
        bodyInicSeguimiento.atributos[0 + 1].valor = montoFinalGuaranies;
        bodyInicSeguimiento.atributos.push({ codigo: bpmprocesosParams.atributo[9].cod, valor: factura[0].monto.total.toString() });
      } else {
        bodyInicSeguimiento.atributos[0 + 1].valor = factura[0].monto.total.toString()
        bodyInicSeguimiento.atributos.push({ codigo: bpmprocesosParams.atributo[9].cod, valor: '0' });
      }
      bodyInicSeguimiento.atributos.push({ codigo: bpmprocesosParams.atributo[3].cod, valor: _provisionamientoFactura });
      bodyInicSeguimiento.atributos.push({ codigo: bpmprocesosParams.atributo[2].cod, valor: _checkExcepcionFactura.getAttribute('value') });
      bodyInicSeguimiento.atributos.push({ codigo: bpmprocesosParams.atributo[4].cod, valor: nroFactura });
      bodyInicSeguimiento.atributos.push({ codigo: bpmprocesosParams.atributo[5].cod, valor: codCliente });
      bodyInicSeguimiento.atributos.push({ codigo: bpmprocesosParams.atributo[6].cod, valor: codigoArea });
      bodyInicSeguimiento.atributos.push({ codigo: bpmprocesosParams.atributo[7].cod, valor: codigoDepartamento });
      bodyInicSeguimiento.atributos.push({ codigo: bpmprocesosParams.atributo[8].cod, valor: tipoMonedaSeleccionada });
      
      // AGREGAR ATRIBUTO ATNOMB DINÁMICO
      if (nombreEmisor) {
        bodyInicSeguimiento.atributos.push({
          codigo: "ATNOMB",
          valor: nombreEmisor.toUpperCase()
        });
      }

      // AGREGAR ATRIBUTO ATRDOC DINÁMICO
      if (numeroDocumento) {
        bodyInicSeguimiento.atributos.push({
          codigo: "ATRDOC",
          valor: numeroDocumento
        });
      }
    }

    _operacionResponse = await insertOperacionSeguimiento(bodyInicSeguimiento)
    if (isInicioSeguimientoSuccess(_operacionResponse)) {
      subirAdjuntos(window.appGlobal as any, _operacionResponse.id).then(() => {
        console.log('entre al subir adjuntos')
        console.log("_operacionResponse.id:", _operacionResponse.id);
        console.log("_operacionResponse 2:", _operacionResponse);
        return _operacionResponse

      }).catch(() => {
        console.log('Algo Paso con los documentos')
      })
    }
    return _operacionResponse
  } else {
    alert("Hubo un inconveniente al insertar la factura. Por favor, intente de nuevo.");
  }
}

export const getDataListado = async (proceso: string, esMiBandeja: boolean = true) => {

  // Por defecto usar V1 + Nintex para Mi Bandeja (pendientes)
  if (esMiBandeja) {
    return await getDataListadoMiBandejaV1(proceso, 1, 10);
  }

  // Solo usar V2 para búsquedas específicas
  const estadoCodigo = bpmprocesosParams.estado[0].cod;
  const _operaciones = await fetchOperaciones(proceso, estadoCodigo);

  const codigoAtributoLoteFactura = 'ATRLFA';
  const codigoMoneda = bpmprocesosParams.atributo[8].cod;
  const estadoNombre = bpmprocesosParams.estado[0].nombre;

  const _datos = _operaciones.map((element) => {
    const tipoMoneda = element.atributos.find(item => item.codigo === codigoMoneda)?.valor || 'PYG';
    const codigoMonto = tipoMoneda === "PYG" ? "ATMONG" : "ATMONF";

    const loteFactura = element.atributos.find(item => item.codigo === codigoAtributoLoteFactura)?.valor || '0';
    const montoFactura = element.atributos.find(item => item.codigo === codigoMonto)?.valor || '0';

    return {
      titulo: `Operacion Nro: ${element.id}`,
      subTitulo: `Lote Nro: ${loteFactura}`,
      fecha: {
        string: element.fechaHoraCreacion,
        date: new Date(element.fechaHoraCreacion),
      },
      estado: estadoNombre,
      monto: montoFactura,
      monedaTipo: tipoMoneda,
    };
  });

  return { _datos, _operaciones };
};

export const responderTareaService = async (idTarea: string, decision: string, lista: string, codOperacion: string, comentario: string) => {
  localStorage.setItem("statusValue", "0");
  let xhr: any = await ProcesartareaNintex(idTarea, decision, lista)
    .then(responseXML => { console.log("responseXML:", responseXML); return responseXML })
    .catch(error => { console.log("Error catch ProcesartareaNintex:", error); return error });
  console.log("xhr:", xhr);

  const _estado = bpmprocesosParams.estado[0].cod
  const getAccionPromise = GetAccionGeneradas(codOperacion, _estado)
  let responseGetAccionPromise: any = await getAccionPromise.then((item: any) => { return item[0].idAccionTomada });
  console.log('responseGetAccionPromise:', responseGetAccionPromise);
  getAccionPromise.then((response) => {
    if (isGetAccionGeneradaResponseArr(response)) {
      const _body: PutResponderAccionBody =
      {
        usuario: localStorage.getItem('contextNombreUsuario'),
        respuesta: decision,
        idAccionTomada: parseInt(response[0].idAccionTomada),
        codAccion: response[0].codAccion,
        codEstado: bpmprocesosParams.estado[0].cod,
        codRol: ''
      }

      console.log('consoleo body')
      console.log(_body)
      console.log("codOperacion:", codOperacion);
      let statusValue: any = localStorage.getItem("statusValue");
      console.log("statusValue LS:", statusValue);
      if (parseInt(statusValue) === 200) {
        localStorage.setItem("statusValue", "0");
        ResponderAccion(_body).then((e) => {
          console.log("ResponderAccion:", e);
          insertarAccionDatos(parseInt(response[0].idAccionTomada), 'ATCOME', comentario).then(async (e) => {
            console.log("responseinsertAccionDatos:", e);
            console.log("E.MENSAJE:", e.mensaje);

            if (e.mensaje === "OK" && _body.codAccion === "SF0001") {
              if (_body.respuesta === "Aprobar") {
                let idLoteRelacion = localStorage.getItem("idLoteRelacion");
                let datosFactura: any = await getFacturas(parseInt(idLoteRelacion));
                if (datosFactura[0].provision === "S") {
                  console.log("MIGRACIÓN FACTURA");
                  await postMigracionProveedorFacturas();
                }
              }
            }

            if (e.mensaje === "OK" && _body.codAccion === "SF0016") {
              if (_body.respuesta === "Aprobar") {
                let idLoteRelacion = localStorage.getItem("idLoteRelacion");
                let datosFactura: any = await getFacturas(parseInt(idLoteRelacion));
                if (datosFactura[0].provision === "N") {
                  console.log("DATOS INSERTADOS EN EL MODULO DE CONTABILIDAD");
                  await postMigracionProveedorFacturas();
                }
              }
            }

            if (e.mensaje === "OK" && _body.codAccion === "SF0017") {
              console.log("ENTRÉ EN EL IF PARA ACTUALIZAR FACTURA DATOSITA.");
              let idLoteRelacion = localStorage.getItem("idLoteRelacion");
              console.log("idLoteRelacion:", idLoteRelacion);

              let datosFactura: any = await getFacturas(parseInt(idLoteRelacion));
              let actualizarDatosFactura: any = [];
              datosFactura.forEach((item: any) => {
                actualizarDatosFactura.push(item.numeroLoteRelacional, item.idCargaRelacional, item.marcaTipoPago, item.provision);
              });

              let body: any = {};

              let bodyEstado = {
                datosFactura: {
                  numeroLote: actualizarDatosFactura[0],
                  idCarga: actualizarDatosFactura[1],
                  estado: 'A',
                  estadoCarga: 'A',
                  estadoAnterior: 1,
                  marcaTipoPago: actualizarDatosFactura[2],
                  marcaProvision: actualizarDatosFactura[3],
                },
                codigoModificacion: '2'
              }

              console.log("actualizarDatosFactura:", actualizarDatosFactura);
              console.log("bodyEstado:", bodyEstado);

              let opcion = 2;

              let response = await putActualizarFactura(body, bodyEstado, opcion);
              console.log("RESPONSE:", response);
            }
          })

        })
      } else {
        alert("HUBO UN INCONVENIENTE AL PROCESAR. POR FAVOR, VOLVER A INTENTAR.");
      }
      console.log("statusValue seteado:", statusValue);
    }
  })
}

export const obtenerDatosAccionesService = async (codAccionGenerada: string, codAtributo: string) => {
  const _atributo = await GetDatosAcciones(codAccionGenerada, codAtributo) as GetDatosAccionesResponse[]

  console.log("_atributo:", _atributo)
  return _atributo[0]


}


export const listarComentarios = async (codOperacion: string) => {
  const _accionesGeneradas = await GetAccionGeneradas(codOperacion, bpmprocesosParams.estado[0].cod) as GetAccionGeneradaResponse[]
  console.log('entro en acciones generadas')
  console.log(_accionesGeneradas)
  console.log('entro en acciones generadas')

  const _comentariosPromise = _accionesGeneradas.map(item => armarComentarioService(item.idAccionTomada))
  let _comentarios: ComentarioCard[] = []
  await Promise.all(_comentariosPromise).then((values) => _comentarios = values)
  console.log("_comentarios:", _comentarios);
  return _comentarios

}

const armarComentarioService = async (codAccionGenerada: string) => {

  const comentario = (await obtenerDatosAccionesService(codAccionGenerada, 'ATRRES')).valor
  const comentarioCard: ComentarioCard =
  {
    usuario: getUserFromStorage(),
    comentario: comentario,
    fechaHora: {
      date: new Date(),
      string: ''
    },
    idAccionTomada: parseInt(codAccionGenerada)
  }

  return comentarioCard

}

export const insertarComentario = async (idCabecera: number, comentario: string) => {
  const bodyComentario: InsertAccionGeneradaBody =
  {
    idCabecera: idCabecera,
    codAccion: bpmprocesosParams.acciones[0].cod,
    codEstado: bpmprocesosParams.estado[1].cod,
    usuarioDominio: getUserFromStorage().NombreUsuario,
    atributos: [
      {
        codigo: bpmprocesosParams.atributo[1].cod,
        nombre: bpmprocesosParams.atributo[1].nombre,
        valor: comentario
      }
    ]
  }
  const idAccionGenerada = await postAccionGenerada(bodyComentario)
  return idAccionGenerada
}

export const insertarAccionDatos = async (idAccionGenerada: number, codigoAtributo: string, valor: string) => {
  const body: InsertAccionDatosBody = {
    idAccionGenerada: idAccionGenerada,
    codigoAtributo: codigoAtributo,
    valor: valor
  }
  const _responseAccionDatos = await postAccionDatos(body);
  return _responseAccionDatos;
}

export const getDataListadoPaginado = async (proceso: string, pagina: number = 1, tamanoPagina: number = 10) => {
  
  try {
    const _headers = await getApiBpmPoliticasProcesosRequestHeaders('');
    const _sturlEndpoint = EndPoint.apiBpmPoliticaProcesos.vDos.get.operaciones;
    const urlEndpoint = _sturlEndpoint.replace('${proceso}', proceso);
    
    const queryParams = [
      `estado=ESTPEN`, // Solo pendientes por defecto
      `paginaActual=${pagina}`,
      `tamanoPagina=${tamanoPagina}`,
      `ordenDescendente=true`
    ];
    
    const filtersUrl = '?' + queryParams.join('&');
    
    const config = {
      method: 'get' as const,
      url: getUrlBase() + urlEndpoint + filtersUrl,
      headers: _headers
    };

    const response = await axios.request(config);
    
    if (!response.data?.elementos) {
      return { _datos: [], _operaciones: [], paginacion: null };
    }

    const codigoAtributoLoteFactura = 'ATRLFA';
    const codigoMoneda = bpmprocesosParams.atributo[8].cod;

    const _datos = response.data.elementos.map((element: any) => {
      const tipoMoneda = element.atributos?.find((item: any) => item.codigo === codigoMoneda)?.valor || 'PYG';
      const codigoMonto = tipoMoneda === "PYG" ? "ATMONG" : "ATMONF";

      const nroFactura = element.atributos?.find((item: any) => item.codigo === "ATRFAC")?.valor || 
                        element.atributos?.find((item: any) => item.codigo === codigoAtributoLoteFactura)?.valor || '0';
      const montoFactura = element.atributos?.find((item: any) => item.codigo === codigoMonto)?.valor || '0';

      return {
        titulo: `Operacion Nro: ${element.id}`,
        subTitulo: `Nro de Factura: ${formatearNumeroFactura(nroFactura)}`,
        fecha: {
          string: formatearFechaConHora(element.fechaHoraCreacion),
          date: new Date(element.fechaHoraCreacion),
        },
        estado: "Pendiente", // Solo pendientes
        monto: montoFactura,
        monedaTipo: tipoMoneda,
      };
    });

    const paginacion = {
      numeroPagina: response.data.numeroPagina,
      tamanoPagina: response.data.tamanoPagina,
      totalElementos: response.data.totalElementos,
      totalPaginas: response.data.totalPaginas,
      tienePaginaAnterior: response.data.tienePaginaAnterior,
      tienePaginaSiguiente: response.data.tienePaginaSiguiente
    };

    return { _datos, _operaciones: response.data.elementos, paginacion };
    
  } catch (error) {
    console.error("Error en getDataListadoPaginado:", error);
    return { _datos: [], _operaciones: [], paginacion: null };
  }
};

// Función para obtener tareas del usuario desde Nintex
const obtenerTareasUsuario = async (proceso: string) => {
  try {
    const siteCod = proceso === 'SEGFAC' ? 1 : 2;
    const lista = 'Tareas Seguimiento Facturas';
    const result = await getListadoMisTareas(siteCod, lista);
    return result || [];
  } catch (error) {
    console.error("❌ Error obteniendo tareas Nintex:", error);
    return [];
  }
};

// Función principal para Mi Bandeja con V1 + Nintex + paginación frontend
export const getDataListadoMiBandejaV1 = async (proceso: string, pagina: number = 1, tamanoPagina: number = 10) => {
  
  try {
    // 1. Obtener tareas del usuario desde Nintex
    const tareasUsuario = await obtenerTareasUsuario(proceso);
    
    if (!tareasUsuario || tareasUsuario.length === 0) {
      return { _datos: [], _operaciones: [], paginacion: null };
    }

    // 2. Crear mapa de operación -> tarea para el match
    const mapaOperacionTarea = new Map();
    tareasUsuario.forEach((tarea: any) => {
      const numeroOperacion = tarea.WorkflowLink?.Description || '';
      if (numeroOperacion) {
        mapaOperacionTarea.set(numeroOperacion.toString(), tarea.ID);
      }
    });

    // 3. Obtener todas las operaciones pendientes
    const todasLasOperaciones = await fetchOperaciones(proceso, 'ESTPEN');
    
    if (!Array.isArray(todasLasOperaciones) || todasLasOperaciones.length === 0) {
      return { _datos: [], _operaciones: [], paginacion: null };
    }

    // 4. Filtrar y asignar idTarea a las operaciones del usuario
    const operacionesDelUsuario = todasLasOperaciones
      .filter((operacion: any) => {
        const idOperacion = operacion.id.toString();
        return mapaOperacionTarea.has(idOperacion);
      })
      .map((operacion: any) => {
        const idOperacion = operacion.id.toString();
        const idTarea = mapaOperacionTarea.get(idOperacion);
        
        return {
          ...operacion,
          idTarea: idTarea
        };
      });

    // 5. Ordenar por fecha descendente (usando sort con spread para inmutabilidad)
    const operacionesOrdenadas = [...operacionesDelUsuario].sort((a: any, b: any) => {
      const fechaA = new Date(a.fechaHoraCreacion).getTime();
      const fechaB = new Date(b.fechaHoraCreacion).getTime();
      return fechaB - fechaA;
    });

    // 6. Transformar datos al formato UI
    const datosTransformados = operacionesOrdenadas.map((operacion: any) => {
      const nroFactura = operacion.atributos?.find((item: any) => item.codigo === "ATRFAC")?.valor || 
                        operacion.atributos?.find((item: any) => item.codigo === "ATRLFA")?.valor || '0';
      const tipoMoneda = operacion.atributos?.find((item: any) => item.codigo === "ATRMON")?.valor || 'PYG';
      const codigoMonto = tipoMoneda === "USD" ? "ATMONF" : "ATMONG";
      const montoFactura = operacion.atributos?.find((item: any) => item.codigo === codigoMonto)?.valor || '0';

      const formatearFecha = formatearFechaConHora;

      return {
        titulo: `Operacion Nro: ${operacion.id}`,
        subTitulo: `Nro de Factura: ${formatearNumeroFactura(nroFactura)}`,
        fecha: {
          string: formatearFecha(operacion.fechaHoraCreacion),
          date: new Date(operacion.fechaHoraCreacion),
        },
        estado: "Pendiente",
        monto: montoFactura,
        monedaTipo: tipoMoneda,
        idTarea: operacion.idTarea
      };
    });

    // 7. Aplicar paginación
    const totalElementos = datosTransformados.length;
    const totalPaginas = Math.ceil(totalElementos / tamanoPagina);
    const indiceInicio = (pagina - 1) * tamanoPagina;
    const indiceFin = indiceInicio + tamanoPagina;
    
    const datosPaginados = datosTransformados.slice(indiceInicio, indiceFin);
    const operacionesPaginadas = operacionesOrdenadas.slice(indiceInicio, indiceFin);

    const paginacion: PaginacionData = {
      numeroPagina: pagina,
      tamanoPagina: tamanoPagina,
      totalElementos: totalElementos,
      totalPaginas: totalPaginas,
      tienePaginaAnterior: pagina > 1,
      tienePaginaSiguiente: pagina < totalPaginas
    };
    
    return {
      _datos: datosPaginados,
      _operaciones: operacionesPaginadas,
      paginacion: paginacion
    };

  } catch (error) {
    console.error("❌ Error en getDataListadoMiBandejaV1:", error);
    return { _datos: [], _operaciones: [], paginacion: null };
  }
};
