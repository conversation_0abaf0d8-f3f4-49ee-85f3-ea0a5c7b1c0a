.menu {
    height: 100%;
    width: 230px;
    position: sticky;
    top: 0;
    display: none;
    flex-direction: column;

    .nav-item{
        background-color: transparent;
        border-radius: $border-radius;

        .nav-link{
            color: $white;

            .fa.fa-chevron-down{
                font-size: .8rem;
                float: right;
                line-height: 2;
            }
        }
    }

    .nav-item.active{
        background-color: $gray-900;
    }

    .nav-item > ul {
        background-color: #0003;
    }
    
    @include media-breakpoint-up(md) {
        width: 230px;
        position: sticky;
        top: 0;display: flex;
    }
}
.content { 
    display: block;
    padding: 0 0 0 0;
    width: 100%;
    overflow-x: hidden;

    @include media-breakpoint-up(md) {
        display: flex;
        flex: 1;
    }
 }

.grid-container{
    display: flex;
    height: calc(100vh - 71px);
    margin-top: 71px;
}

.header-search{
    display: block;
    width: 280px;
    height: calc(1.5em + .75rem + 2px);
    padding: .375rem .75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: $gray-800;
    background-color: transparentize($white, 0.85);
    border: none;
    border-radius: .25rem;
    transition: all .2s ease-in-out;

    &::placeholder{
        color: white;
    }

    &:active, &:focus{
        width: 350px;
        color: $gray-800;
        background-color: $white;

        &::placeholder{
            color: $gray-800;
        }
    }
  }