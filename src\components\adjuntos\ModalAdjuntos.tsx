/// <reference types="webpack/module" />

import React, { useState, useEffect } from "react";
import { CardAdjuntosList } from "./CardAdjuntosList";
import { BtnSubirAdjuntos } from "./BtnSubirAdjuntos";
import { DatosProvider } from "../contextProvider/datosProveedorContext";
import { getAdjuntos } from "../../services/adjuntosServices";

export const ModalAdjuntos = (props: any) => {
  const [cargando, setCargando] = useState(true);
  const [adjuntosCargados, setAdjuntosCargados] = useState(false);
  const [error, setError] = useState(false);
  const [mensajeError, setMensajeError] = useState("Error al cargar los documentos");

  useEffect(() => {
    const cargarAdjuntos = async () => {
      if (props.operacion) {
        setCargando(true);
        setError(false);
        try {
          await getAdjuntos(props.operacion, '3108');
          setAdjuntosCargados(true);
        } catch (error) {
          console.error("Error al cargar adjuntos:", error);
          setError(true);
          
          // Personalizar mensaje según el tipo de error
          if (error.response) {
            // Error de respuesta del servidor
            if (error.response.status === 500) {
              setMensajeError("Error interno del servidor. Por favor, intente más tarde.");
            } else if (error.response.status === 404) {
              setMensajeError("No se encontraron documentos para esta operación.");
            } else if (error.response.status === 403) {
              setMensajeError("No tiene permisos para acceder a estos documentos.");
            } else {
              setMensajeError(`Error al cargar los documentos (${error.response.status})`);
            }
          } else if (error.request) {
            // Error de red o sin respuesta
            setMensajeError("No se pudo conectar con el servidor. Verifique su conexión.");
          } else {
            // Error inesperado
            setMensajeError("Error inesperado al cargar los documentos.");
          }
        } finally {
          setCargando(false);
        }
      } else {
        setCargando(false);
      }
    };

    cargarAdjuntos();
  }, [props.operacion]);

  const jsx: JSX.Element =
    (props.operacion === undefined || props.operacion === null) ?
      <DatosProvider><CardAdjuntosList isInicioDeSeguimiento={true} /></DatosProvider> : 
      <DatosProvider><CardAdjuntosList isInicioDeSeguimiento={false} operacion={props.operacion} /></DatosProvider>

  const botonAdjuntar: JSX.Element = (props.operacion === undefined || props.operacion === null) ?
    <></> : <DatosProvider><BtnSubirAdjuntos /></DatosProvider>

  return (
    <div className="modal fade show" id="filtrosAvanzados" data-backdrop="static" data-keyboard="false" tabIndex={-1} role="dialog" aria-labelledby="filtrosAvanzados" aria-hidden="true">
      <div className="modal-dialog modal-dialog-centered modal-xl">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title" id="filtrosAvanzadosLabel">Documentos</h5>
            <button type="button" className="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">×</span>
            </button>
          </div>

          <div className="modal-body">
            {cargando ? (
              <div className="d-flex justify-content-center align-items-center" style={{ height: '200px' }}>
                <div className="spinner-border text-primary" role="status" style={{ width: '3rem', height: '3rem' }}>
                  <span className="sr-only">Cargando documentos...</span>
                </div>
              </div>
            ) : error ? (
              <div className="d-flex justify-content-center align-items-center" style={{ height: '200px' }}>
                <div className="text-center">
                  <p className="text-danger font-weight-bold" style={{ fontSize: '1.2rem' }}>{mensajeError}</p>
                  <button 
                    className="btn btn-outline-primary mt-3" 
                    onClick={() => {
                      setCargando(true);
                      setError(false);
                      getAdjuntos(props.operacion, '3108')
                        .then(() => {
                          setAdjuntosCargados(true);
                          setCargando(false);
                        })
                        .catch(err => {
                          console.error("Error al reintentar:", err);
                          setError(true);
                          setCargando(false);
                        });
                    }}
                  >
                    Reintentar
                  </button>
                </div>
              </div>
            ) : jsx}
          </div>
          <div className="modal-footer my-2">{botonAdjuntar}</div>
        </div>
      </div>
    </div>
  )
}
