// Variables propias sobreescribe el reboot
@import "bootstrap/custom-variables";

.grid-container{
    height: auto !important;
}

  
 .navbar {
    color: white;
  }
  
  .navbar > li > a
  {
    color: white;
  }
  
  .navbar-primary {
    background-color: #333;
    bottom: 0px;
    left: 0px;
    position: absolute;
    top: 55px;
    width: 200px;
    //float:left;
    z-index: 8;
    overflow: hidden;
    -webkit-transition: all 0.1s ease-in-out;
    -moz-transition: all 0.1s ease-in-out;
    transition: all 0.1s ease-in-out;
  }
  
  .navbar-primary.collapsed {
    width: 50px;
  }
  
  .navbar-primary.collapsed .glyphicon {
    font-size: 22px;
  }
  
  .navbar-primary.collapsed .nav-label {
    display: none;
  }
  
  .btn-expand-collapse {
     // position: absolute;
      display: block;
      left: 0px;
      bottom:0;
      width: 100%;
      padding: 8px 0;
      border-top:solid 1px #666;
      color: grey;
      font-size: 20px;
      text-align: center;
  }
  
  .btn-expand-collapse:hover,
  .btn-expand-collapse:focus {
      background-color: #222;
      color: white;
  }
  
  .btn-expand-collapse:active {
      background-color: #111;
  }
  
  .navbar-primary-menu,
  .navbar-primary-menu li {
    margin:0; padding:0;
    list-style: none;
  }
  
  .navbar-primary-menu li a {
    display: block;
    padding: 10px 18px;
    text-align: left;
    border-bottom:solid 1px #444;
    color: #ccc;

    i:before{
      font-size: 18px;
      text-align: center;
    }
  }
  
  .navbar-primary-menu li a:hover {
    background-color: #000;
    text-decoration: none;
    color: white;
  }
  
  .navbar-primary-menu li a i {
    margin-right: 6px;
  }
  
  .navbar-primary-menu li a:hover i {
    color: $key-lime-pie;
  }
  
  .main-content {
    margin-top: 60px;
    margin-left: 200px;
    padding: 20px;
  }
  
  .collapsed + .main-content {
    margin-left: 60px;
  }

.menu{
    ul{
        li{
        margin-bottom:.5rem;

        &.separador{
            border-bottom: 1px solid $gray-600
        }
        }
    }
}

.menu-filter {
    display: block;
    width: 100%;
    height: calc(1.5em + .75rem + 2px);
    padding: .375rem .75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: white;
    background-color: rgba(255,255,255,0.15);
    border: none;
    border-radius: .25rem;
    transition: all .2s ease-in-out;
    margin-bottom:1rem;
}


select, textarea{
  background-position: right calc(1.375em + .1875rem) center !important;
}

$md-checkbox-checked-color: $fun-blue;
$md-checkbox-border-color: $fun-blue;
$md-checkbox-border-color-disabled: $gray-300;
$md-checkbox-checked-color-disabled: $gray-300;

$md-checkbox-margin: 1em 0;
$md-checkbox-size: 1.25em;
$md-checkbox-padding: .25em;
$md-checkbox-border-width: 2px;
$md-checkbox-border-radius: 0.125em;
$md-checkmark-width: 0.125em;
$md-checkmark-color: $white;
$md-checkbox-label-padding: .75em;

.md-checkbox {
  position: relative;
  margin: $md-checkbox-margin;
  text-align: left;

  &.md-checkbox-inline {
    display: inline-block;
  }
  
  label {
    cursor: pointer;
    display: inline;
    line-height: $md-checkbox-size;
    vertical-align: top;
    clear: both;
    padding-left: 1px;
    &:not(:empty) {
      padding-left: $md-checkbox-label-padding;
    }
    
    &:before, &:after {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
    }
    
    &:before {
      // box
      width: $md-checkbox-size;
      height: $md-checkbox-size;
      background: $white;
      border: $md-checkbox-border-width solid $md-checkbox-border-color;
      border-radius: $md-checkbox-border-radius;
      cursor: pointer;
      transition: background .3s;
    }
  
    &:after {
      // checkmark
    }    
  }
  
  input[type="checkbox"] {
    outline: 0;
    visibility: hidden;
    width: $md-checkbox-size;
    margin: 0;
    display: block;
    float: left;
    font-size: inherit;

    &:checked {
       + label:before{
        background: $md-checkbox-checked-color;
        border:none;
      }
      + label:after {
        
        $md-checkmark-size: $md-checkbox-size - 2 * $md-checkbox-padding;

        transform: translate($md-checkbox-padding, ($md-checkbox-size / 2) - ($md-checkmark-size / 2.6)) rotate(-45deg);
        width: $md-checkmark-size;
        height: $md-checkmark-size / 2;
        
        border: $md-checkmark-width solid $md-checkmark-color;
        border-top-style: none;
        border-right-style: none;
      } 
    }
    
    &:disabled {
      + label:before{
        border-color: $md-checkbox-border-color-disabled;
      }
      &:checked {
        + label:before{
          background: $md-checkbox-checked-color-disabled;
        }
      }
    }
  }
 
}

$md-radio-checked-color: $fun-blue;
$md-radio-border-color: $fun-blue;
$md-radio-size: 20px;
$md-radio-checked-size: 12px; 
$md-radio-ripple-size: 12px;

@keyframes ripple {
  0% {
    box-shadow: 0px 0px 0px 1px rgba(0, 0, 0, 0.0);
  }
  50% { 
    box-shadow: 0px 0px 0px $md-radio-ripple-size rgba(0, 0, 0, 0.1);
  }
  100% {
    box-shadow: 0px 0px 0px $md-radio-ripple-size rgba(0, 0, 0, 0);
  }
}

.md-radio {
    margin: 16px 0;
    
    &.md-radio-inline {
        display: inline-block;
    }

    input[type="radio"] {
        display: none;
        &:checked + label:before {
            border-color: $md-radio-checked-color;
            animation: ripple 0.2s linear forwards;   
        }
        &:checked + label:after {
            transform: scale(1);
        }
    }
    
    label {
        display: inline-block;
        min-height: $md-radio-size;
        position: relative;
        padding: 0 ($md-radio-size + 10px);
        margin-bottom: 0;
        cursor: pointer;
        vertical-align: bottom;
        &:before, &:after {
            position: absolute;            
            content: '';  
            border-radius: 50%;
            transition: all .3s ease;
            transition-property: transform, border-color;
        }
        &:before {
            left: 0;
            top: 0;
            width: $md-radio-size;
            height: $md-radio-size;
            border: 2px solid $md-radio-border-color;
        }
        &:after {
            top: $md-radio-size / 2 - $md-radio-checked-size / 2;
            left: $md-radio-size / 2 - $md-radio-checked-size / 2;
            width:$md-radio-checked-size;
            height:$md-radio-checked-size;
            transform: scale(0);
            background:$md-radio-checked-color;
        }
    }
}
