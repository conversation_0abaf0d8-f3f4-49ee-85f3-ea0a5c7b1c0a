<%@ Language="C#" %>
<%@ Register Tagprefix="SharePoint" Namespace="Microsoft.SharePoint.WebControls" Assembly="Microsoft.SharePoint, Version=********, Culture=neutral, PublicKeyToken=71e9bce111e9429c" %> <%@ Register Tagprefix="Utilities" Namespace="Microsoft.SharePoint.Utilities" Assembly="Microsoft.SharePoint, Version=********, Culture=neutral, PublicKeyToken=71e9bce111e9429c" %> <%@ Import Namespace="Microsoft.SharePoint" %> <%@ Assembly Name="Microsoft.Web.CommandUI, Version=********, Culture=neutral, PublicKeyToken=71e9bce111e9429c" %>
<%@ Import Namespace="Microsoft.SharePoint.ApplicationPages" %>
<%@ Register Tagprefix="WebPartPages" Namespace="Microsoft.SharePoint.WebPartPages" Assembly="Microsoft.SharePoint, Version=********, Culture=neutral, PublicKeyToken=71e9bce111e9429c" %>
<%@ Register TagPrefix="wssuc" TagName="Welcome" src="~/_controltemplates/15/Welcome.ascx" %>
<%@ Register TagPrefix="wssuc" TagName="MUISelector" src="~/_controltemplates/15/MUISelector.ascx" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html lang="<%$Resources:wss,language_value%>" xmlns:o="urn:schemas-microsoft-com:office:office" runat="server"
	dir="<%$Resources:wss,multipages_direction_dir_value%>">

<head runat="server">
	<SharePoint:IEVersionMetaTag runat="server" />
	<meta name="GENERATOR" content="Microsoft SharePoint" />
	<meta name="progid" content="SharePoint.WebPartPage.Document" />
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta http-equiv="Expires" content="0" />
	<SharePoint:RobotsMetaTag runat="server" />
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
	<!-- CSS -->
	<link rel="stylesheet" href="../../css/finansys.css">
	<link rel="stylesheet" href="./c_css/custom.css">
	<link href="https://maxcdn.bootstrapcdn.com/font-awesome/4.3.0/css/font-awesome.min.css" rel="stylesheet"></link>
	<title>Bandeja de Solicitudes</title>
	<script src="https://kit.fontawesome.com/192ee6ec02.js" crossorigin="anonymous"></script>

	<!-- <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script> -->

	<script src="/_layouts/15/sp.runtime.js"></script>
		<script src="/_layouts/15/sp.js"></script>	
		<script src="/_layouts/15/clienttemplates.js"></script>
		<script src="/_layouts/15/clientforms.js"></script>
		<script src="/_layouts/15/clientpeoplepicker.js"></script>
		<script src="/_layouts/15/autofill.js"></script>

		<script src="/_layouts/15/sp.init.js"></script>
		<script src="/_layouts/15/1033/strings.js"></script>
		<script src="/_layouts/15/sp.core.js"></script>
		<!-- <script src="../SiteAssets/employee.js"></script> -->

		<asp:Content ContentPlaceHolderId="PlaceHolderMain" runat="server">
    <SharePoint:ScriptLink name="clienttemplates.js" runat="server" LoadAfterUI="true" Localizable="false" />
    <SharePoint:ScriptLink name="clientforms.js" runat="server" LoadAfterUI="true" Localizable="false" />
    <SharePoint:ScriptLink name="clientpeoplepicker.js" runat="server" LoadAfterUI="true" Localizable="false" />
    <SharePoint:ScriptLink name="autofill.js" runat="server" LoadAfterUI="true" Localizable="false" />
    <SharePoint:ScriptLink name="sp.js" runat="server" LoadAfterUI="true" Localizable="false" />
    <SharePoint:ScriptLink name="sp.runtime.js" runat="server" LoadAfterUI="true" Localizable="false" />
    <SharePoint:ScriptLink name="sp.core.js" runat="server" LoadAfterUI="true" Localizable="false" />
    <!-- <div id="peoplePickerDiv"></div> -->
    <!-- <div id="peoplePickerCE"></div> -->
    <!-- <div>
        <br/>
        <input type="button" value="Get User Info" onclick="getUserInfo()"></input>
        <br/>
        <h1>User info:</h1>
        <p id="resolvedUsers"></p>
        <h1>User keys:</h1>
        <p id="userKeys"></p> 
        <h1>User ID:</h1>
        <p id="userId"></p>
    </div> -->
</asp:Content>

	<!-- <SharePoint:CssLink runat="server" Alternate="true" Version="4" /> -->
	<!-- <SharePoint:Theme runat="server" /> -->
	<!-- <SharePoint:CssRegistration Name="minimalv4.css" runat="server" />
	<SharePoint:CssRegistration Name="layouts.css" runat="server" /> -->

	<!-- <SharePoint:ULSClientConfig runat="server" />
	<SharePoint:ScriptLink language="javascript" name="core.js" Localizable="false" OnDemand="true" runat="server" />
	<SharePoint:CustomJSUrl runat="server" />
	<SharePoint:SoapDiscoveryLink runat="server" />
	<SharePoint:DelegateControl runat="server" ControlId="AdditionalPageHead" AllowMultipleControls="true" />
	<SharePoint:SPShortcutIcon runat="server" IconUrl="/_layouts/15/images/favicon.ico?rev=43" />
	<SharePoint:SPHelpPageComponent Visible="false" runat="server" /> -->
	<style>
		a:hover {
			text-decoration: none;
		}

		#body-row {
			margin-left: 0;
			margin-right: 0;
		}

		#sidebar-container {
			min-height: 100vh;
			background-color: #343A40;
			padding: 0;
		}

		.list-group-item {
			position: relative;
			display: block;
			padding: .75rem 1.25rem;
			background-color: #fff;
			border: 0px solid rgba(0, 0, 0, .125);
		}

		.sidebar-expanded {
			width: 290px;
		}

		.sidebar-collapsed {
			width: 60px;
		}

		#sidebar-container .list-group a {
			height: 50px;
			color: white;
		}

		#sidebar-container .list-group .sidebar-submenu a {
			height: 45px;
			padding-left: 30px;
		}

		.sidebar-submenu {
			font-size: 0.9rem;
		}

		.sidebar-separator-title {
			background-color: #333;
			height: 35px;
		}

		.sidebar-separator {
			background-color: #333;
			height: 25px;
		}

		.logo-separator {
			background-color: #333;
			height: 60px;
		}

		#sidebar-container .list-group .list-group-item[aria-expanded="false"] .submenu-icon::after {
			content: " \f107";
			font-family: FontAwesome;
			display: inline;
			text-align: right;
			padding-left: 10px;
		}

		#sidebar-container .list-group .list-group-item[aria-expanded="true"] .submenu-icon::after {
			content: " \f106";
			font-family: FontAwesome;
			display: inline;
			text-align: right;
			padding-left: 10px;
		}

		.fa-2x {
			font-size: 24px;
		}
	</style>
</head>

<body id = "root" onload="javascript:if (typeof(_spBodyOnLoadWrapper) != 'undefined') _spBodyOnLoadWrapper();">
	
		<form runat="server" onsubmit="if (typeof(_spFormOnSubmitWrapper) != 'undefined') {return _spFormOnSubmitWrapper();} else {return true;}">
			<asp:ScriptManager id="ScriptManager" runat="server" EnablePageMethods="false" EnablePartialRendering="true" EnableScriptGlobalization="false" EnableScriptLocalization="true" />
	<WebPartPages:SPWebPartManager id="m" runat="Server" />
	<SharePoint:FormDigest runat="server"/>
</form>
	<nav class="navbar navbar-expand-lg navbar-dark fixed-top bg-fun-blue">
		<a class="navbar-brand" href="#"><img src="../../img/brand/bc-bg-dark.svg" class="brand brand--md d-none d-lg-block"></a>
	
		<div class="collapse navbar-collapse" id="navbarText">
			<div class="ml-3">
				<input type="text" class="header-search bg-white" id="inputSearch" placeholder="Buscar clientes, operaciones, etc..." />
			</div>
			<button class="btn btn-primary ml-2" id="buscarClientes">Buscar</button>
			<span id="displayNameNAV" class="ml-auto navbar-text d-flex flex-wrap align-items-center">
	
		</div>
	</nav>

		<div class="grid-container">
			<!-- Sidebar -->
			<div id="sidebar-container" class="sidebar-expanded d-md-block d-sm-block">
			</div><!-- sidebar-container END -->

			<!-- Page content -->
			<div id="container" class="content">

			</div>
		</div>

		<script
  src="https://code.jquery.com/jquery-3.6.1.min.js"
  integrity="sha256-o88AwQnZB+VDvE9tvIXrMQaPlFFSUTR+nldQm1LuPXQ="
  crossorigin="anonymous"></script>
		<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js"
			integrity="sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo"
			crossorigin="anonymous"></script>
		<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/js/bootstrap.min.js"
			integrity="sha384-wfSDF2E50Y2D1uUdj0O3uMBJnjuUD4Ih7YwaYd1iqfktj0Uod8GCExl3Og8ifwB6"
			crossorigin="anonymous"></script>
		<script src="../../js/dist/list.min.js"></script>
		<script src="../../js/finansys.js"></script>
		<script src="https://unpkg.com/react@18/umd/react.development.js" crossorigin></script>
		<script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js" crossorigin></script>
		<script src="$(BpmConsola).main.js"></script>

		<!---------------------------- Numeric Format ---------------------------->

		<script src="https://cdn.jsdelivr.net/npm/autonumeric@4.5.4"></script>

		<script src="https://cdn.jsdelivr.net/npm/autonumeric@4.10.5/dist/autoNumeric.min.js"></script>

		<script src="https://cdn.jsdelivr.net/npm/react-url@1.0.1/build/index.min.js"></script>

		<!------------------------------------------------------------------------>

		<script>
			// Hide submenus
			$('#body-row .collapse').collapse('hide');

			// Collapse/Expand icon
			$('#collapse-icon').addClass('fa-angle-double-left');

			// Collapse click
			$('[data-toggle=sidebar-colapse]').click(function () {
				SidebarCollapse();
			});

			function SidebarCollapse() {
				$('.menu-collapsed').toggleClass('d-none');
				$('.sidebar-submenu').toggleClass('d-none');
				$('.submenu-icon').toggleClass('d-none');
				$('#sidebar-container').toggleClass('sidebar-expanded sidebar-collapsed');

				// Treating d-flex/d-none on separators with title
				var SeparatorTitle = $('.sidebar-separator-title');
				if (SeparatorTitle.hasClass('d-flex')) {
					SeparatorTitle.removeClass('d-flex');
				} else {
					SeparatorTitle.addClass('d-flex');
				}

				// Collapse/Expand icon
				$('#collapse-icon').toggleClass('fa-angle-double-left fa-angle-double-right');
			}
		</script>			
</body>

</html>