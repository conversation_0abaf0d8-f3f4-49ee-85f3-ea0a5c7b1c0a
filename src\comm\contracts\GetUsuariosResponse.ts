export type GetUsuariosResponse =
  {
    "codigo": string,
    "nombre": "string",
    "sucursal": {
      "codigo": "string",
      "descripcion": "string"
    },
    "departamento": {
      "codigo": 0,
      "descripcion": "string"
    },
    "correo": "string",
    "nivel": 0

  }

export function isGetUsuariosResponse(GetUsuariosResponse: any): GetUsuariosResponse is GetUsuariosResponse {
  return (GetUsuariosResponse as GetUsuariosResponse != null);
}