/// <reference types="webpack/module" />

import React, { useMemo, useState } from 'react'
import FacturaFormContext from '../contextProvider/counterContext'
import { Factura } from './Factura'
import { FacturaFormFooter } from './FacturaFormFooter'
import { DatosProvider, useProveedor } from '../contextProvider/datosProveedorContext'
import limpiarCamposFactura from '../limpiarCampos/limpiarCamposFactura'

'use strict'

export const FacturaInicioDeSeguimiento = () => {
  const { state } = useProveedor();

  limpiarCamposFactura();

  localStorage.setItem("archivosAdjuntos", "0");

  let [cantFacturas, setCantFacturas] = useState(1)
  let _id = 'btnApretar' + cantFacturas
  const [counter, setCounter] = useState(0)
  const [btnAgregar, setBtnAgregar] = useState(<div className='col'>
  </div>)
  const [facturas, setFacturas] = useState([<>
  </>])
  const [inicioSeguimiento, setInicioSeguimiento] = useState(false);

  state.condicionInicioSeguimiento = inicioSeguimiento;

  const botonAgregarPress = (_cantFactura: number) => {
    setInicioSeguimiento(true);
    console.log(counter)
    _id = 'btnApretar' + counter
    const setting = _cantFactura + 1
    setBtnAgregar(<div className='col'>
    </div>)
    let _facturasJSXHolder = facturas
    _facturasJSXHolder.push(
      <FacturaFormContext.Provider value={{ counter: counter, factura: {}, onlyRead: false }}>
        <DatosProvider><Factura /></DatosProvider>
        <DatosProvider><FacturaFormFooter btnAgregar={btnAgregar} /></DatosProvider>
      </FacturaFormContext.Provider>
    )

    setFacturas(_facturasJSXHolder)
  }

  const collapsarTodos = () => {
    let _botones = document.getElementsByClassName('expandir-principal')
    for (let index = 0; index < _botones.length; index++) {
      const element = _botones[index];
      const button = document.getElementById(element.id)
      if (button.ariaExpanded === 'true') {
        button.click()
      }
    }
  }

  const esconderBotones = () => {
    let _facturas = document.getElementsByClassName('card')
    for (let index = 0; index < _facturas.length; index++) {
      let element = _facturas[index];
      let buttons = element.getElementsByTagName('button')
      for (let index = 0; index < buttons.length; index++) {
        const elementj = buttons[index];
        let unBoton = document.getElementById(elementj.id)
      }
    }
  }

  useMemo(() => {
    setCantFacturas((x) => { let cantF = x + 1; return cantF })
    esconderBotones()
    collapsarTodos()
    botonAgregarPress(cantFacturas)
  }, [counter])

  return (

    <div className="col-md-12">
      {facturas}
    </div>

  )
}