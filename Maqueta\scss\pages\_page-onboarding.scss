@include block('action-bar'){
    background-color: $white;
    box-shadow: 0 6px 20px $gray-400;
    display: flex;
    justify-content: start;
    border-radius: 0;
    width: 100%;
    margin: -25px auto 4rem auto;
    overflow-x: scroll;

    @include media-breakpoint-up(md) {
        justify-content: space-around;
        border-radius: $border-radius;
        width: 750px;
    }

    .nav-link{
        border-bottom: 2px solid transparent;
        line-height: 3rem;
        padding: 0 3rem;
        text-decoration: none;

        &:hover{
            text-decoration: none;
        }
    }

    .nav-link.active{
        border-bottom: 2px solid $fun-blue;
    }
}

@include block('icons'){
    width: 50px;
    height: auto;

    @include modifier('sm'){
        width: 35px;
    }

    @include modifier('xs'){
        width: 25px;
    }
}

.card-photo {
    width: 100%;
    height: 190px;
    background-color: $gray-100;
    overflow: hidden;
    contain: content;
    border-radius: 4px 4px 0 0;

    img{
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    span {
        position: absolute;
        right: 15px;
        top: 15px;
        font-size: 0.9rem;
        box-shadow: 0 0 40px 35px #ffff;
    }
    .action-required{
        position: absolute;
        bottom: 5px;
        margin-bottom: 0;
        text-align: center;
        width: 100%;
        background:#ffffffd3;
    }
}

.page-hero {
    padding: 5rem 0;
    background-size: cover !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
}

.products{
    padding-bottom: 5rem;
}

.faq {
    padding: 5rem 0;

    .nav-link {
        padding-left: 0;
    }
}

.result{
    height: 0;
    opacity: 0;
    display: none;
    transition: display .1s linear, opacity .3s ease-in-out, height .6s ease-in;
}

.result__show{
    height: auto;
    opacity: 1;
    display: block;
}

.benefit {
    min-height: 200px;
}

.benefit-title {
    border-radius: $border-radius $border-radius 0 0;
}

.card-hover:hover {
    transform: scale(1.04);
}
.card-hover {
    text-decoration: none !important;
    color: inherit !important;
    transition: all 0.3s ease;
    display: block;
}
#moneda {
    max-width: 29%;
}