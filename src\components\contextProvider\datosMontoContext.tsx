import React, { createContext, useContext, useState } from "react";

interface MontoContextType {
  montoTotal: string;
  setMontoTotal: React.Dispatch<React.SetStateAction<string>>;
}

const MontoContext = createContext<MontoContextType | undefined>(undefined);

export const useMontoContext = () => {
  const context = useContext(MontoContext);
  if (!context) {
    throw new Error("useMontoContext debe ser utilizado dentro de MontoProvider.");
  }
  return context;
};

export const MontoProvider: React.FC<any> = ({ children }: any) => {
  const [montoTotal, setMontoTotal] = useState("");

  return (
    <MontoContext.Provider value={{ montoTotal, setMontoTotal }}>
      {children}
    </MontoContext.Provider>
  );
};

