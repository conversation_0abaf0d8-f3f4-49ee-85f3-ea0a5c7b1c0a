/// <reference types="webpack/module" />

import React, { useContext, useMemo, useState, useEffect } from "react";
import { FacturaGetResponse } from "../../comm/contracts/FacturaGetResponse";
import FacturaFormContext from "../contextProvider/counterContext";
import { useProveedor } from "../contextProvider/datosProveedorContext";
import { NumericFormat } from "react-number-format";

'use strict'

export const FacturaMontos = () => {

  const { state } = useProveedor();

  const [total, setTotal] = useState<number>(0);
  const [condicionVenta, setCondicionVenta] = useState("CONTADO");
  const [pct5, setPct5] = useState<number>(0);
  const [pct10, setPct10] = useState<number>(0);
  const [gravadas5, setGravadas5] = useState<number>(0);
  const [gravadas10, setGravadas10] = useState<number>(0);
  const [iva5, setIva5] = useState<number>(0);
  const [iva10, setIva10] = useState<number>(0);
  const [descripcion, setDescripcion] = useState("");
  const [exentas, setExentas] = useState<number>(0);

  const row = 3
  const maxlength = 258
  const _FacturaFormContext = useContext(FacturaFormContext)
  const _readOnly = _FacturaFormContext.onlyRead
  const _idObj = {
    totalInput: 'total-input' + _FacturaFormContext.counter,
    condicionState: 'condicionState' + _FacturaFormContext.counter,
    pctInput5: 'pcInput5' + _FacturaFormContext.counter,
    pctInput10: 'pcInput10' + _FacturaFormContext.counter,
    gravadas5: 'gravadas5' + _FacturaFormContext.counter,
    gravadas10: 'gravadas10' + _FacturaFormContext.counter,
    iva5: 'iva5' + _FacturaFormContext.counter,
    iva10: 'iva10' + _FacturaFormContext.counter,
    descripcion: 'descripcion' + _FacturaFormContext.counter,
    exentas: 'exentas' + _FacturaFormContext.counter,
  }

  if (_readOnly) {
    useMemo(() => {
      try {
        const _factura = _FacturaFormContext.factura as FacturaGetResponse
        setTotal(_factura.monto.total)
        setIva5(_factura.monto.ivaCincoPorCiento)
        setIva10(_factura.monto.ivaDiezPorCiento)
        setGravadas5(_factura.monto.gravadasCincoPorCiento)
        setGravadas10(_factura.monto.gravadasDiezPorCiento)
        setDescripcion(_factura.items[0].descripcion)
        setPct5(_factura.monto.ivaCincoPorCiento + _factura.monto.gravadasCincoPorCiento)
        setPct10(_factura.monto.ivaDiezPorCiento + _factura.monto.gravadasDiezPorCiento)
        setExentas(_factura.exentas)
      } catch (error) {
        console.log("error catch useMemo MontoFactura:", error);
      }
    }, [])
  }

  useEffect(() => {
    let valor: any = 0;
    setTotal(state.montoTotal || valor);
    setCondicionVenta(state.montoCondicionVenta || "CONTADO");
    setPct5(state.montoCinco || valor);
    setPct10(state.montoDiez || valor);
    setGravadas5(state.montoGravadasCinco || valor);
    setGravadas10(state.montoGravadasDiez || valor);
    setIva5(state.montoIvaCinco || valor);
    setIva10(state.montoIvaDiez || valor);
    setDescripcion(state.montoDescripcion || "");
    setExentas(state.montoExentas || valor);
  }, [state]);

  let monedaSeleccionada = document.getElementById("monendaState" + 0) as HTMLSelectElement | null;
  let tipoMonedaSeleccionada = monedaSeleccionada?.value;

  const handleCondicionVenta = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setCondicionVenta(e.target.value);
  }

  useEffect(() => {
    if (tipoMonedaSeleccionada === "USD") {
      const calculatedGravadas5 = parseFloat((pct5 / 1.05).toFixed(2));
      setGravadas5(isNaN(calculatedGravadas5) ? 0 : calculatedGravadas5);
      const calculatedIva5 = parseFloat((pct5 - calculatedGravadas5).toFixed(2));
      setIva5(isNaN(calculatedIva5) ? 0 : calculatedIva5);
    }

    if (tipoMonedaSeleccionada === "PYG") {
      const calculatedGravadas5 = parseFloat((pct5 / 1.05).toFixed(0));
      setGravadas5(isNaN(calculatedGravadas5) ? 0 : calculatedGravadas5);
      const calculatedIva5 = parseFloat((pct5 - calculatedGravadas5).toFixed(0));
      setIva5(isNaN(calculatedIva5) ? 0 : calculatedIva5);
    }
  }, [pct5, tipoMonedaSeleccionada]);

  useEffect(() => {
    if (tipoMonedaSeleccionada === "USD") {
      const calculatedGravadas10 = parseFloat((pct10 / 1.1).toFixed(2));
      setGravadas10(isNaN(calculatedGravadas10) ? 0 : calculatedGravadas10);
      const calculatedIva10 = parseFloat((pct10 - calculatedGravadas10).toFixed(2));
      setIva10(isNaN(calculatedIva10) ? 0 : calculatedIva10);
    }

    if (tipoMonedaSeleccionada === "PYG") {
      const calculatedGravadas10 = parseFloat((pct10 / 1.1).toFixed(0));
      setGravadas10(isNaN(calculatedGravadas10) ? 0 : calculatedGravadas10);
      const calculatedIva10 = parseFloat((pct10 - calculatedGravadas10).toFixed(0));
      setIva10(isNaN(calculatedIva10) ? 0 : calculatedIva10);
    }
  }, [pct10, tipoMonedaSeleccionada]);

  const handlePct5Change = (value: number, isPct5: boolean) => {
    let valor: number = 0;
    const newValue: any = tipoMonedaSeleccionada === "USD" ? parseFloat(value.toFixed(2)) : parseFloat(value.toFixed(0));
    if (!isNaN(newValue)) {
      isPct5 ? setPct5(newValue) : setPct5(valor);
    } else {
      setPct5(valor);
    }
  }

  const handlePct10Change = (value: number, isPct10: boolean) => {
    let valor: number = 0
    const newValue: any = tipoMonedaSeleccionada === "USD" ? parseFloat(value.toFixed(2)) : parseFloat(value.toFixed(0));
    if (!isNaN(newValue)) {
      isPct10 ? setPct10(newValue) : setPct10(valor);
    } else {
      setPct10(valor);
    }
  };

  const handleExentasChange = (value: number, exent: boolean) => {
    let valor: number = 0;
    const newValue: any = tipoMonedaSeleccionada === "USD" ? parseFloat(value.toFixed(2)) : parseFloat(value.toFixed(0));
    if (!isNaN(newValue)) {
      exent ? setExentas(newValue) : setExentas(valor);
    }
  };

  useEffect(() => {
    const calculatedTotal = Number(exentas) + Number(pct5) + Number(pct10);
    setTotal(isNaN(calculatedTotal) ? 0 : calculatedTotal);
  }, [exentas, pct5, pct10]);

  const handleBlurPct5 = () => {
    if (!pct5) {
      setPct5(0);
    }
  };

  const handleBlurPct10 = () => {
    if (!pct10) {
      setPct10(0);
    }
  };

  const handleBlurExentas = () => {
    if (!exentas) {
      setExentas(0);
    }
  }

  localStorage.setItem("pct5", pct5.toString());
  localStorage.setItem("pct10", pct10.toString());
  localStorage.setItem("descripcion", descripcion);
  localStorage.setItem("exentas", exentas.toString());

  useEffect(() => {
    const montoTotalNumber = parseFloat(state.montoTotal);
    if (montoTotalNumber === 0) {
      document.getElementById(_idObj.totalInput).setAttribute("disabled", "disabled");
    }
  }, []);

  useEffect(() => {
    const montoCincoNumber = parseFloat(state.montoCinco);
    if (montoCincoNumber === 0) {
      document.getElementById(_idObj.pctInput5).setAttribute("disabled", "disabled");
    }
  }, []);

  useEffect(() => {
    const montoDiezNumber = parseFloat(state.montoDiez);
    if (montoDiezNumber === 0) {
      document.getElementById(_idObj.pctInput10).setAttribute("disabled", "disabled");
    }
  }, []);

  useEffect(() => {
    const montoGravadasCincoNumber = parseFloat(state.montoGravadasCinco);
    if (montoGravadasCincoNumber === 0) {
      document.getElementById(_idObj.gravadas5).setAttribute("disabled", "disabled");
    }
  }, []);

  useEffect(() => {
    const montoGravadasDiezNumber = parseFloat(state.montoGravadasDiez);
    if (montoGravadasDiezNumber === 0) {
      document.getElementById(_idObj.gravadas10).setAttribute("disabled", "disabled");
    }
  }, []);

  useEffect(() => {
    const montoIvaCincoNumber = parseFloat(state.montoIvaCinco);
    if (montoIvaCincoNumber === 0) {
      document.getElementById(_idObj.iva5).setAttribute("disabled", "disabled");
    }
  }, []);

  useEffect(() => {
    const montoIvaDiezNumber = parseFloat(state.montoIvaDiez);
    if (montoIvaDiezNumber === 0) {
      document.getElementById(_idObj.iva10).setAttribute("disabled", "disabled");
    }
  }, []);

  useEffect(() => {
    const montoExentasNumber = parseFloat(state.montoExentas);
    if (montoExentasNumber === 0) {
      document.getElementById(_idObj.exentas).setAttribute("disabled", "disabled");
    }
  }, []);

  return (
    <form className="invoice-price">
      <div className="invoice-price-left">
        <form>
          <div className="row">
            <div className="col d-flex flex-column justify-content-center align-items-center my-2">
              <label htmlFor={_idObj.totalInput}><strong>TOTAL</strong></label>
              {
                state.montoTotal ? <NumericFormat id={_idObj.totalInput} value={state.montoTotal} className="form-control bg-light" thousandSeparator="," decimalSeparator="." disabled />
                  : <NumericFormat id={_idObj.totalInput} name="total" value={total} className="form-control bg-light" readOnly thousandSeparator="," decimalSeparator="." />
              }
            </div>
            <div className="col d-flex flex-column justify-content-center align-items-center my-2">
              <label htmlFor={_idObj.condicionState}>Condicion Venta</label>
              {state.montoCondicionVenta ?
                <select id={_idObj.condicionState} className="form-control" value={state.montoCondicionVenta} onChange={handleCondicionVenta} disabled>
                  <option value="CONTADO">CONTADO</option>
                  <option value="CREDITO">CREDITO</option>
                </select> :
                <select id={_idObj.condicionState} className="form-control" value={condicionVenta} onChange={handleCondicionVenta}>
                  <option value="CONTADO">CONTADO</option>
                  <option value="CREDITO">CREDITO</option>
                </select>}
            </div>
          </div>
          <div className="row">
            <div className="col d-flex flex-column justify-content-center align-items-center my-2">
              <label htmlFor={_idObj.pctInput5}>Monto 5%</label>
              {
                state.montoCinco ? <NumericFormat value={state.montoCinco} onValueChange={({ value }) => handlePct5Change(value === "USD" ? parseFloat(value) : parseFloat(value), true)} id={_idObj.pctInput5} className="form-control bg-light" onBlur={handleBlurPct5} thousandSeparator="," decimalSeparator="." disabled />
                  : <NumericFormat value={pct5} onValueChange={({ value }) => handlePct5Change(value === "USD" ? parseFloat(value) : parseFloat(value), true)} id={_idObj.pctInput5} className="form-control bg-light" onBlur={handleBlurPct5} thousandSeparator="," decimalSeparator="." />
              }
            </div>
            <div className="col d-flex flex-column justify-content-center align-items-center my-2">
              <label htmlFor={_idObj.pctInput10}>Monto 10%</label>
              {
                state.montoDiez ? <NumericFormat value={state.montoDiez} onValueChange={({ value }) => handlePct10Change(value === "USD" ? parseFloat(value) : parseFloat(value), true)} id={_idObj.pctInput10} className="form-control bg-light" onBlur={handleBlurPct10} thousandSeparator="," decimalSeparator="." disabled />
                  : <NumericFormat value={pct10} onValueChange={({ value }) => handlePct10Change(value === "USD" ? parseFloat(value) : parseFloat(value), true)} id={_idObj.pctInput10} className="form-control bg-light" onBlur={handleBlurPct10} thousandSeparator="," decimalSeparator="." />
              }
            </div>
          </div>
          <div className="row">
            <div className="col d-flex flex-column justify-content-center align-items-center my-2">
              <label htmlFor={_idObj.gravadas5}>Gravadas 5%</label>
              {
                state.montoGravadasCinco ? <NumericFormat id={_idObj.gravadas5} value={state.montoGravadasCinco} className="form-control bg-light" thousandSeparator="," decimalSeparator="." disabled />
                  : <NumericFormat id={_idObj.gravadas5} value={gravadas5} className="form-control bg-light" thousandSeparator="," decimalSeparator="." />
              }
            </div>
            <div className="col d-flex flex-column justify-content-center align-items-center my-2">
              <label htmlFor={_idObj.gravadas10}>Gravadas 10%</label>
              {
                state.montoGravadasDiez ? <NumericFormat id={_idObj.gravadas10} value={state.montoGravadasDiez} className="form-control bg-light" thousandSeparator="," decimalSeparator="." disabled />
                  : <NumericFormat id={_idObj.gravadas10} value={gravadas10} className="form-control bg-light" thousandSeparator="," decimalSeparator="." />
              }
            </div>
          </div>
          <div className="row">
            <div className="col d-flex flex-column justify-content-center align-items-center my-2">
              <label htmlFor={_idObj.iva5}>IVA 5%</label>
              {
                state.montoIvaCinco ? <NumericFormat id={_idObj.iva5} value={state.montoIvaCinco} className="form-control bg-light" thousandSeparator="," decimalSeparator="." disabled />
                  : <NumericFormat value={iva5} id={_idObj.iva5} className="form-control bg-light" thousandSeparator="," decimalSeparator="." />
              }
            </div>
            <div className="col d-flex flex-column justify-content-center align-items-center my-2">
              <label htmlFor={_idObj.iva10}>IVA 10%</label>
              {
                state.montoIvaDiez ? <NumericFormat id={_idObj.iva10} value={state.montoIvaDiez} className="form-control bg-light" thousandSeparator="," decimalSeparator="." disabled />
                  : <NumericFormat value={iva10} id={_idObj.iva10} className="form-control bg-light" thousandSeparator="," decimalSeparator="." />
              }
            </div>
          </div>
          <div className="row">
            <div className="form-group col-md-6 d-flex flex-column justify-content-center align-items-center my-2">
              <label htmlFor={_idObj.descripcion}>Descripción</label>
              {
                state.montoDescripcion ? <textarea className="form-control bg-light" value={state.montoDescripcion} onChange={(e) => setDescripcion(e.target.value)} maxLength={maxlength} id={_idObj.descripcion} rows={row} disabled></textarea>
                  : <textarea className="form-control bg-light" value={descripcion} onChange={(e) => setDescripcion(e.target.value)} maxLength={maxlength} id={_idObj.descripcion} rows={row}></textarea>
              }
            </div>
            <div className="col-md-6 d-flex flex-column justify-content-center align-items-center my-2">
              <label htmlFor={_idObj.exentas}>Exentas</label>
              {
                state.montoExentas ? <NumericFormat value={state.montoExentas} onValueChange={({ value }) => handleExentasChange(value === "USD" ? parseFloat(value) : parseFloat(value), true)} id={_idObj.exentas} className="form-control bg-light" onBlur={handleBlurExentas} thousandSeparator="," decimalSeparator="." disabled />
                  : <NumericFormat value={exentas} onValueChange={({ value }) => handleExentasChange(value === "USD" ? parseFloat(value) : parseFloat(value), true)} id={_idObj.exentas} className="form-control bg-light" onBlur={handleBlurExentas} thousandSeparator="," decimalSeparator="." />
              }
            </div>
          </div>
        </form>
      </div>
    </form>
  )
}