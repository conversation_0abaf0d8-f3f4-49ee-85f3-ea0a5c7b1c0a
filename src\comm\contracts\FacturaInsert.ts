import { Articulo } from "../../entities/Articulo"
import { MontoInsert } from "../../entities/MontoInsert"
import { NumeroFactura } from "../../entities/NumeroFactura"

export type FacturaInsert =
  {
    usuarioCarga: string,
    numeroFactura: NumeroFactura,
    idEmisor: number,
    monto: MontoInsert,
    provision: boolean,
    fechaEmision: string,
    tipoComprobante: string,
    timbrado: string
    articulos: Articulo[],
    descripcion: string,
    condicionVenta: string,
    marcaTipoPago: string
  }