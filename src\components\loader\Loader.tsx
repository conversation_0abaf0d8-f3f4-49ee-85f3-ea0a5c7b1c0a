import React, { useState, useEffect } from 'react'

const Loader = () => {
  return (
    <div className="spinner-border text-primary" role="status">
      <span className="sr-only">Loading...</span>
    </div>
  )
}

export const LoaderLista = () => {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  
  const loadingMessages = [
    'Cargando datos...',
    'Buscando tareas pendientes...',
    'Verificando asignaciones...',
    'Actualizando información...',
    'Preparando la vista...',
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentMessageIndex((prevIndex: number) => 
        prevIndex === loadingMessages.length - 1 ? 0 : prevIndex + 1
      );
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="d-flex justify-content-center align-items-center" style={{ 
      minHeight: "300px", 
      width: "100%",
      position: "relative"
    }}>
      <div className="text-center">
        <div className="spinner-border text-primary" role="status">
          <span className="sr-only">Cargando...</span>
        </div>
        <p className="mt-2">{loadingMessages[currentMessageIndex]}</p>
      </div>
    </div>
  );
};

export const LoaderSmall = () => {
  return (
    <div className="spinner-border spinner-border-sm text-warning" role="status">
      <span className="sr-only">Loading...</span>
    </div>
  )
}

export default Loader;