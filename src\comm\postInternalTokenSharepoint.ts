import axios from 'axios'
import { InternalTokenResponse as InternalTokenResponse } from './contracts/InternalTokenResponse';
import { TokenInternoRequestHeader } from './contracts/TokenInternoRequestHeader';
import { TokenInternoMetiriRequestHeader } from './contracts/TokenInternoMetiriRequestHeader';
import { getAmbienteContexto } from '../utilities/contextInfo';
import { getUrlBase } from '../utilities/contextInfo';

export async function postInternalTokenSharepoint() {
  let data: InternalTokenResponse, fResponse, result, _ambiente;
  _ambiente = getAmbienteContexto()
  const RequestHeader = new TokenInternoRequestHeader(_ambiente);
  const request = axios.create({});

  const urlCompleto = {
    urlBase: getUrlBase(),
    urlEndPoint: '/autenticarServicio/v1/realms/interno'
  }

  const config = {
    method: 'post',
    url: urlCompleto.urlBase + urlCompleto.urlEndPoint,
    headers: {
      'Subscription-key': RequestHeader['Subscription-key'],
      'Grant-Type': RequestHeader['Grant-Type'],
      'Client-Id': RequestHeader['Client-Id'],
      'Client-Secret': RequestHeader['Client-Secret'],
      'Scope': RequestHeader['Scope'],
      'Accept': RequestHeader['Accept']
    },
    data: data
  }

  fResponse = request<InternalTokenResponse>(config)
    .then(function (response) {
      console.log("response postInternalTokenSharepoint:", response);
      return response.data
    })
    .catch(function (error) {
      console.log("Error postInternalTokenSharepoint:", error);
    })

  result = await fResponse
  return result;
}

export async function postInternalTokenMetiri() {
  let data: InternalTokenResponse, fResponse, result, _ambiente;
  _ambiente = getAmbienteContexto()
  const RequestHeader = new TokenInternoMetiriRequestHeader(_ambiente);
  const request = axios.create({});

  const urlCompleto = {
    urlBase: getUrlBase(),
    urlEndPoint: '/autenticarServicio/v1/realms/interno'
  }

  const config = {
    method: 'post',
    url: urlCompleto.urlBase + urlCompleto.urlEndPoint,
    headers: {
      'Subscription-key': RequestHeader['Subscription-key'],
      'Grant-Type': RequestHeader['Grant-Type'],
      'Client-Id': RequestHeader['Client-Id'],
      'Client-Secret': RequestHeader['Client-Secret'],
      'Scope': RequestHeader['Scope'],
      'Accept': RequestHeader['Accept']
    },
    data: data
  }

  fResponse = request<InternalTokenResponse>(config)
    .then(function (response) {
      return response.data
    })
    .catch(function (error) {
      console.log(error);
    })

  result = await fResponse
  return result;
}

