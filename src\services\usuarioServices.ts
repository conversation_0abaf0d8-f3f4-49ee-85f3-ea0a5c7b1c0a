import _ from 'lodash';
import { getRolesdeUsuario } from '../comm/apiBpmPoliticasProcesos';
import { getProovedor } from '../comm/apiProveedorFactura';
import { getUsuario } from '../comm/apiUsuarios';
import { isGetUsuariosResponse } from '../comm/contracts/GetUsuariosResponse';
import { isProveedor } from '../entities/Proveedor';
import { Rol } from '../entities/Rol';
import { Usuario } from '../entities/usuario';
import { storeIdUserSitios } from '../utilities/SPandNintexUtilities';
import * as sp from '../utilities/_spPageContextInfo';

export async function getUsuarioDeContexto() {
    let _rolArr: Rol[]
    let _nombreUsuario = sp.getUserName().toString()
    let _rolArrHolder: any = await getRolesdeUsuario(_nombreUsuario).then(function (response) { return response })
    let _getUsuarioResponse = await getUsuario(_nombreUsuario)
    let _tresLetras: string
    if (_rolArrHolder !== undefined) {
        _rolArr = _rolArrHolder
    }
    if (isGetUsuariosResponse(_getUsuarioResponse)) {
        _tresLetras = _getUsuarioResponse.codigo
    }



    let usuario: Usuario =
    {
        NombreUsuario: _nombreUsuario,
        NombreParaMostrar: sp.getDisplayName().toString(),
        TresLetras: _tresLetras,
        RolArr: _rolArr

    };

    localStorage.setItem('usuarioTresLetras', usuario.TresLetras)
    localStorage.setItem('contextNombreUsuario', usuario.NombreUsuario)
    localStorage.setItem('contextNombreParaMostrar', usuario.NombreParaMostrar)
    if(usuario.RolArr !== undefined){
        usuario.RolArr.forEach((element, index) => {

            localStorage.setItem('contextRolCodigo' + index, element.codigo)
            localStorage.setItem('contextRolNombre' + index, element.nombre)
            localStorage.setItem('contextHerenciaNombre' + index, element.herencia)
        });
        localStorage.setItem('usuarioRolLengh',(usuario.RolArr.length).toString())
    }
    


    storeIdUserSitios()


    return usuario;

}

export const obtenerProovedor = async (id: string, tipoDocumento: string) => {
    let _proveedor = await getProovedor(id, tipoDocumento)
    
    if (isProveedor(_proveedor)) {
        return _proveedor
    }
    else{
        alert('No se registra el proveedor.');
        return null
    }
}

export const getUserFromStorage = () =>
{
    const _rolArrLengh = parseInt(localStorage.getItem('usuarioRolLengh'))
    const user : Usuario = 
    {
        NombreUsuario : localStorage.getItem('contextNombreUsuario'),
        NombreParaMostrar : localStorage.getItem('contextNombreParaMostrar'),
        TresLetras : localStorage.getItem('usuarioTresLetras'),
        RolArr : [] as  Rol[]

    }
   
    for (let index = 0; index < _rolArrLengh; index++) {

        const rol : Rol =
        {
            codigo : localStorage.getItem('contextRolCodigo' + index),
            nombre : localStorage.getItem('contextRolNombre' + index),
            herencia : localStorage.getItem('contextHerenciaNombre' + index)

        }
        user.RolArr.push(rol)
    }
    return user
}