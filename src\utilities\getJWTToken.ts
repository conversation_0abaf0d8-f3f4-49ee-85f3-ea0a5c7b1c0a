import { InternalTokenResponse } from "../comm/contracts/InternalTokenResponse";
import { postInternalTokenMetiri, postInternalTokenSharepoint } from "../comm/postInternalTokenSharepoint";

export async function getJWTToken(tokenType: string) {
  let response: any;

  switch (tokenType) {
    case "internal":

      response = await postInternalTokenSharepoint();
      if (isInternalTokenResponse(response)) {
        let token: string = response.access_token.toString();
        return token;
      }
      else {
        return 'hubo un problema accediendo al token'
      }

    case "internal_metiri":
      response = await postInternalTokenMetiri();
      if (isInternalTokenResponse(response)) {
        let token: string = response.access_token.toString();
        return token;
      } else {
        return 'Hubo un problema accediendo al Token';
      }

    default:
      return 'hubo un problema accediendo al token'
  }
}
function isInternalTokenResponse(instanceOf: void | InternalTokenResponse): instanceOf is InternalTokenResponse {
  return (instanceOf as InternalTokenResponse).token_type !== undefined;
}