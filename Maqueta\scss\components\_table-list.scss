@include block('table-list'){
    width: 100%;
    height: 100%;
    border: 1px solid white;
    border-right: 1px solid $gray-400;
    overflow-x: hidden;
    overflow-y: scroll;

    @include media-breakpoint-up(md) {
        width: 35%;
        max-width: 50%;
        min-width: 35%;
        resize: horizontal;
        height: auto;
    }

    @include element('header'){
        padding: 20px;
    }
}

.table-list::-webkit-scrollbar, .preview::-webkit-scrollbar {
    width: 4px;
    border: 1px solid $gray-100;
}
 
.table-list::-webkit-scrollbar-track, .preview::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
}
 
.table-list::-webkit-scrollbar-thumb, .preview::-webkit-scrollbar-thumb {
  background-color: $gray-500;
}

@include block('preview'){
    width: 100%;
    max-width: 100%;
    height: 100%;
    max-height: 100%;
    overflow: hidden;
    overflow-y: scroll;
    position: fixed;
    top: 71px;
    right: -100%;
    opacity: 0;
    transition: right 0.3s ease-in, opacity 0.3s ease-in;
    background: #fff;
    display: none;


    @include modifier('show'){
        right: 0 !important;
        opacity: 1;
        display: block;
    }

    @include media-breakpoint-up(md) {
        width: 65%;
        max-width: 65%;
        top: auto;
        position: relative;
        right: -65%;
    }
}

@include block('default'){
    width: 100%;
    max-width: 100%;
    height: 100%;
    max-height: 100%;
    overflow: hidden;
    position: fixed;
    top: 71px;
    right: -100%;
    opacity: 0;
    display: none;
    align-items: center;
    justify-content: center;
    transition: right 0.3s ease-in, opacity 0.3s ease-in;
    background: #fff;


    @include modifier('show'){
        right: 0 !important;
        opacity: 1;
        display: none;
        justify-content: center;
        align-items: center;

        @include media-breakpoint-up(md) {
            display: flex;
        }
    }

    @include media-breakpoint-up(md) {
        width: 65%;
        max-width: 65%;
        top: auto;
        position: relative;
        right: -65%;
    }
}

@include block('tbl-item'){
    display: block;
    border-left: none;
    border-bottom: 1px solid $gray-400;
    cursor: pointer;
    position: relative;
    padding: 12px 20px;
    transition: all .3s ease;

    &:hover > .tbl-item__check{
        opacity: 1;
    }

    // Table elements
    @include element('check'){
        position: absolute;
        top: 12px;
        left: 5px;
        opacity: 0;
    }

    @include element('name'){
        width: 80%;
        font-weight: bold;    
        padding: 0 5px;
    }

    @include element('subproduct'){
        width: 80%;
        padding: 0 5px;
        font-size: 0.9em;
    }

    @include element('date'){
        width: auto;
        text-align: right;
        padding: 0 5px;
        position: absolute;
        right: 10px;
        bottom: 15px;
        font-size: 0.75em;
    }

    @include element('total'){
        padding: 0 5px;
        width: 100%;
        font-size: 0.9em;
    }
    
    @include element('state'){
        width: auto;
        padding: 0 5px;
        position: absolute;
        right: 10px;
        top: 15px;
    }

    // Modifiers
    @include modifier('header'){
        font-weight: $font-weight-bold;
        background: #fff;
        position: sticky;
        top: 0;
        z-index: 2;
        display: none !important;

        @include media-breakpoint-up(md) {
            display: flex !important;
        }
    }

    @include modifier('selected'){
        background-color: $gray-200;

        > .tbl-item__check{
            opacity: 1;
        }
    }
}

