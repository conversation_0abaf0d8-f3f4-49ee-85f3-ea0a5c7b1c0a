interface InicioSeguimientoRequest {
    usuarioIniciante: {
        tresLetras: string;
        ldap: string;
    };
    codigoCliente: string;
    codigoProducto: number;
    codigoSubProducto: number;
    codigoSucursal: string;
    codigoBanca: number;
    idSharePoint: number;
}

interface UpdateBpmCabeceraRequest{    
    producto: number;
    subproducto: number;
    operacion: string;
    sharedpointId: number;
}