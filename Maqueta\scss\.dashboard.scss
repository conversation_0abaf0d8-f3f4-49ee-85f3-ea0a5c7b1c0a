/*
 
 ██████╗  █████╗ ███████╗██╗  ██╗██████╗  ██████╗  █████╗ ██████╗ ██████╗ 
 ██╔══██╗██╔══██╗██╔════╝██║  ██║██╔══██╗██╔═══██╗██╔══██╗██╔══██╗██╔══██╗
 ██║  ██║███████║███████╗███████║██████╔╝██║   ██║███████║██████╔╝██║  ██║
 ██║  ██║██╔══██║╚════██║██╔══██║██╔══██╗██║   ██║██╔══██║██╔══██╗██║  ██║
 ██████╔╝██║  ██║███████║██║  ██║██████╔╝╚██████╔╝██║  ██║██║  ██║██████╔╝
 ╚═════╝ ╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝╚═════╝  ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═╝╚═════╝ 
 Estilos para login
  
  - Importamos los archivos base
  - Importamos los archivos que vamos a utilizar desde bootstrap.scss                                                                         
 
*/


// Archivos Base Bootstrap
//////////////////////////////////////////////////////////

@import "functions";
@import "variables";
@import "mixins";


// Base & Vendor
// Fonts utilizados - Librerias
//////////////////////////////////////////////////////////

@import "base/body";
@import "base/font-face";
@import "base/icons";
@import "vendor/bemify/bemify";
@import "vendor/swiper/swiper";
@import "vendor/mediaqueries/media-queries";
@import "vendor/arrows/arrows";

// Variables propias sobreescribe el reboot
//////////////////////////////////////////////////////////

@import "custom-variables";
@import "reboot";

// Componentes bootstrap que utilizamos
///////////////////////////////////////////////////////////

@import "type";
@import "forms";
@import "custom-forms";
@import "buttons";
@import "dropdown";
@import "grid";

// Componentes con mixins propios
///////////////////////////////////////////////////////////
@import "components/wrapper";
@import "components/buttons";
@import "components/labels";
@import "components/dropdown-arrow";
@import "components/avatar";
@import "components/card-cuenta";
@import "components/card-operaciones";
@import "components/titulos";



// Page & Layout
///////////////////////////////////////////////////////////

@import "layout/layout-dashboard";
@import "layout/layout-menu-aside";
@import "layout/layout-submenu-aside";
@import "layout/layout-menu-mobile";
@import "layout/layout-header";
@import "pages/page-dashboard";


// Utilities
///////////////////////////////////////////////////////////

@import "utilities/text";
@import "utilities/tachyons/_variables";
@import "utilities/tachyons/_flexbox";
@import "utilities/tachyons/_widths";
@import "utilities/tachyons/_heights";
@import "utilities/tachyons/_spacing";
@import "utilities/tachyons/_font-weight";
@import "utilities/tachyons/_text-align";
@import "utilities/tachyons/_borders";
@import "utilities/tachyons/_border-colors";
@import "utilities/tachyons/_border-radius";
@import "utilities/tachyons/_type-scale";
@import "utilities/tachyons/_position";
@import "utilities/tachyons/_skins";

