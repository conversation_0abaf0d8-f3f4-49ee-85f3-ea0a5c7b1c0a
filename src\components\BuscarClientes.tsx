import React, { useState } from 'react';
import { getAmbienteContexto, getUrlBase } from '../utilities/contextInfo';
import axios from 'axios';
import EndPoint from '../comm/EndPoint.Config';
import { getApiProveedorFacturaHeaders } from '../comm/contracts/ApiBpmPoliticasProcesosRequestHeaders';
import { Estados } from '../entities/Enums/estadosRespuesta';
import { ContainerRender } from './Container';
import { DatosProvider, useProveedor } from './contextProvider/datosProveedorContext';
import { LoaderSmall } from './loader/Loader';
import { ListArmado } from './listado/armarListado';
import { getDataListadoMiBandejaV1 } from '../services/operacionesService';

const BuscarClientes = () => {
  const { state } = useProveedor();
  const [dato, setDato] = useState({ inputSearch: '', ultimoFiltro: '' });
  const [isLoading, setIsLoading] = useState(false);

  localStorage.setItem('datoInputSearch', dato.inputSearch);

  const Params = {
    urlBase: getUrlBase(),
    ambiente: getAmbienteContexto(),
    request: axios.create(),
    endpoints: EndPoint.apiBpmPoliticaProcesos,
  }

  const obtenerOperacionesPaginado = async (codFiltro: string, pagina: number = 1) => {
    const cacheControl = 'max-age=60*60*10, private';
    const headers = await getApiProveedorFacturaHeaders(cacheControl);
    const sturlEndpoint = Params.endpoints.vDos.get.operaciones;
    const urlEndpoint = sturlEndpoint.replace('${proceso}', 'SEGFAC');
    let searchValue = dato.inputSearch.replace(/-/g, '');

    if (codFiltro === 'ATNOMB') {
      searchValue = searchValue.toUpperCase();
    }

    const queryParams: string[] = [];
    
    if (codFiltro === 'ATRCOC') {
      queryParams.push(
        `tipoAtributo=ATRCOC`,
        `valorAtributo=${searchValue}`,
        `paginaActual=${pagina}`,
        `tamanoPagina=10`,
        `ordenDescendente=true`
      );
    } else if (codFiltro === 'ATRFAC') {
      queryParams.push(
        `tipoAtributo=ATRFAC`,
        `valorAtributo=${searchValue}`,
        `paginaActual=${pagina}`,
        `tamanoPagina=10`,
        `ordenDescendente=true`
      );
    } else if (codFiltro === 'idOperacion') {
      queryParams.push(
        `idOperacion=${searchValue}`,
        `paginaActual=${pagina}`,
        `tamanoPagina=10`,
        `ordenDescendente=true`
      );
    } else if (codFiltro === 'ATNOMB') {
      queryParams.push(
        `tipoAtributo=ATNOMB`,
        `valorAtributo=${searchValue}`,
        `paginaActual=${pagina}`,
        `tamanoPagina=10`,
        `ordenDescendente=true`
      );
    } else if (codFiltro === 'ATRDOC') {
      queryParams.push(
        `tipoAtributo=ATRDOC`,
        `valorAtributo=${searchValue}`,
        `paginaActual=${pagina}`,
        `tamanoPagina=10`,
        `ordenDescendente=true`
      );
    }
    
    const complemento = `?${queryParams.join('&')}`;
    
    const config = {
      method: "get" as const,
      url: Params.urlBase + urlEndpoint + complemento,
      headers
    };

    const response = await Params.request(config);
    
    // Actualizar el state con los datos obtenidos
    if (response.status === Estados.estadoExitoso && response.data?.elementos) {
      state.operacionCliente = response.data.elementos;
      state.statusObtenerOperaciones = Estados.estadoExitoso.toString();
    } else {
      state.operacionCliente = [];
      state.statusObtenerOperaciones = response.status?.toString() || '500';
    }
    return response.data;
  };

  // Función para manejar paginación de búsqueda
  (window as any).handleBusquedaPaginada = async (nuevaPagina: number) => {
    if (dato.inputSearch?.trim() && dato.ultimoFiltro) {
      try {
        setIsLoading(true);
        const response = await obtenerOperacionesPaginado(dato.ultimoFiltro, nuevaPagina);
        if (response?.elementos && response.elementos.length > 0) {
          state.paginacionBusqueda = {
            numeroPagina: response.numeroPagina,
            tamanoPagina: response.tamanoPagina,
            totalElementos: response.totalElementos,
            totalPaginas: response.totalPaginas,
            tienePaginaAnterior: response.tienePaginaAnterior,
            tienePaginaSiguiente: response.tienePaginaSiguiente
          };
          
          // Renderizar directamente con los nuevos datos
          ContainerRender({
            component: (
              <DatosProvider>
                <div id="idTabla" className="table-list">
                  <div id="headerBandeja" className="table-list__header text-center">
                    <h3 id="cabecera" className="mb-4">Mi Bandeja - Resultados de Búsqueda</h3>
                  </div>
                  <div id="listContenido">
                    <ListArmado 
                      operaciones={state.operacionCliente} 
                      tareas={[]}
                      paginacion={state.paginacionBusqueda}
                      onPageChange={(pagina: number) => {
                        (window as any).handleBusquedaPaginada(pagina);
                      }}
                      esBusquedaFiltrada={true}
                    />
                  </div>
                </div>
              </DatosProvider>
            )
          });
          
          // Actualizar también el estado global
          if ((window as any).handleBusquedaFiltrada) {
            (window as any).handleBusquedaFiltrada('update', '', state.operacionCliente, state.paginacionBusqueda);
          }
        }
      } catch (error) {
        console.error("Error en paginación de búsqueda:", error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDato({
      ...dato,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (codFiltro: string, event: React.MouseEvent) => {
    event.preventDefault();
    if (dato.inputSearch?.trim()) {
      setIsLoading(true);
      try {
        dato.ultimoFiltro = codFiltro;
        const response = await obtenerOperacionesPaginado(codFiltro, 1);
        
        // Verificar si hay datos en la respuesta
        if (response?.elementos && response.elementos.length > 0) {
          // Asegurar que los datos estén en el state
          state.operacionCliente = response.elementos;
          
          state.paginacionBusqueda = {
            numeroPagina: response.numeroPagina,
            tamanoPagina: response.tamanoPagina,
            totalElementos: response.totalElementos,
            totalPaginas: response.totalPaginas,
            tienePaginaAnterior: response.tienePaginaAnterior,
            tienePaginaSiguiente: response.tienePaginaSiguiente
          };
          
          // Renderizar directamente con los datos obtenidos
          ContainerRender({
            component: (
              <DatosProvider>
                <div id="idTabla" className="table-list">
                  <div id="headerBandeja" className="table-list__header text-center">
                    <h3 id="cabecera" className="mb-4">Mi Bandeja - Resultados de Búsqueda</h3>
                  </div>
                  <div id="listContenido">
                    <ListArmado 
                      operaciones={response.elementos} 
                      tareas={[]}
                      paginacion={state.paginacionBusqueda}
                      onPageChange={(pagina: number) => {
                        (window as any).handleBusquedaPaginada(pagina);
                      }}
                      esBusquedaFiltrada={true}
                    />
                  </div>
                </div>
              </DatosProvider>
            )
          });
          
          // Actualizar también el estado global
          if ((window as any).handleBusquedaFiltrada) {
            (window as any).handleBusquedaFiltrada('update', '', response.elementos, state.paginacionBusqueda);
          }
        } else {
          // Si no hay elementos, mostrar mensaje de sin resultados
          if ((window as any).handleBusquedaFiltrada) {
            (window as any).handleBusquedaFiltrada('empty', '');
          }
          
          ContainerRender({
            component: (
              <DatosProvider>
                <div id="idTabla" className="table-list">
                  <div id="headerBandeja" className="table-list__header text-center">
                    <h3 id="cabecera" className="mb-4">Mi Bandeja</h3>
                  </div>
                  <div id="listContenido">
                    <div className="text-center" style={{ marginTop: "20%", padding: "20px" }}>
                      <img src="../../img/abi4-error.svg" alt="Sin resultados" style={{ width: "100px", marginBottom: "20px" }} />
                      <h4>No se encontraron resultados</h4>  
                    </div>
                  </div>
                </div>
              </DatosProvider>
            )
          });
        }
      } catch (error) {
        console.error("Error al buscar operaciones:", error);

        // Mostrar mensaje de error visual en lugar de alert
        ContainerRender({
          component: (
            <DatosProvider>
              <div id="idTabla" className="table-list">
                <div id="headerBandeja" className="table-list__header text-center">
                  <h3 id="cabecera" className="mb-4">Mi Bandeja</h3>
                </div>
                <div id="listContenido">
                  <div className="text-center" style={{ marginTop: "20%", padding: "20px" }}>
                    <img src="../../img/abi4-error.svg" alt="Error" style={{ width: "100px", marginBottom: "20px" }} />
                    <h4>No se pudo completar la búsqueda</h4>
                    <p className="text-muted">Verifica los filtros ingresados o intenta más tarde.</p>
                  </div>
                </div>
              </div>
            </DatosProvider>
          )
        });
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Función para manejar paginación de Mi Bandeja V1
  const cargarMiBandejaPaginada = async (pagina: number) => {
    try {
      setIsLoading(true);
      const response = await getDataListadoMiBandejaV1('SEGFAC', pagina, 10);
      
      if (response && response._datos.length > 0) {
        state.operacionCliente = response._operaciones;
        state.paginacionBusqueda = response.paginacion;
        
        // Re-renderizar con nueva página
        ContainerRender({
          component: (
            <DatosProvider>
              <div id="idTabla" className="table-list">
                <div id="headerBandeja" className="table-list__header text-center">
                  <h3 id="cabecera" className="mb-4">Mi Bandeja</h3>
                </div>
                <div id="listContenido">
                  <ListArmado 
                    operaciones={state.operacionCliente} 
                    tareas={[]}
                    paginacion={state.paginacionBusqueda}
                    onPageChange={(nuevaPagina: number) => {
                      cargarMiBandejaPaginada(nuevaPagina);
                    }}
                    esBusquedaFiltrada={false}
                  />
                </div>
              </div>
            </DatosProvider>
          )
        });
      }
    } catch (error) {
      console.error("Error en paginación Mi Bandeja:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <span className='d-flex align-items-center justify-content-start'>
      <div className="ml-3">
        <input 
          type="text" 
          className="header-search" 
          id="inputSearch" 
          name="inputSearch" 
          placeholder="Buscar clientes, operaciones, etc..." 
          onChange={handleChange} 
          value={dato.inputSearch} 
        />
      </div>
      <div className="dropdown">
        <button 
          className="btn btn-primary ml-2 d-flex align-items-center dropdown-toggle" 
          type="button" 
          data-toggle="dropdown" 
          aria-expanded="false"
        >
          Buscar {isLoading && <div className="text-center ml-2"><LoaderSmall /></div>}
        </button>
        <ul className="dropdown-menu">
          <li>
            <button 
              className="dropdown-item text-primary d-flex" 
              onClick={(e) => handleSubmit('ATRCOC', e)}
            >
              Código Cliente
            </button>
          </li>
          <li>
            <button 
              className="dropdown-item text-primary d-flex" 
              onClick={(e) => handleSubmit('idOperacion', e)}
            >
              Número de Operación
            </button>
          </li>
          <li>
            <button 
              className="dropdown-item text-primary d-flex" 
              onClick={(e) => handleSubmit('ATRFAC', e)}
            >
              Número de Factura
            </button>
          </li>
          <li>
            <button
              className="dropdown-item text-primary d-flex"
              onClick={(e) => handleSubmit('ATNOMB', e)}
            >
              Nombre del Cliente
            </button>
          </li>
          <li>
            <button
              className="dropdown-item text-primary d-flex"
              onClick={(e) => handleSubmit('ATRDOC', e)}
            >
              Número de Documento (RUC/CI)
            </button>
          </li>
        </ul>
      </div>
    </span>
  );
}

export default BuscarClientes;