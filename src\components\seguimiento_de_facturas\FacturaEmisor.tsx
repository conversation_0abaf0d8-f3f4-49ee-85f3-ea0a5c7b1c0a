/// <reference types="webpack/module" />

import React, { useContext } from "react";
import { FacturaGetResponse } from "../../comm/contracts/FacturaGetResponse";
import FacturaFormContext from "../contextProvider/counterContext";
import { FacturaEmisorPortal } from "./FacturaEmisorPortal";
import { useProveedor } from "../contextProvider/datosProveedorContext";

export const FacturaEmisor = () => {

  const {state, dispatch} = useProveedor();

  const _FacturaFormContext = useContext(FacturaFormContext)
  const _id = 'facturaEmisor' + _FacturaFormContext.counter
  const _readOnly = _FacturaFormContext.onlyRead

  let emisorProps = { nombre: '', ciudad: 'Ciudad', pais: 'Pais', numeroTelefono: 'Nro de Telefono', numeroDeDocumento: 'Nro de Documento' }
  if (_readOnly) {
    const _factura = _FacturaFormContext.factura as FacturaGetResponse
    console.log('estoy en emisor Props')
    console.log(_factura)
    emisorProps.nombre = _factura.proveedores.nombre + ' ' + _factura.proveedores.apellidoUno
    emisorProps.ciudad = _factura.proveedores.ciudad
    emisorProps.pais = _factura.proveedores.pais
    emisorProps.numeroTelefono = _factura.proveedores.telefono
    emisorProps.numeroDeDocumento = _factura.proveedores.cedula
  }

  return (
    <div className="invoice-from">
      <div className="d-flex">
        <small>factura emitida por</small>
        <small className="mx-2 text-success">{[state.nombre, state.apellido, state.nombreDos, state.apellidoDos].filter(Boolean).join(' ') || state.bandejaNombre}</small>
      </div>
      <address id={_id} className="m-t-5 m-b-5">
      <FacturaEmisorPortal handler={dispatch} {...state} />
      </address>
    </div>
  )
}