/**
 * Utilidades para el manejo de fechas
 */

/**
 * Formatea una fecha ISO a un string legible con fecha y hora
 * @param fechaISO - Fecha en formato ISO string
 * @returns String con formato DD/MM/YYYY HH:MM:SS o la fecha original si hay error
 */
export const formatearFechaConHora = (fechaISO: string): string => {
  try {
    const fecha = new Date(fechaISO);
    const dia = fecha.getDate().toString().padStart(2, '0');
    const mes = (fecha.getMonth() + 1).toString().padStart(2, '0');
    const anio = fecha.getFullYear();
    const hora = fechaISO.slice(11, 19);
    return `${dia}/${mes}/${anio} ${hora}`;
  } catch {
    return fechaISO;
  }
};

/**
 * Obtiene la fecha actual en formato ISO sin la parte de la zona horaria
 * @returns String con la fecha actual en formato YYYY-MM-DD
 */
export const getCurrentDateISO = (): string => {
  return new Date().toISOString().split('T')[0];
};
