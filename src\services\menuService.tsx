import _ from 'lodash';
import { getMenuElementos } from '../comm/apiBpmPoliticasProcesos';
import { isMenuElementoResponse, MenuElementoResponse, isMenuElemento } from '../comm/contracts/GetMenuElementosResponse';

export async function getMenuService(codigoElemento?: string, codigoRol?: string, codigoUsuario?: string) {

  let _menu: void | MenuElementoResponse = await getMenuElementos(codigoElemento, codigoRol, codigoUsuario)

  if (isMenuElementoResponse(_menu)) {
    console.log("_menu.elementos:", _menu.elementos);
    return _menu.elementos
  }

  if (isMenuElemento(_menu)) {
    return _menu
  }
}