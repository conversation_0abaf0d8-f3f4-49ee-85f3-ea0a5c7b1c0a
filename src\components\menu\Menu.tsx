/// <reference types="webpack/module" />

import _, { isNull, isUndefined } from "lodash";
import React from "react";
import { MenuElemento } from "../../entities/MenuElemento";
import { getMenuService } from "../../services/menuService";
import { MenuElementoComp } from "./MenuElementoComponent";
import { MenuSubElementoWrapperComp } from "./MenuSubElementoComponent";
import { DatosProvider } from "../contextProvider/datosProveedorContext";

'use strict'

export class Menu extends React.Component<any, { menu: MenuElemento[], menuRendered: JSX.Element[] }> {
  inicioEl: JSX.Element
  sideBarCollapseEl: JSX.Element
  menuReponse: MenuElemento[]

  constructor(props: any) {
    super(props);
    this.inicioEl = this.renderInicio();
    this.sideBarCollapseEl = this.renderSideBar()
    this.state = { menu: null, menuRendered: [<></>] }
  }

  renderInicio() {
    return (
      <a href="#" className="bg-dark list-group-item list-group-item-action flex-column align-items-start">
        <div className="d-flex w-100 justify-content-start align-items-center">
          <span className="fas fa-home mr-3"></span>
          <span className="menu-collapsed">Inicio</span>
        </div>
      </a>
    )
  }

  renderSideBar() {
    return (
      <a href="#top" data-toggle="sidebar-collapse"
        className="bg-dark list-group-item list-group-item-action d-flex align-items-center">
        <div className="d-flex w-100 justify-content-start align-items-center">
          <span id="collapse-icon" className="fa fa-2x mr-3"></span>
          <span id="collapse-text" className="menu-collapsed"></span>
        </div>
      </a>
    )
  }

  asignarPadres(arr: MenuElemento[]) {
    arr.map((element, index) => {
      if (isUndefined(element.padre)) {
        element.padre = 'a'
      }

      let _tieneHijo = (isNull(element.subelementos) || isUndefined(element.subelementos) || element.subelementos.length == 0) ? false : true;
      if (_tieneHijo) {

        element.subelementos.forEach(hijo => {
          let hijoIndex = arr.findIndex((e, i) => {
            if (e.codigo === hijo.codigo) {
              return true
            }
          })
          arr[hijoIndex].padre = element.codigo
        });
      }
    })
    return arr
  }

  sortMenuArr(menu: MenuElemento[]) {
    menu.sort((a, b) => {
      const nameA = a.proceso.codigo.toUpperCase();
      const nameB = b.proceso.codigo.toUpperCase();
      if (nameA < nameB) {
        return -1;
      }
      if (nameA > nameB) {
        return 1;
      }
      return 0;
    });

    menu.sort((a, b) => {
      const nameA = a.padre.toUpperCase();
      const nameB = b.padre.toUpperCase();
      if (nameA < nameB) {
        return -1;
      }
      if (nameA > nameB) {
        return 1;
      }
      return 0;
    });

    return menu
  }

  async renderMenu() {
    let counter = 0;
    let flag: string = 'a';
    let _menu = await getMenuService()
    _menu = this.asignarPadres(_menu)
    this.setState({ menu: _menu })
    let menuElementosEl = [<></>];
    _menu = this.sortMenuArr(_menu)
    let counterDos = 0;

    _menu.sort((a, b) => {
      if (a.proceso.codigo  < b.proceso.codigo) {
        return -1;
      }
      if (a.proceso.codigo  > b.proceso.codigo) {
        return 1;
      }
      return 0;
    });

    _menu.forEach((element, index) => {
      if (element.proceso.codigo !== flag) {
        counter++
        counterDos = 0;
        flag = element.proceso.codigo
      }

      let _tieneHijo = (isNull(element.subelementos) || isUndefined(element.subelementos) || element.subelementos.length == 0) ? false : true;
      let _tienePadre = (element.padre == 'a') ? false : true;


      if (_tienePadre) {
        let _menuHolder = _menu
        let childHolder = _menuHolder.filter(mEl => mEl.padre == element.padre)
        let subMenu = [<></>]

        childHolder.forEach((child) => {

          subMenu.push(<DatosProvider><MenuElementoComp
            codigo={child.codigo}
            nombre={child.nombre}
            descripcion={child.Descripcion}
            tieneHijo={_tieneHijo}
            tienePadre={_tienePadre}
            productoNombre={child.proceso.codigo.trim()}
            idNumber={counter.toString()}
          /></DatosProvider>)
        });

        counterDos++
        
        if (counterDos === 1) {
          let _subMenuRender = <MenuSubElementoWrapperComp procesoNombreId={element.proceso.codigo.trim() + counter.toString()} >
            {subMenu}
          </MenuSubElementoWrapperComp>
          menuElementosEl.push(_subMenuRender)
        }
      }
      else {
        counterDos = 0
        let jsxMenu = <DatosProvider><MenuElementoComp
          codigo={element.codigo}
          nombre={element.nombre}
          descripcion={element.Descripcion}
          tieneHijo={_tieneHijo}
          tienePadre={_tienePadre}
          productoNombre={element.proceso.codigo.trim()}
          idNumber={counter.toString()}
        /></DatosProvider>
        menuElementosEl.push(jsxMenu)
      }
    });

    this.setState({ menuRendered: menuElementosEl });
  }

  componentDidMount(): void {
    this.renderMenu();
    
    // Agregar función global para limpiar búsqueda
    (window as any).limpiarBusquedaDesdeMenu = () => {
      if ((window as any).limpiarBusquedaYVolverBandeja) {
        (window as any).limpiarBusquedaYVolverBandeja();
      }
    };
  }



  render() {
    return (
      <ul className="list-group pl-0">
        {this.inicioEl}
        {this.state.menuRendered}
        {this.sideBarCollapseEl}
      </ul>
    )
  }
}
