/*

 █████  ██      ████████  █████                ██████  ███████ 
██   ██ ██         ██    ██   ██               ██   ██ ██      
███████ ██         ██    ███████     █████     ██████  █████   
██   ██ ██         ██    ██   ██               ██      ██      
██   ██ ███████    ██    ██   ██               ██      ██      
                                                           

*/


// Archivos Base Bootstrap
@import "bootstrap/functions";
@import "bootstrap/variables";
@import "bootstrap/mixins";

// Base & Vendor
@import "base/font-face";
@import "vendor/bemify/bemify";

// Variables propias sobreescribe el reboot
@import "bootstrap/custom-variables";
@import "bootstrap/reboot";

// Componentes bootstrap que utilizamos
@import "bootstrap/progress";

// Componentes con mixins propios
@import "components/timeline";
@import "components/progress";


hr{
  border:0; 
  border-bottom: solid 1px lighten($color: $blue, $amount: 50);  
}

.main-title{
    margin: 0 auto;
    padding: 1rem 0 .5rem;
    border-top: solid 1px lighten($color: $blue, $amount: 50); 
    border-bottom: solid 1px lighten($color: $blue, $amount: 50);  
    
    h1{
        font-size: 1.5rem;
        line-height: 2rem;
    }

    h5{
      margin-bottom:.5rem;
    }
}

.secondary-title{

    h2{
        font-size: 1.3rem;
        line-height: 1.8rem;
        text-transform: uppercase;
        span{
            width: 100%;
            display: block;
        }
    }
}

.modal-footer{
  justify-content: center;
  
}

.placeholder-img{
  width: 130px;
  height: 130px;
  border-radius: 150px;
  overflow: hidden;
  background: url("../img/alta/placeholder-img.png") center no-repeat;
  border: 2px solid $gray-100;
  transition: linear;

  &:hover{
    transition: linear;
    border-color: $key-lime-pie;
  }
}

.modal{
  .nav-item{
    text-transform: uppercase;
    border-bottom: none;

    a.nav-link{
      color: $gray-600;
      font-size: 1rem;
      //letter-spacing: 1px;
      font-weight: bold;
      border: transparent;
      transform: perspective(1px) translateZ(0);
      &.active,&:hover{
        color: $fun-blue;
        border-bottom: $fun-blue solid 2px;
      }
      &:hover{
        transition: ease;
      }
      /*&:before{
        content: "";
        position: absolute;
        z-index: -1;
        left: 0;
        right: 0;
        bottom: 0;
        background: #2098D1;
        height: 4px;
        -webkit-transform: translateY(4px);
        transform: translateY(4px);
        -webkit-transition-property: transform;
        transition-property: transform;
        -webkit-transition-duration: 0.3s;
        transition-duration: 0.3s;
        -webkit-transition-timing-function: ease-out;
        transition-timing-function: ease-out;
      }*/
    }
  }
}

.card-body{
  padding: 1rem;
}

.bg-light-blue{
  background: #D1E4F9;
}

.text-light-blue{
  color: #D1E4F9;
}

.border-light-blue{
  border-bottom: solid 2px;
  border-color: lighten(#D1E4F9,5%);
}

.b-light-blue{
  border-color: darken(#D1E4F9,5%);
}

.fa-angle-up{
  position: absolute;
  right: 30px;
}

[data-toggle="collapse"] .fa-angle-up {   
  //content: "\f139";
  transform: rotate(0deg);
  transition: all 100ms;
}

[data-toggle="collapse"].collapsed .fa-angle-up {
  content: "\f107";
  transform: rotate(180deg);
  transition: all 100ms;
}

// TABLA
 /*
@include block('tabla-alta'){
  width: 100%;
  height: 100%;
  //border-right: 1px solid $gray-400;
  //overflow-x: hidden;
  //overflow-y: scroll;

  @include media-breakpoint-up(md) {
      //width: 35%;
      //max-width: 50%;
      //min-width: 35%;
      resize: horizontal;
      height: auto;
  }

  @include element('header'){
    text-transform: uppercase;
    color: $gray-700;
  }
}

@include block('row-alta'){
  @include element('tipo'){
    width: 20%;
  }

  @include element('prefijo'){
    width: 20%;
  }

  @include element('telefono'){
    width: 20%;
  }

  @include element('editar'){
    width: 10%;
  }

  @include element('elimninar'){
    width: 10%;
  }

  @include element('principal'){
    width: 10%;
  }
}

*/


// TABLA

.tabla-alta, .d-table{
  display: flex;
  flex-wrap: wrap;
  margin: 0;
  padding: 0;
  //box-shadow: 0 0 40px rgba(0,0,0,0.2);

  .row-alta, .d-table-row{
    width: 100%;
    display: flex;
    
    .cell-alta, .d-table-cell{
      box-sizing: border-box;
      flex-grow: 1;
      padding: 1em 1.2em 0em;
      overflow: hidden; // Or flex might break
      list-style: none;
      color: black;

      &.cell-heading{
        //font-weight: bold;
        color: $gray-600;
        text-transform: uppercase;
        //letter-spacing: 1px;
        padding: 1em;
      }
    }
  }
}

/* TEXTAREA AUTO RESIZE STYLE */
.txta {
  width: 100%;
  max-width: 500px;
  min-height: 100px;
  overflow: hidden;
  line-height: 1.4;
}
