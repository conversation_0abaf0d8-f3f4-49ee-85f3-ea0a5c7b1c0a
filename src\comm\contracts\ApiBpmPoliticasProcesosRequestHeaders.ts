import { getAmbienteContexto } from "../../utilities/contextInfo"
import { Ambiente } from "../../entities/Enums/enumAmbientes"
import { getJWTToken } from "../../utilities/getJWTToken";

export type ApiBpmPoliticasProcesosRequestHeaders =
  {
    Authorization: string,
    'Subscription-Key': string,
    'Content-Type': string,
    Accept: string,
    'Cache-Control': string
  }

export async function getApiBpmPoliticasProcesosRequestHeaders(cache: string) {
  let _apiBpmPoliticasProcesosRequestHeaders: ApiBpmPoliticasProcesosRequestHeaders;
  let _ambiente = getAmbienteContexto();
  let _jwtToken = await getJWTToken('internal');

  switch (_ambiente) {
    case Ambiente.dev:
      _apiBpmPoliticasProcesosRequestHeaders =
      {
          Authorization: 'Bearer ' + _jwtToken,
          "Subscription-Key": '88b20fe90a2d46529b7a5e9e9af3f38c',
          "Content-Type": 'application/json',
          Accept: 'application/json',
          'Cache-Control': cache
        }
        return _apiBpmPoliticasProcesosRequestHeaders;

    case Ambiente.qa:
      _apiBpmPoliticasProcesosRequestHeaders =
      {
          Authorization: 'Bearer ' + _jwtToken,
          "Subscription-Key": 'a1d6ebd894334156a5bf2ed7f38bb599',
          "Content-Type": 'application/json',
          Accept: 'application/json',
          'Cache-Control': cache
        }
        return _apiBpmPoliticasProcesosRequestHeaders;

    case Ambiente.prod:
      _apiBpmPoliticasProcesosRequestHeaders = {
        Authorization: 'Bearer ' + _jwtToken,
        "Subscription-Key": '2966f3b11c15480cb45c4aacb2f8965e',
        "Content-Type": 'application/json',
        Accept: 'application/json',
        'Cache-Control': cache
      }
      return _apiBpmPoliticasProcesosRequestHeaders;
  }
}
export async function getApiProveedorFacturaHeaders(cache: string) {
  let _apiBpmPoliticasProcesosRequestHeaders: ApiBpmPoliticasProcesosRequestHeaders;
  let _ambiente = getAmbienteContexto();
  let _jwtToken = await getJWTToken('internal');


  switch (_ambiente) {
    case Ambiente.dev:
      _apiBpmPoliticasProcesosRequestHeaders =
      {
        Authorization: 'Bearer ' + _jwtToken,
        "Subscription-Key": '88b20fe90a2d46529b7a5e9e9af3f38c',
        "Content-Type": 'application/json',
        Accept: 'application/json',
        'Cache-Control': cache
      }
      return _apiBpmPoliticasProcesosRequestHeaders;

    case Ambiente.qa:
      _apiBpmPoliticasProcesosRequestHeaders =
      {
        Authorization: 'Bearer ' + _jwtToken,
        "Subscription-Key": 'a1d6ebd894334156a5bf2ed7f38bb599',
        "Content-Type": 'application/json',
        Accept: 'application/json',
        'Cache-Control': cache
      }
      return _apiBpmPoliticasProcesosRequestHeaders;

    case Ambiente.prod:
      _apiBpmPoliticasProcesosRequestHeaders = {
        Authorization: 'Bearer ' + _jwtToken,
        "Subscription-Key": '2966f3b11c15480cb45c4aacb2f8965e',
        "Content-Type": 'application/json',
        Accept: 'application/json',
        'Cache-Control': cache
      }
      return _apiBpmPoliticasProcesosRequestHeaders;
  }
}

export async function getApiUsuarioHeaders(cache: string) {
  let _apiBpmPoliticasProcesosRequestHeaders: ApiBpmPoliticasProcesosRequestHeaders;
  let _ambiente = getAmbienteContexto();
  let _jwtToken = await getJWTToken('internal');


  switch (_ambiente) {
    case Ambiente.dev:
      _apiBpmPoliticasProcesosRequestHeaders =
      {
        Authorization: 'Bearer ' + _jwtToken,
        "Subscription-Key": '47a1892f6b294f70b669d3baaa075dc5',
        "Content-Type": 'application/json',
        Accept: 'application/json',
        'Cache-Control': cache
      }
      return _apiBpmPoliticasProcesosRequestHeaders;

    case Ambiente.qa:
      _apiBpmPoliticasProcesosRequestHeaders =
      {
        Authorization: 'Bearer ' + _jwtToken,
        "Subscription-Key": 'a1d6ebd894334156a5bf2ed7f38bb599',
        "Content-Type": 'application/json',
        Accept: 'application/json',
        'Cache-Control': cache
      }
      return _apiBpmPoliticasProcesosRequestHeaders;

    case Ambiente.prod:
      _apiBpmPoliticasProcesosRequestHeaders = {
        Authorization: 'Bearer ' + _jwtToken,
        "Subscription-Key": '2966f3b11c15480cb45c4aacb2f8965e',
        "Content-Type": 'application/json',
        Accept: 'application/json',
        'Cache-Control': cache
      }
      return _apiBpmPoliticasProcesosRequestHeaders;
  }
}
