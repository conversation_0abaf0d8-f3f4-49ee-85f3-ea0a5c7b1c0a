import { Atributo } from './Atributo'
import { Proceso } from './Proceso'

export type Operacion = {

  id: number,
  fechaHoraCreacion: string,
  fechaHoraMofificado: string,
  fechaHoraFinDate: string,
  usuarioIniciador: string,
  codigoEstado: string,
  proceso: Proceso
  atributos: Atributo[]
}

export const isOperacionArr = (Operacion: any): Operacion is Operacion[] => {
  return (Operacion as Operacion[]).length > 0;
}
