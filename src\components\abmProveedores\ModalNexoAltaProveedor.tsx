import React, { useState, useEffect } from 'react';
import { getAmbienteContexto, getUrlBase } from '../../utilities/contextInfo';
import axios, { AxiosResponse } from 'axios';
import EndPoint from '../../comm/EndPoint.Config';
import { getApiProveedorFacturaHeaders } from '../../comm/contracts/ApiBpmPoliticasProcesosRequestHeaders';
import { ErrorResponse } from '../../comm/contracts/ErrorResponse';
import { Dispatch, useProveedor } from '../contextProvider/datosProveedorContext';
import { Proveedor } from '../../entities/Proveedor';
import { AltaProveedorSuccess } from '../../entities/AltaProveedorSuccess';
import { AgregarTimbrado } from '../../entities/AgregarTimbrado';
import Loader from '../loader/Loader';
import { Estados } from '../../entities/Enums/estadosRespuesta';
import { Modal, Form, Button, Card } from 'react-bootstrap';
import ModalAltaProveedorExitoso from '../modales/ModalAltaProveedorExitoso';
import ModalAltaProveedorFallido from '../modales/ModalAltaProveedorFallido';
import ModalValidacionAltaProveedor from '../modales/ModalValidacionAltaProveedor';

'use strict'

interface ModalProps {
  mostrarModal: boolean;
  cerrarModal: () => void;
  datoStatusCliente: string;
  tipoDocumento: string;
  cedula: string;
  nombre: string;
  nombreDos: string;
  apellidoUno: string;
  apellidoDos: string;
  tipoPersona: string;
  direccion: string;
  telefono: string;
  ciudad: string;
  pais: string;
  tipoProveedor: string;
  criticidad: string;
  declaracionJurada: string;
  fechaDeclaracionJurada: string;
  codigoCliente: string;
}

interface ModalNexoAltaProveedorProps extends ModalProps {
  handler: Dispatch;
}

export const ModalNexoAltaProveedor: React.FC<ModalNexoAltaProveedorProps> = ({ mostrarModal, cerrarModal, handler, datoStatusCliente, tipoDocumento, cedula, nombre, nombreDos, apellidoUno, apellidoDos, tipoPersona, direccion, telefono, ciudad, pais, tipoProveedor, criticidad, declaracionJurada, fechaDeclaracionJurada, codigoCliente }) => {

  const { state, dispatch } = useProveedor();

  const [isLoading, setIsLoading] = useState(false);

  const [dato, setDato] = useState({ nroDoc: '', tipoDoc: '', codigoCliente: '', responseStatusObtenerDatosClienteProveedor: '', elProveedorNoEsCliente: 'NO ES CLIENTE' });

  // Estado para los datos del formulario - pre-cargado con datos de búsqueda
  const [datoNuevo, setDatoNuevo] = useState(() => ({
    tipoDocumento: tipoDocumento ?? '',
    cedula: cedula ?? '',
    nombre: nombre ?? '',
    nombreDos: nombreDos ?? '',
    apellidoUno: apellidoUno ?? '',
    apellidoDos: apellidoDos ?? '',
    tipoPersona: tipoPersona ?? '',
    direccion: direccion ?? '',
    telefono: telefono ?? '',
    ciudad: ciudad ?? '',
    pais: pais ?? '',
    tipoProveedor: tipoProveedor ?? '',
    criticidad: criticidad ?? '',
    declaracionJurada: declaracionJurada ?? '',
    mensajeGrabadoCorrectamente: ''
  }));

  // Sincroniza datoNuevo con los props iniciales cuando cambian (pre-carga datos de búsqueda)
  useEffect(() => {
    setDatoNuevo({
      tipoDocumento: tipoDocumento || '',
      cedula: cedula || '',
      nombre: nombre || '',
      nombreDos: nombreDos || '',
      apellidoUno: apellidoUno || '',
      apellidoDos: apellidoDos || '',
      tipoPersona: tipoPersona || '',
      direccion: direccion || '',
      telefono: telefono || '',
      ciudad: ciudad || '',
      pais: pais || '',
      tipoProveedor: tipoProveedor || '',
      criticidad: criticidad || '',
      declaracionJurada: declaracionJurada || '',
      mensajeGrabadoCorrectamente: ''
    });
  }, [
    tipoDocumento, cedula, nombre, nombreDos, apellidoUno, apellidoDos,
    tipoPersona, direccion, telefono, ciudad, pais, tipoProveedor,
    criticidad, declaracionJurada
  ]);

  // Definir el tipo para los campos del formulario
  type FormField = 'tipoDocumento' | 'cedula' | 'nombre' | 'nombreDos' |
                  'apellidoUno' | 'apellidoDos' | 'tipoPersona' |
                  'direccion' | 'telefono' | 'ciudad' | 'pais' |
                  'tipoProveedor' | 'criticidad' | 'declaracionJurada' |
                  'fechaDeclaracionJurada' | 'numeroTimbrado' | 'mes' | 'anho';

  // Tipos para los estados
  type TouchedFields = Partial<Record<FormField, boolean>>;
  type FormErrors = Record<FormField, string>;

  // Lista de todos los campos del formulario para inicialización
  const formFields: FormField[] = [
    'tipoDocumento', 'cedula', 'nombre', 'nombreDos',
    'apellidoUno', 'apellidoDos', 'tipoPersona',
    'direccion', 'telefono', 'ciudad', 'pais',
    'tipoProveedor', 'criticidad', 'declaracionJurada',
    'fechaDeclaracionJurada', 'numeroTimbrado', 'mes', 'anho'
  ];

  // Estado para los errores de validación
  const [errors, setErrors] = useState<FormErrors>(() => {
    const initialErrors = {} as FormErrors;
    formFields.forEach(field => {
      initialErrors[field] = '';
    });
    return initialErrors;
  });

  // Estado para el error de fecha
  const [fechaError, setFechaError] = useState('');

  // Seguimiento del envío del formulario y del estado de campos tocados
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [touched, setTouched] = useState<TouchedFields>(() => {
    const initialTouched: TouchedFields = {};
    formFields.forEach(field => {
      // Si el campo tiene valor precargado en datoNuevo, lo marcamos como tocado
      initialTouched[field] = !!datoNuevo[field as keyof typeof datoNuevo] || field === 'fechaDeclaracionJurada' && !!state.fechaDeclaracionJurada;
    });
    return initialTouched;
  });

  const [datosTimbrado, setDatosTimbrado] = useState({
    codigoCliente: '', numeroTimbrado: '',
    mes: '', anho: '', mensajeOk: ''
  });

  // Sincronizar touched y validación cuando cambian los valores iniciales
  useEffect(() => {
    const newTouched: TouchedFields = {};
    formFields.forEach(field => {
      newTouched[field] = !!datoNuevo[field as keyof typeof datoNuevo] || (field === 'fechaDeclaracionJurada' && !!state.fechaDeclaracionJurada);
    });
    setTouched(newTouched);
    // Validar todos los campos con los nuevos valores
    checkFormValidity(true);
  }, [JSON.stringify(datoNuevo), JSON.stringify(datosTimbrado), state.fechaDeclaracionJurada]);

  const [datoFechaDeclaracionJurada, setDatoFechaDeclaracionJurada] = useState({ fechaDeclaracionJurada: '' });

  const [modalAltaExitoso, setModalAltaExitoso] = useState(false);
  const [modalAltaFallido, setModalAltaFallido] = useState(false);

  const [validacionAltaFallida, setValidacionAltaFallida] = useState(false);
  const [mensajeAltaFallida, setMensajeAltaFallida] = useState("");

  const cerrarModalValidacionFallida = () => setValidacionAltaFallida(false);
  const [mensajeError, setMensajeError] = useState<string>("");

  const Params = {
    urlBase: getUrlBase(),
    ambiente: getAmbienteContexto(),
    request: axios.create(),
    endpoints: EndPoint.apiProveedorFactura
  }

  const getProveedor = async ({ handler }: { handler: Dispatch }) => {
    let _cacheControl = 'max-age=60*60*10, private';
    let _headers = await getApiProveedorFacturaHeaders(_cacheControl);
    let _sturlEndpoint = Params.endpoints.vUno.get.proveedor;
    let urlEndpoint = _sturlEndpoint.replace("${codigoCliente}", state.nroD);
    let tipoDoc = `?tipoDocumento=`;

    let config = {
      method: 'get',
      url: Params.urlBase + urlEndpoint + tipoDoc + `${state.tipD}`,
      headers: _headers
    };
    console.log("url getCodigoCliente: ", config.url);

    let Promise = Params.request<Proveedor>(config)
      .then(function (response) {
        if (response.status === Estados.estadoExitoso) {
          console.log("codigoCliente:", response.data.persona.codigoCliente);
          dato.codigoCliente = response.data.persona.codigoCliente;
        }
        console.log("Response data getCodigoCliente:", response.data);

        if (response.status === Estados.estadoValidacionIncorrecta) {
          alert("Por favor, verificar los datos.");
        }

        if (response.status === Estados.estadoNoAutorizado) {
          alert("Favor, refrescar la página y volver a intentar.");
        }

        if (response.status === Estados.estadoErrorServidores) {
          alert("Ocurrió un error.");
        }

        handler('datos');

        return response.data;
      }).catch(function (error) {
        console.log("Error getCodigoCliente:", error);
      });

    let result = await Promise;
    return result;
  }

  const postAltaProveedor = async () => {
    let _headers = await getApiProveedorFacturaHeaders('');
    let _sturlEndpoint = Params.endpoints.vUno.post.proveedor;

    let _body = {
      datos: {
        tipoDocumento: datoNuevo.tipoDocumento,
        cedula: datoNuevo.cedula.trim(),
        nombre: datoNuevo.nombre,
        nombreDos: datoNuevo.nombreDos,
        apellidoUno: datoNuevo.apellidoUno,
        apellidoDos: datoNuevo.apellidoDos,
        tipoPersona: datoNuevo.tipoPersona,
        direccion: datoNuevo.direccion,
        telefono: datoNuevo.telefono,
        ciudad: datoNuevo.ciudad,
        pais: datoNuevo.pais,
        tipoProveedor: datoNuevo.tipoProveedor,
        criticidad: datoNuevo.criticidad,
        declaracionJurada: datoNuevo.declaracionJurada,
        fechaDeclaracionJurada: datoFechaDeclaracionJurada.fechaDeclaracionJurada === ""
          || datoFechaDeclaracionJurada.fechaDeclaracionJurada === null
          || datoFechaDeclaracionJurada.fechaDeclaracionJurada === undefined ?
          "0001-01-01" : datoFechaDeclaracionJurada.fechaDeclaracionJurada
      },
      accion: "A"
    }

    console.log("_body postAltaProveedor:", _body);

    let config = {
      method: "post",
      url: Params.urlBase + _sturlEndpoint,
      headers: _headers,
    }

    const promise = Params.request.post<any, AxiosResponse<AltaProveedorSuccess, ErrorResponse>>(config.url, _body, config)
      .then(function (response) {
        console.log("Response data Post Alta Proveedor:", response.data);
        datoNuevo.mensajeGrabadoCorrectamente = response.data.mensaje;

        if (response.status === Estados.estadoValidacionIncorrecta) {
          alert("Por favor, verificar los datos.");
        }

        if (response.status === Estados.estadoNoAutorizado) {
          alert("Favor, refrescar la página y volver a intentar.");
        }

        if (response.status === Estados.estadoErrorServidores) {
          alert("Ocurrió un error.");
        }

        console.log("response.status POST ALTA PROVEEDOR:", response.status);

        state.responseStatusPostAltaProveedor = response.status.toString();

        return response.data;
      })
      .catch(function (error) {
        console.log("Error Post Alta Proveedor:", error.response);
        if (error.response?.data?.Mensaje) {
          setMensajeError(Array.isArray(error.response.data.Mensaje)
            ? error.response.data.Mensaje[0]
            : error.response.data.Mensaje);
        }
        return error.response;
      });

    if (promise) {
      setIsLoading(true);
      await promise;
    } else {
      setIsLoading(false);
    }

    if (Estados.estadoExitoso || Estados.estadoSinContenido || Estados.estadoErrorServidores || Estados.estadoTiempoDeEspera) {
      setIsLoading(false);
    }

    const result = await promise;
    return result;
  }

  const postTimbrado = async () => {
    let _headers = await getApiProveedorFacturaHeaders('');
    let _sturlEndpoint = Params.endpoints.vUno.post.timbrado;

    // Actualizar el estado con los datos actuales del formulario antes de hacer la llamada a la API
    state.nroD = datoNuevo.cedula;
    state.tipD = datoNuevo.tipoDocumento;

   // Obtener el código del proveedor después del registro exitoso
    await getProveedor({ handler: dispatch });

    let _body = {
      codigoCliente: datoStatusCliente === Estados.estadoSinContenido.toString() ? dato.codigoCliente : codigoCliente,
      numeroTimbrado: datosTimbrado.numeroTimbrado,
      mes: datosTimbrado.mes,
      anho: datosTimbrado.anho
    }

    console.log("_body postAltaTimbrado:", _body);

    let config = {
      method: 'post',
      url: Params.urlBase + _sturlEndpoint,
      headers: _headers
    }

    const promise = Params.request.post<any, AxiosResponse<AgregarTimbrado, ErrorResponse>>(config.url, _body, config)
      .then(function (response) {
        console.log("Response data Post Alta Timbrado:", response.data);
        datosTimbrado.mensajeOk = response.data.mensaje;

        if (response.status === Estados.estadoValidacionIncorrecta) {
          alert("Por favor, verificar los datos.");
        }

        if (response.status === Estados.estadoNoAutorizado) {
          alert("Favor, refrescar la página y volver a intentar.");
        }

        if (response.status === Estados.estadoErrorServidores) {
          alert("Ocurrió un error.");
        }

        return response.data;
      }).catch(function (error) {
        console.log("Error Alta Timbrado Post:", error.response);
        return error.response;
      });

    if (promise) {
      setIsLoading(true);
      await promise;
    } else {
      setIsLoading(false);
    }

    if (Estados.estadoExitoso || Estados.estadoSinContenido || Estados.estadoErrorServidores || Estados.estadoTiempoDeEspera) {
      setIsLoading(false);
    }

    const result = await promise;
    return result;
  }

  // Inicializar campos con valores pre-llenados como "touched"
  useEffect(() => {
    const fieldsWithValues: FormField[] = [
      'tipoDocumento', 'cedula', 'nombre', 'tipoPersona',
      'direccion', 'telefono', 'ciudad', 'pais',
      'tipoProveedor', 'criticidad', 'declaracionJurada'
    ] as const;

    const newTouched = { ...touched };
    let hasUpdates = false;

    fieldsWithValues.forEach(field => {
      const value = datoNuevo[field as keyof typeof datoNuevo];
      if (value && !touched[field]) {
        newTouched[field] = true;
        hasUpdates = true;
      }
    });

    // Marcar fechaDeclaracionJurada como tocada si tiene valor
    if (state.fechaDeclaracionJurada && !touched.fechaDeclaracionJurada) {
      newTouched.fechaDeclaracionJurada = true;
      hasUpdates = true;
    }

    if (hasUpdates) {
      setTouched(newTouched);
    }
  }, [datoNuevo, state.fechaDeclaracionJurada]);

  // Función para validar un campo del formulario
  const validateField = (field: FormField, value: string, checkAllFields: boolean = false): string => {
    if (!field) return '';

    let error = '';

    // Validación común para campos requeridos
    if ((!value?.trim()) && field !== 'apellidoUno' && field !== 'apellidoDos' && field !== 'nombreDos') {
      error = 'Este campo es obligatorio';
    }

    // Validaciones específicas por campo
    const validations: Record<FormField, () => string> = {
      // Campos de datos personales
      tipoDocumento: () => '',
      cedula: () => value && !/^\d+$/.test(value) ? 'La cédula debe contener solo números' : '',
      nombre: () => '',
      nombreDos: () => '',
      apellidoUno: () => '',
      apellidoDos: () => '',
      tipoPersona: () => (!value && checkAllFields) ? 'Debe seleccionar un tipo de persona' : '',
      direccion: () => '',
      telefono: () => '',
      ciudad: () => '',
      pais: () => '',
      tipoProveedor: () => (!value && checkAllFields) ? 'Debe seleccionar un tipo de proveedor' : '',
      criticidad: () => '',
      declaracionJurada: () => '',
      fechaDeclaracionJurada: () => {
        if (!value) return '';
        const selectedDate = new Date(value + 'T00:00:00');
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return selectedDate < today ? 'Debe ingresar una fecha mayor o igual a la actual' : '';
      },
      // Campos de timbrado
      numeroTimbrado: () => {
        if (!value) return 'Este campo es obligatorio';
        if (!/^\d+$/.test(value)) return 'Este campo debe contener solo números';
        if (value.length !== 8) return 'El número de timbrado debe tener exactamente 8 dígitos';
        return '';
      },
      mes: () => {
        if (!value) return '';
        if (!/^\d+$/.test(value)) return 'Debe contener solo números';
        const month = parseInt(value, 10);
        return (month < 1 || month > 12) ? 'Ingrese un mes válido (1-12)' : '';
      },
      anho: () => {
        if (!value) return '';
        if (!/^\d+$/.test(value)) return 'Debe contener solo números';
        const year = parseInt(value, 10);
        const currentYear = new Date().getFullYear();
        return (year < currentYear || year > currentYear + 10) ? `Ingrese un año válido (${currentYear}-${currentYear + 10})` : '';
      }
    };

    const fieldError = validations[field]?.() || '';
    if (fieldError) {
      error = fieldError;
    }

    // Validación condicional para apellidoUno
    if (field === 'apellidoUno' && datoNuevo.tipoPersona === 'F') {
      if (!value?.trim()) {
      error = 'El primer apellido es requerido para personas físicas';
      } else {
      error = '';
    }
    }

    return error;
  };

  // Verificar si el formulario es válido
  const checkFormValidity = (checkAllFields: boolean = false): boolean => {
    let hasErrors = false;
    const newErrors = { ...errors };

    // Validar campos requeridos en datoNuevo
    const formFields: FormField[] = [
      'tipoDocumento', 'cedula', 'nombre', 'apellidoUno',
      'tipoPersona', 'tipoProveedor', 'numeroTimbrado',
      'mes', 'anho'
    ] as const;

    formFields.forEach(field => {
      // Obtener el valor del campo del estado correspondiente
      const value = (field in datoNuevo)
        ? (datoNuevo as any)[field] as string
        : (datosTimbrado as any)[field] as string;

      // Validar solo los campos tocados o si se está verificando todo el formulario
      if (touched[field] || checkAllFields) {
        const error = validateField(field, value, checkAllFields);

        if (error) {
          hasErrors = true;
          newErrors[field] = error;
        } else {
          newErrors[field] = '';
        }
      }
    });

    // Validar fechaDeclaracionJurada solo si tiene valor (no es requerida)
    if (datoFechaDeclaracionJurada.fechaDeclaracionJurada && (touched.fechaDeclaracionJurada || checkAllFields)) {
      const fechaValue = datoFechaDeclaracionJurada.fechaDeclaracionJurada;
      const error = validateField('fechaDeclaracionJurada', fechaValue, checkAllFields);
      if (error) {
        hasErrors = true;
        setFechaError(error);
      } else {
        setFechaError('');
      }
    } else {
      setFechaError('');
    }

    // Validar campos dependientes
    if (datoNuevo.tipoPersona === 'F' && !datoNuevo.apellidoUno?.trim() &&
        (touched.apellidoUno || checkAllFields)) {
      newErrors.apellidoUno = 'El primer apellido es requerido para personas físicas';
      hasErrors = true;
    }

    // Actualizar los errores solo si hay cambios para evitar bucles de renderizado
    if (JSON.stringify(newErrors) !== JSON.stringify(errors)) {
      setErrors(newErrors);
    }

    return !hasErrors;
  };

  useEffect(() => {
    checkFormValidity(isSubmitted);
  }, [datoNuevo, isSubmitted, touched, datoFechaDeclaracionJurada.fechaDeclaracionJurada]);

  const handleFechaDeclaracionJurada = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    // Solo validar si hay un valor (el campo no es obligatorio)
    if (value) {
      const error = validateField('fechaDeclaracionJurada', value);
      if (error) {
        setFechaError(error);
        // No limpiar el valor, solo mostrar el error
        setDatoFechaDeclaracionJurada({
          ...datoFechaDeclaracionJurada,
          [e.target.name]: value
        });
      } else {
        setFechaError('');
        setDatoFechaDeclaracionJurada({
          ...datoFechaDeclaracionJurada,
          [e.target.name]: value
        });
      }
    } else {
      // Si no hay valor, limpiar error
      setFechaError('');
      setDatoFechaDeclaracionJurada({
        ...datoFechaDeclaracionJurada,
        [e.target.name]: value
      });
    }

    // Marcar el campo como tocado
    setTouched(prev => ({
      ...prev,
      fechaDeclaracionJurada: true
    }));

    // Validar el formulario completo si ya se ha intentado enviar
    if (isSubmitted) {
      checkFormValidity(true);
    }
  }

  // Manejador unificado para cambios en los campos del formulario
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // Determinar si el campo pertenece a datosTimbrado o datoNuevo
    const isTimbradoField = ['numeroTimbrado', 'mes', 'anho'].includes(name);

    if (isTimbradoField) {
      const fieldName = name as keyof typeof datosTimbrado;

      // Actualizar el estado de timbrado
      setDatosTimbrado(prev => ({
        ...prev,
        [fieldName]: value
      }));

      // Validar el campo de timbrado
      const error = validateField(fieldName as FormField, value);
      setErrors(prev => ({
        ...prev,
        [fieldName]: error
      }));
    } else {
      const fieldName = name as keyof typeof datoNuevo;

      // Actualizar el estado principal
      setDatoNuevo(prev => ({
        ...prev,
        [fieldName]: value
      }));

      // Validar el campo principal
      const error = validateField(fieldName as FormField, value);
      setErrors(prev => ({
        ...prev,
        [fieldName]: error
      }));
    }

    // Marcar el campo como tocado
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));

    // Validar el formulario completo si ya se ha intentado enviar
    if (isSubmitted) {
      checkFormValidity(true);
    }
  };
  
  // Alias para mantener compatibilidad con el código existente
  const changeDatosTimbrado: React.ChangeEventHandler<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement> = (e) => {
    handleInputChange(e);
  }

  const soloNumeros = (e: React.KeyboardEvent<HTMLInputElement>) => {
    const key = e.key;
    if (!/[0-9]/.test(key) && key !== "ArrowLeft" && key !== "ArrowRight" && key !== "Delete" && key !== "Backspace" && key !== "Tab") {
      e.preventDefault();
    }
  }

  const abrirModalExitoso = () => setModalAltaExitoso(true);
  const cerrarModalExitoso = () => { setModalAltaExitoso(false); window.location.reload(); }

  const abrirModalFallido = () => setModalAltaFallido(true);
  const cerrarModalFallido = () => { setModalAltaFallido(false); window.location.reload(); }

  const agregarNuevoProveedorYTimbrado = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    // Marcar todos los campos como tocados para mostrar todos los errores
    const allTouched: TouchedFields = {};
    formFields.forEach(field => {
      allTouched[field] = true;
    });
    setTouched(allTouched);

    // Marcar el formulario como enviado
    setIsSubmitted(true);

    // Validar todos los campos
    const isFormValid = checkFormValidity(true);

    if (!isFormValid) {
      // Encontrar el primer campo con error
      const firstErrorField = document.querySelector('.is-invalid');
      if (firstErrorField) {
        // Desplazarse al campo con error
        firstErrorField.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });

        // Enfocar el campo
        if (firstErrorField instanceof HTMLElement) {
          firstErrorField.focus({ preventScroll: true });
        }
      }

      // Mostrar mensaje de error
      setMensajeAltaFallida("Por favor complete todos los campos requeridos");
      setValidacionAltaFallida(true);
      return;
    }

    try {
      await postAltaProveedor();
      console.log("state.responseStatusPostAltaProveedor:", state.responseStatusPostAltaProveedor);

      if (state.responseStatusPostAltaProveedor === Estados.estadoDeSolicitudProcesada.toString()) {
        await postTimbrado();
        console.log("Alta Timbrado Exitoso:", datosTimbrado.mensajeOk);

        if (datosTimbrado.mensajeOk) {
          console.log("datoNuevo.mensajeGrabadoCorrectamente:", datoNuevo.mensajeGrabadoCorrectamente);
          abrirModalExitoso();
          handler('datos');
        } else {
          abrirModalFallido();
          console.log("Error mensaje datos timbrado");
        }
      } else {
        abrirModalFallido();
        console.log("Error estado post alta proveedor");
      }
      handler('datos');
    } catch (error) {
      console.error("Error al procesar la solicitud:", error);
      setMensajeAltaFallida("Ocurrió un error al procesar la solicitud");
      setValidacionAltaFallida(true);
    }
  }

  return (
    <>
      <Modal show={mostrarModal}>
        <Card>
          <Modal.Header className='d-flex justify-content-between align-items-center'>
            <Modal.Title className='text-primary'>Alta Proveedor</Modal.Title>
            <Button variant='danger' onClick={cerrarModal}>x</Button>
          </Modal.Header>
          <div className='text-center'>{datoStatusCliente === Estados.estadoSinContenido.toString() ?
            <h6 className="text-danger">NO ES CLIENTE</h6> :
            <h6 className="text-success">ES CLIENTE</h6>}</div>
          <Card.Body>
            <Form>
              <div className='d-flex justify-content-center align-items-center'>
                <Form.Group className='d-flex flex-column align-items-center mx-4'>
                  <Form.Label>Tipo Documento <span className="text-danger">*</span></Form.Label>
                  <Form.Select 
                    className={`custom-select ${errors.tipoDocumento ? 'is-invalid' : ''}`} 
                    aria-label="Default select example" 
                    name="tipoDocumento" 
                    value={datoNuevo.tipoDocumento ?? tipoDocumento ?? ""} 
                    onChange={handleInputChange}
                  >
                    <option value="">TIPO DOCUMENTO</option>
                    <option value="1">CEDULA</option>
                    <option value="4">RUC</option>
                  </Form.Select>
                  {errors.tipoDocumento && <div className="invalid-feedback d-block">{errors.tipoDocumento}</div>}
                </Form.Group>
                <Form.Group className='d-flex flex-column align-items-center mx-4'>
                  <Form.Label>Cédula <span className="text-danger">*</span></Form.Label>
                  <Form.Control 
                    type="text" 
                    placeholder="Cédula" 
                    name="cedula" 
                    maxLength={15}
                    value={datoNuevo.cedula ?? ""}
                    onChange={handleInputChange}
                    className={errors.cedula ? 'is-invalid' : ''}
                  />
                  {errors.cedula && <div className="invalid-feedback d-block">{errors.cedula}</div>}
                </Form.Group>
              </div>
              <div className='d-flex justify-content-center align-items-center my-4'>
                <Form.Group className='d-flex flex-column align-items-center mx-4'>
                  <Form.Label>Nombre <span className="text-danger">*</span></Form.Label>
                  <Form.Control 
                    type="text" 
                    placeholder="Nombre" 
                    name="nombre" 
                    maxLength={100}
                    value={datoNuevo.nombre || nombre || ""} 
                    onChange={handleInputChange}
                    className={errors.nombre ? 'is-invalid' : ''}
                  />
                  {errors.nombre && <div className="invalid-feedback d-block">{errors.nombre}</div>}
                </Form.Group>
                <Form.Group className='d-flex flex-column align-items-center mx-4'>
                  <Form.Label>Segundo Nombre</Form.Label>
                  <Form.Control type="text" placeholder="Segundo Nombre" name="nombreDos" maxLength={100} value={datoNuevo.nombreDos || nombreDos || ""} onChange={handleInputChange} />
                </Form.Group>
              </div>
              <div className='d-flex justify-content-center align-items-center my-4'>
                <Form.Group className='d-flex flex-column align-items-center mx-4'>
                  <Form.Label>Apellido {datoNuevo.tipoPersona === 'F' && <span className="text-danger">*</span>}</Form.Label>
                  <Form.Control 
                    type="text" 
                    placeholder="Apellido" 
                    name="apellidoUno" 
                    maxLength={100}
                    value={datoNuevo.apellidoUno || apellidoUno} 
                    onChange={handleInputChange}
                    className={errors.apellidoUno ? 'is-invalid' : ''}
                  />
                  {errors.apellidoUno && <div className="invalid-feedback d-block">{errors.apellidoUno}</div>}
                </Form.Group>
                <Form.Group className='d-flex flex-column align-items-center mx-4'>
                  <Form.Label>Segundo Apellido</Form.Label>
                  <Form.Control type="text" placeholder="Segundo Apellido" name="apellidoDos" maxLength={100} value={datoNuevo.apellidoDos || apellidoDos || ""} onChange={handleInputChange} />
                </Form.Group>
              </div>
              <div className='d-flex justify-content-center align-items-center my-4'>
                <Form.Group className='d-flex flex-column align-items-center mx-4'>
                  <Form.Label>Tipo Persona <span className="text-danger">*</span></Form.Label>
                  <Form.Select 
                    className={`custom-select ${errors.tipoPersona ? 'is-invalid' : ''}`} 
                    aria-label="Default select example" 
                    name="tipoPersona" 
                    value={datoNuevo.tipoPersona ?? tipoPersona ?? ""} 
                    onChange={handleInputChange}
                  >
                    <option value="">TIPO PERSONA</option>
                    <option value="J">JURIDICA</option>
                    <option value="F">FISICA</option>
                  </Form.Select>
                  {errors.tipoPersona && <div className="invalid-feedback d-block">{errors.tipoPersona}</div>}
                </Form.Group>
                <Form.Group className='d-flex flex-column align-items-center mx-4'>
                  <Form.Label>Dirección</Form.Label>
                  <Form.Control type="text" placeholder="Dirección" name="direccion" maxLength={100} value={datoNuevo.direccion || direccion || ""} onChange={handleInputChange} />
                </Form.Group>
              </div>
              <div className='d-flex justify-content-center align-items-center my-4'>
                <Form.Group className='d-flex flex-column align-items-center mx-4'>
                  <Form.Label>Teléfono</Form.Label>
                  <Form.Control type="text" placeholder="Teléfono" name="telefono" maxLength={50} value={datoNuevo.telefono || telefono || ""} onChange={handleInputChange} />
                </Form.Group>
                <Form.Group className='d-flex flex-column align-items-center mx-4'>
                  <Form.Label>Ciudad</Form.Label>
                  <Form.Control type="text" placeholder="Ciudad" name="ciudad" maxLength={50} value={datoNuevo.ciudad || ciudad || ""} onChange={handleInputChange} />
                </Form.Group>
              </div>
              <div className='d-flex justify-content-center align-items-center my-4'>
                <Form.Group className='d-flex flex-column align-items-center mx-4'>
                  <Form.Label>País</Form.Label>
                  <Form.Control type="text" placeholder="País" name="pais" maxLength={25} value={datoNuevo.pais || pais || ""} onChange={handleInputChange} />
                </Form.Group>
                <Form.Group className='d-flex flex-column align-items-center mx-4'>
                  <Form.Label>Tipo Proveedor <span className="text-danger">*</span></Form.Label>
                  <Form.Select 
                    className={`custom-select ${errors.tipoProveedor ? 'is-invalid' : ''}`} 
                    aria-label="Tipo de proveedor" 
                    name="tipoProveedor" 
                    value={datoNuevo.tipoProveedor || ""} 
                    onChange={handleInputChange}
                  >
                    <option value="">TIPO PROVEEDOR</option>
                    <option value="PON">Proveedor ocasional - No Cliente</option>
                    <option value="PHCC">Proveedor habitual con contrato - Cliente</option>
                    <option value="PHSC">Proveedor habitual sin contrato - Cliente</option>
                    <option value="POC">Proveedor ocasional - Cliente</option>
                    <option value="PHCN">Proveedor habitual con contrato - No Cliente</option>
                    <option value="PHSN">Proveedor habitual sin contrato - No Cliente</option>
                  </Form.Select>
                  {errors.tipoProveedor && (
                    <div className="invalid-feedback d-block">{errors.tipoProveedor}</div>
                  )}
                </Form.Group>
              </div>
              <div className='d-flex justify-content-center align-items-center my-4'>
                <Form.Group className='d-flex flex-column align-items-center mx-4'>
                  <Form.Label>Criticidad</Form.Label>
                  <Form.Select className="custom-select" aria-label="Default select example" name="criticidad" defaultValue={state.criticidad || criticidad} onChange={handleInputChange}>
                    <option>CRITICIDAD</option>
                    <option value="B">BAJA</option>
                    <option value="M">MEDIA</option>
                    <option value="A">ALTA</option>
                    <option value="MA">MUY ALTA</option>
                  </Form.Select>
                </Form.Group>
                <Form.Group className='d-flex flex-column align-items-center mx-4'>
                  <Form.Label>Declaración Jurada</Form.Label>
                  <Form.Select className="custom-select" name="declaracionJurada" defaultValue={state.declaracionJurada || declaracionJurada} onChange={handleInputChange}>
                    <option value="">DECLARACION JURADA</option>
                    <option value="S">SI</option>
                    <option value="N">NO</option>
                  </Form.Select>
                </Form.Group>
              </div>
              <div className='d-flex justify-content-center align-items-center my-4'>
                <Form.Group className='d-flex flex-column align-items-center mx-4'>
                  <Form.Label>Fecha Declaración Jurada</Form.Label>
                  <Form.Control
                    type="date"
                    name="fechaDeclaracionJurada"
                    value={datoFechaDeclaracionJurada.fechaDeclaracionJurada || state.fechaDeclaracionJurada || fechaDeclaracionJurada || ''}
                    onChange={handleFechaDeclaracionJurada}
                    onClick={(e) => (e.target as HTMLInputElement).showPicker?.()}
                    className={fechaError ? 'is-invalid' : ''}
                  />
                  {fechaError && <div className="invalid-feedback d-block">{fechaError}</div>}
                </Form.Group>
              </div>
              <Modal.Header>
                <Modal.Title className='text-primary'>Alta Timbrado</Modal.Title>
              </Modal.Header>
              <Form.Group className='d-flex flex-column align-items-center mx-4 my-4'>
                <Form.Label className="d-flex justify-content-center w-100">Número Timbrado <span className="text-danger">*</span></Form.Label>
                <Form.Control
                  type="text"
                  minLength={8}
                  maxLength={8}
                  className={`w-50 ${errors.numeroTimbrado ? 'is-invalid' : ''}`}
                  placeholder="Número Timbrado"
                  name="numeroTimbrado"
                  value={datosTimbrado.numeroTimbrado}
                  onChange={changeDatosTimbrado}
                  onKeyDown={soloNumeros}
                />
                {errors.numeroTimbrado && (
                  <div className="w-100 d-flex justify-content-center">
                    <div className="invalid-feedback d-block text-center w-50">
                      {errors.numeroTimbrado}
                    </div>
                  </div>
                )}
                {!errors.numeroTimbrado && datosTimbrado.numeroTimbrado.length > 8 && (
                  <div className="w-100 d-flex justify-content-center">
                    <div className="text-danger text-center w-50">
                      El número de timbrado debe tener exactamente 8 dígitos.
                    </div>
                  </div>
                )}
              </Form.Group>
              <div className='d-flex justify-content-center align-items-center my-4 mb-5'>
                <Form.Group className='d-flex flex-column align-items-center mx-4'>
                  <Form.Label>Mes <span className="text-danger">*</span></Form.Label>
                  <Form.Select 
                    name="mes" 
                    className={`custom-select ${errors.mes ? 'is-invalid' : ''}`} 
                    value={datosTimbrado.mes ?? ""} 
                    onChange={changeDatosTimbrado}
                  >
                    <option value="">MES</option>
                    <option value="01">01</option>
                    <option value="02">02</option>
                    <option value="03">03</option>
                    <option value="04">04</option>
                    <option value="05">05</option>
                    <option value="06">06</option>
                    <option value="07">07</option>
                    <option value="08">08</option>
                    <option value="09">09</option>
                    <option value="10">10</option>
                    <option value="11">11</option>
                    <option value="12">12</option>
                  </Form.Select>
                </Form.Group>
                <Form.Group className='d-flex flex-column align-items-center mx-4'>
                  <Form.Label>Año <span className="text-danger">*</span></Form.Label>
                  <Form.Select 
                    className={`custom-select ${errors.anho ? 'is-invalid' : ''}`} 
                    name="anho" 
                    value={datosTimbrado.anho ?? ""} 
                    onChange={changeDatosTimbrado}
                  >
                    <option value="">AÑO</option>
                    <option value="2020">2020</option>
                    <option value="2021">2021</option>
                    <option value="2022">2022</option>
                    <option value="2023">2023</option>
                    <option value="2024">2024</option>
                    <option value="2025">2025</option>
                    <option value="2026">2026</option>
                    <option value="2027">2027</option>
                    <option value="2028">2028</option>
                    <option value="2029">2029</option>
                    <option value="2030">2030</option>
                    <option value="2031">2031</option>
                    <option value="2032">2032</option>
                    <option value="2033">2033</option>
                    <option value="2034">2034</option>
                    <option value="2035">2035</option>
                    <option value="2036">2036</option>
                    <option value="2037">2037</option>
                    <option value="2038">2038</option>
                    <option value="2039">2039</option>
                    <option value="2040">2040</option>
                    <option value="2041">2041</option>
                    <option value="2042">2042</option>
                    <option value="2043">2043</option>
                    <option value="2044">2044</option>
                    <option value="2045">2045</option>
                    <option value="2046">2046</option>
                    <option value="2047">2047</option>
                    <option value="2048">2048</option>
                    <option value="2049">2049</option>
                    <option value="2050">2050</option>
                  </Form.Select>
                </Form.Group>
              </div>
              <div className='my-4'>
                {isLoading ?
                  <div className='text-center'><Loader /></div>
                  : ""}
              </div>
              <div className='d-flex justify-content-center'>
                <Button variant='secondary' className="mx-5" onClick={cerrarModal}>Cerrar</Button>
                <Button 
                  variant='success' 
                  className="mx-5 position-relative" 
                  onClick={agregarNuevoProveedorYTimbrado}
                >
                  Confirmar
                </Button>
              </div>
            </Form>
          </Card.Body>
        </Card>
      </Modal>
      {<ModalAltaProveedorExitoso abrir={modalAltaExitoso} cerrar={cerrarModalExitoso} />}
      {<ModalAltaProveedorFallido abrir={modalAltaFallido} cerrar={cerrarModalFallido} mensaje={mensajeError} />}
      {<ModalValidacionAltaProveedor show={validacionAltaFallida} onClose={cerrarModalValidacionFallida} message={mensajeAltaFallida} />}
    </>
  )
}

export default ModalNexoAltaProveedor;



