/*
 
  █████╗ ██╗   ██╗ █████╗ ████████╗ █████╗ ██████╗ 
 ██╔══██╗██║   ██║██╔══██╗╚══██╔══╝██╔══██╗██╔══██╗
 ███████║██║   ██║███████║   ██║   ███████║██████╔╝
 ██╔══██║╚██╗ ██╔╝██╔══██║   ██║   ██╔══██║██╔══██╗
 ██║  ██║ ╚████╔╝ ██║  ██║   ██║   ██║  ██║██║  ██║
 ╚═╝  ╚═╝  ╚═══╝  ╚═╝  ╚═╝   ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═╝                                               
 
*/

@include block('avatar') {
    position: relative;
    width: 30px;
    height: 30px;
    margin:0;
    border-radius: 50%;
    border:1px solid $gray-200;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;

    img { 
        width: 100%;
        height: 100%;
        object-fit: contain;
        vertical-align: baseline;
    }

    @include element('overlay'){
        background-color: transparentize($black, 0.5);
        color: $white;
        width: 100%;
        height: 100%;
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        opacity: 0;
        transition: all .3s ease;
        cursor: pointer;
    
        &:hover{
            opacity: 1;
        }
    }
    
    // Tamaño
    @include modifier('xl') {
        width: 120px;
        height: 120px;
        
        span{
            font-size: 2.8rem;
        }
    }
    @include modifier('lg') {
        width: 70px;
        height: 70px;
        
        span{
            font-size: 2rem;
        }
    }
    @include modifier('md') {
        width: 50px;
        height: 50px;
        
        span{
            font-size: 1.2rem;
        }
    }
    @include modifier('sm') {
        width: 30px;
        height: 30px;
        
        span{
            font-size: 1rem;
        }
    }
    @include modifier('xs') {
        width: 22px;
        height: 22px;

        span{
            font-size: 0.8rem;
        }
    }

    // Alternativos
    @include modifier('text') {
        span{
            text-transform: uppercase;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
        }
    }
    
    // Forma
    @include modifier('square') {
        border-radius: 8px;
    }       
}
