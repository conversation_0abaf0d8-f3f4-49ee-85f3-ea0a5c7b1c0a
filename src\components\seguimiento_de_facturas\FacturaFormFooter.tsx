/// <reference types="webpack/module" />

import React, { useContext, useState } from 'react'
import { spCrearItem } from '../../comm/apiSharePoint'
import { inicioDeSegumientoFacturas } from '../../services/operacionesService'
import FacturaFormContext from '../contextProvider/counterContext'
import { datosFacturasObtenerDOM } from '../../services/facturaServices'
import { getUserName } from '../../utilities/_spPageContextInfo'
import { InicioSeguimientoRequest } from '../../comm/contracts/InicioSeguimientoRequest'
import { SharepointListData } from '../../entities/SharepointListData'
import Loader from '../loader/Loader'
import { getAmbienteContexto, getUrlBase, getUrlSPBase } from '../../utilities/contextInfo'
import axios, { AxiosResponse } from 'axios'
import EndPoint from '../../comm/EndPoint.Config'
import { getApiBpmPoliticasProcesosRequestHeaders } from '../../comm/contracts/ApiBpmPoliticasProcesosRequestHeaders'
import { InsertarAccionGenerada } from '../../entities/InsertarAccionGenerada';
import { ErrorResponse } from '../../comm/contracts/ErrorResponse'
import { insertarAutorizantes } from '../insertarAutorizantes'
import { hadoop } from '../../comm/WSdocumentos'
import { useProveedor } from '../contextProvider/datosProveedorContext'
import ModalValidacionInicioDeSeguimiento from '../modales/ModalValidacionInicioDeSeguimiento'
import ModalValidacionExitosaInicioDeSeguimiento from '../modales/ModalValidacionExitosaInicioDeSeguimiento'

'use strict'

export const FacturaFormFooter = (props: any) => {

  const { state } = useProveedor();

  const _FacturaFormContext = useContext(FacturaFormContext)
  const _id = 'btnIniciarSeguimiento' + _FacturaFormContext.counter

  const [isLoading, setIsLoading] = useState(false);

  const [modalValidacionFallida, setModalValidacionFallida] = useState(false);
  const cerrarModalValidacionFallida = () => { setModalValidacionFallida(false); };
  const [validacionInicioDeSeguimiento, setValidacionInicioDeSeguimiento] = useState("");
  const [modalValidacionExitosa, setModalValidacionExitosa] = useState(false);
  const cerrarModalValidacionExitosa = () => { setModalValidacionExitosa(false); window.location.reload(); };
  const abrirModalValidacionFallidaCompletarNumeroDeDocumento = () => { setModalValidacionFallida(true); setValidacionInicioDeSeguimiento("Debe completar los campos: Número de documento, Tipo de documento y presione Buscar Proveedor."); };
  const abrirModalValidacionFallidaCompletarFechaDeEmision = () => { setModalValidacionFallida(true); setValidacionInicioDeSeguimiento("Debe agregar una Fecha de emisión."); };
  const abrirModalValidacionFallidaActualizarTimbrado = () => { setModalValidacionFallida(true); setValidacionInicioDeSeguimiento("El Timbrado se encuentra vencido. Actualice por favor."); };
  const abrirModalValidacionFallidaCompletarDatosDeLaFactura = () => { setModalValidacionFallida(true); setValidacionInicioDeSeguimiento("Debe completar los campos Número de Factura."); };
  const abrirModalValidacionFallidaSeleccionarTipoDeFacura = () => { setModalValidacionFallida(true); setValidacionInicioDeSeguimiento("Debe seleccionar un Tipo de Factura."); };
  const abrirModalValidacionFallidaSeleccionarAreaYDepartamento = () => { setModalValidacionFallida(true); setValidacionInicioDeSeguimiento("Debe seleccionar un Area y Departamento."); };
  const abrirModalValidacionFallidaAgregarAutorizanteDeArea = () => { setModalValidacionFallida(true); setValidacionInicioDeSeguimiento("Debe agregar al menos un Autorizante de area."); };
  const abrirModalValidacionFallidaCompletarCampoMontoCinco = () => { setModalValidacionFallida(true); setValidacionInicioDeSeguimiento("Debe completar el campo Monto 5%. Ó agregar un cero."); };
  const abrirModalValidacionFallidaCompletarCampoMontoDiez = () => { setModalValidacionFallida(true); setValidacionInicioDeSeguimiento("Debe completar el campo Monto 10%. Ó agregar un cero."); };
  const abrirModalValidacionFallidaCompletarCampoDescripcion = () => { setModalValidacionFallida(true); setValidacionInicioDeSeguimiento("Debe completar el campo Descripción."); };
  const abrirModalValidacionFallidaCompletarCampoExentas = () => { setModalValidacionFallida(true); setValidacionInicioDeSeguimiento("Debe completar el campo Exentas. Ó agregar un cero."); };
  const abrirModalValidacionFallidaAdjuntarArchivo = () => { setModalValidacionFallida(true); setValidacionInicioDeSeguimiento("Debe adjuntar un archivo. Por favor, verifique para poder Iniciar Seguimiento."); };

  const iniciarSeguimientoFactura = async () => {

    let nroSeguimiento: any;

    const Params = {
      urlBase: getUrlBase(),
      ambiente: getAmbienteContexto(),
      request: axios.create(),
      endpoints: EndPoint.apiBpmPoliticaProcesos
    }

    const insertarAccionGeneradaTracking = async () => {
      const _headers = await getApiBpmPoliticasProcesosRequestHeaders('');
      const _sturlEndpoint = Params.endpoints.vUno.post.insertarAccionGenerada;
      const config = {
        method: 'post',
        url: Params.urlBase + _sturlEndpoint,
        headers: _headers
      };

      let body = {
        IdCabecera: parseInt(nroSeguimiento),
        CodAccion: "SF0015",
        CodEstado: "ESTAPR",
        UsuarioDominio: getUserName(),
        Atributos: [
          {
            codigo: "ATRRES",
            valor: "Aprobar"
          },
          {
            codigo: "ATCOME",
            valor: "Inicio de Seguimiento Alta de Factura"
          }
        ]
      }

      const promise = Params.request.post<any, AxiosResponse<InsertarAccionGenerada, ErrorResponse>>(config.url, body, config)
        .then(function (response) {
          localStorage.setItem("idAccionGenerada", JSON.stringify(response.data));
          return response.data
        })
        .catch(function (error) {
          console.log("error catch insertarAccionGenerada Tracking:", error);
          return error.response
        });

      const result = await promise

      return result
    }

    let archivosAdjuntos = localStorage.getItem("archivosAdjuntos");
    let nroDocumento = document.getElementById("buscadorProveedor") as HTMLInputElement | null;
    let nroDocumentoValue = nroDocumento?.value;
    let tipoDocumento = document.getElementById("tipoDocumento") as HTMLInputElement | null;
    let tipoDocumentoValue = tipoDocumento?.value;
    let datoFechaEmision = localStorage.getItem("datoFechaEmision");
    let fechaTimbradoSelected = localStorage.getItem("timbradoSelected");
    let timbradoDate: any = state.timbrado.map((item: any) => item.fechaVencimiento);
    let primerTimbradoState = localStorage.getItem("primerTimbradoState");
    let numUno = localStorage.getItem("numUno");
    let numDos = localStorage.getItem("numDos");
    let numTres = localStorage.getItem("numTres");
    let pct5 = localStorage.getItem("pct5").toString();
    let pct10 = localStorage.getItem("pct10").toString();
    let descripcion = localStorage.getItem("descripcion");
    let exentas = localStorage.getItem("exentas").toString();
    let tipoComprobante = document.getElementById("tipoComprobante") as HTMLSelectElement | null;
    let tipoComprobanteValue = tipoComprobante?.value;
    let departamento = document.getElementById("departamento") as HTMLSelectElement | null;
    let departamentoValue = departamento?.value;
    let area = document.getElementById("area") as HTMLSelectElement | null;
    let areaValue = area?.value;
    let inputAutorizantes = document.getElementById("seleccionarArea") as HTMLInputElement | null;

    if (!nroDocumentoValue || !tipoDocumentoValue) {
      abrirModalValidacionFallidaCompletarNumeroDeDocumento();
      setIsLoading(false);
    } else if (!datoFechaEmision) {
      abrirModalValidacionFallidaCompletarFechaDeEmision();
      setIsLoading(false);
    } else if (fechaTimbradoSelected.slice(0, 10) < state.fechaActual && timbradoDate.toString().slice(0, 10) < state.fechaActual) {
      abrirModalValidacionFallidaActualizarTimbrado();
      setIsLoading(false);
    } else if (!numUno || !numDos || !numTres) {
      abrirModalValidacionFallidaCompletarDatosDeLaFactura();
      setIsLoading(false);
    } else if (!tipoComprobanteValue) {
      abrirModalValidacionFallidaSeleccionarTipoDeFacura();
      setIsLoading(false);
    } else if (!departamentoValue) {
      abrirModalValidacionFallidaSeleccionarAreaYDepartamento();
      setIsLoading(false);
    } else if (!areaValue) {
      abrirModalValidacionFallidaSeleccionarAreaYDepartamento();
      setIsLoading(false);
    } else if (inputAutorizantes && JSON.parse(localStorage.getItem("arrayID")).length === 0) {
      abrirModalValidacionFallidaAgregarAutorizanteDeArea();
      setIsLoading(false);
    } else if (!pct5) {
      abrirModalValidacionFallidaCompletarCampoMontoCinco();
      setIsLoading(false);
    } else if (!pct10) {
      abrirModalValidacionFallidaCompletarCampoMontoDiez();
    } else if (!descripcion) {
      abrirModalValidacionFallidaCompletarCampoDescripcion();
      setIsLoading(false);
    } else if (!exentas) {
      abrirModalValidacionFallidaCompletarCampoExentas();
      setIsLoading(false);
    } else if (archivosAdjuntos === "1") {
      console.log("INICIAR SEGUIMIENTO FACTURA");
      setIsLoading(true);

      try {
        const onSuccess = async (sender: any, args: any) => {
          let arrayID = localStorage.getItem("arrayID");
          let autorizantes = JSON.parse(arrayID);
          if (localStorage.getItem("areaSeleccionada") !== "TECNOLOGIA") {
            await insertarAutorizantes(nroSeguimiento, autorizantes);
          }
          console.log('Insertado Exitosamente');
          const abrirModalValidacionInicioDeSeguimientoExitoso = () => { setModalValidacionExitosa(true); setValidacionInicioDeSeguimiento(`Insertado Exitosamente. Número de operación: ${nroSeguimiento}`); };
          abrirModalValidacionInicioDeSeguimientoExitoso();
          localStorage.setItem("arrayID", "0");
          setIsLoading(false);
          autorizantes = []
          localStorage.setItem("arrayID", JSON.stringify(autorizantes));
          let idArray: any = [];
          localStorage.setItem("arrayID", JSON.stringify(idArray));
        }

        const onFail = (sender: any, args: any) => {
          console.log("mensaje onfail: ", args.get_message());
        }

        let _facturaInsert = await datosFacturasObtenerDOM()
        let inicioSeguimientoBody: InicioSeguimientoRequest =
        {
          usuarioDominio: getUserName(),
          codigoProceso: 'SEGFAC',
          atributos: [{
            codigo: '',
            valor: '0'
          },
          ]
        }

        inicioSeguimientoBody.atributos.push({ codigo: '', valor: '0' })

        await inicioDeSegumientoFacturas(inicioSeguimientoBody, _facturaInsert)
          .then(async (response) => {
            nroSeguimiento = response.id;
            localStorage.setItem('operacionSeguimiento', nroSeguimiento);
            await insertarAccionGeneradaTracking();
            await hadoop((document.getElementById('subirAdjuntos') as HTMLInputElement).files);
            let data: SharepointListData[] = [{
              columna: 'Title',
              valor: response.id
            }]
            console.log("data inicioDeSegumientoFacturas:", data);
            await spCrearItem(`${getUrlSPBase()}` + 'Sistemas/SeguimientoFacturas', 'BpmCabecera', data, onSuccess, onFail);
            if (response) {
              setIsLoading(true);
            } else {
              setIsLoading(false);
            }
            return nroSeguimiento;
          })
      } catch (error) {
        console.log("Error inicioDeSegumientoFacturas:", error);
      }
    } else {
      abrirModalValidacionFallidaAdjuntarArchivo();
      setIsLoading(false);
    }
  }

  return (
    <div className=''>
      <div className='text-center mb-4'>
        {isLoading ? <Loader /> : ""}
      </div>
      <div className=''>
        <div className="card w-50 m-auto">
          <button onClick={iniciarSeguimientoFactura} type="button" id={_id} className="btn btn-primary btn-lg btn-block btnIniciarSeguimiento">Iniciar Seguimiento</button>
        </div>
      </div>
      {props.btnAgregar}
      {<ModalValidacionInicioDeSeguimiento show={modalValidacionFallida} onClose={cerrarModalValidacionFallida} message={validacionInicioDeSeguimiento} />}
      {<ModalValidacionExitosaInicioDeSeguimiento show={modalValidacionExitosa} onClose={cerrarModalValidacionExitosa} message={validacionInicioDeSeguimiento} />}
    </div>
  )
}