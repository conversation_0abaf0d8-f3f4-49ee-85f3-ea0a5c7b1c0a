/*
 
 ██╗      █████╗ ██╗   ██╗ ██████╗ ██╗   ██╗████████╗   ███╗   ███╗███████╗███╗   ██╗██╗   ██╗
 ██║     ██╔══██╗╚██╗ ██╔╝██╔═══██╗██║   ██║╚══██╔══╝   ████╗ ████║██╔════╝████╗  ██║██║   ██║
 ██║     ███████║ ╚████╔╝ ██║   ██║██║   ██║   ██║█████╗██╔████╔██║█████╗  ██╔██╗ ██║██║   ██║
 ██║     ██╔══██║  ╚██╔╝  ██║   ██║██║   ██║   ██║╚════╝██║╚██╔╝██║██╔══╝  ██║╚██╗██║██║   ██║
 ███████╗██║  ██║   ██║   ╚██████╔╝╚██████╔╝   ██║      ██║ ╚═╝ ██║███████╗██║ ╚████║╚██████╔╝
 ╚══════╝╚═╝  ╚═╝   ╚═╝    ╚═════╝  ╚═════╝    ╚═╝      ╚═╝     ╚═╝╚══════╝╚═╝  ╚═══╝ ╚═════╝ 
 - Estilos para el menu y submenu                                                                                             
 
*/

// Menu Aside
/////////////////////////////////////////////////

@include block('menu-aside') {
    
    position: fixed;
    padding: $spacer;
    
    ul {
       list-style: none;
       margin: ($spacer * 3) 0 0;
       padding: 0;
    }

    li { margin-bottom: $spacer; }

    a {
        display: flex;
        color:$white;
        padding: ($spacer / 2) 0;
    }

    .icon { 
        margin-right: $spacer;
        z-index: 10; 
    }

    @include element('active') {

        position: relative;
        .icon { color:$blue; }

            &:after {
                content: '';
                position: absolute;
                left: -25px;
                bottom: 0;
                width: 60px;
                height: 100%;
                background-color: $white;
                z-index: 1;
                border-top-right-radius: 10px;
                border-bottom-right-radius: 10px;
            }
    }



}//endMenuAside
