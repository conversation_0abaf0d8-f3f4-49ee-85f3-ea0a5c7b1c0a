import { xmlToJson } from "../utilities/Parsers";
import { getUrlSeguimientoFactura } from "../utilities/contextInfo";

export const spCrearItem = async (url, nombreLista, itemData, onSuccess, onFail) => {
  console.log("spCrearItem url:", url);
  console.log("nombre lista: ", nombreLista);
  console.table("itemData: ", itemData);
  let clientContext = new SP.ClientContext(url);
  console.log("clientContext: ", clientContext);
  let oList = clientContext.get_web().get_lists().getByTitle(nombreLista);
  let itemCreateInfo = new SP.ListItemCreationInformation();
  let oListItem = oList.addItem(itemCreateInfo);

  itemData.forEach((element) => {
    console.log("element:", element);
    console.log("element['columna']:", element["columna"]);
    oListItem.set_item(element.columna, element.valor);
  });

  oListItem.update();
  clientContext.load(oListItem);
  clientContext.executeQueryAsync(Function.createDelegate(this, onSuccess), Function.createDelegate(this, onFail));
  console.log("FIN DE SP.CREARITEM------------------------------------");
}

SP.SOD.executeFunc('sp.js', 'SP.ClientContext', spCrearItem);

export const spActualizarItem = async (url, nombreLista, itemId, itemData, onSuccess, onFail) => {
  let clientContext = new SP.ClientContext(url);
  let oList = clientContext.get_web().get_lists().getByTitle(nombreLista);
  let oListItem = oList.getItemById(itemId);
  console.log("clientContext:", clientContext);
  console.log("oList:", oList);
  console.log("oListItem:", oListItem);
  console.log("url:", url);
  console.log("nombreLista:", nombreLista);
  console.log("itemData:", itemData);
  console.log("itemId:", itemId);
  itemData.forEach((element) => {
    oListItem.set_item(element.columna, element.valor);
  });

  oListItem.update();
  clientContext.load(oListItem);
  clientContext.executeQueryAsync(Function.createDelegate(this, onSuccess), Function.createDelegate(this, onFail));
}

SP.SOD.executeFunc('sp.js', 'SP.ClientContext', spActualizarItem);

export const obtenerItemsAutorizantes = async (url, nombreLista, usuario, codigo) => {
  return new Promise((resolve, reject) => {
    console.log("url:", url);
    console.log("nombreLista:", nombreLista);
    console.log("usuario:", usuario);
    console.log("codigo:", codigo);
    let clientContext = new SP.ClientContext(url);
    let oList = clientContext.get_web().get_lists().getByTitle(nombreLista);
    let camlQuery = new SP.CamlQuery();

    camlQuery.set_viewXml('<View><Query><Where><And>' +
      '<Eq><FieldRef Name="Usuario" LookupId="True" /><Value Type="User">' + usuario + '</Value></Eq>' +
      '<Eq><FieldRef Name="Title" /><Value Type="Text">' + codigo + '</Value></Eq>' +
      '</And></Where></Query></View>');

    console.log("camlQuery:", camlQuery);
    let collListItem = oList.getItems(camlQuery);

    clientContext.load(collListItem);
    clientContext.executeQueryAsync(
      () => {
        let itemsArray = [];
        let listItemEnumerator = collListItem.getEnumerator();
        while (listItemEnumerator.moveNext()) {
          let oListItem = listItemEnumerator.get_current();
          console.log("oListItem:", oListItem);
          itemsArray.push({
            itemId: oListItem.get_id(),
            usuario: oListItem.get_item('Usuario')
          });
        }
        console.log("itemsArray:", itemsArray);
        let datosArray = {itemId: itemsArray[0].itemId, usuario: itemsArray[0].usuario.$1p_1};
        console.log("datosArray:", datosArray);
        resolve(itemsArray);
      },
      (sender, args) => {
        reject('Error al recuperar ítems: ' + args.get_message());
      }
    );
  });
}

export const spCrearItemAutorizantes = (url, nombreLista, itemData, onSuccess, onFail) => {
  console.log("spCrearItem url:", url);
  console.log("nombre lista: ", nombreLista);
  console.table("itemData: ", itemData);
  let clientContext = new SP.ClientContext(url);
  console.log("clientContext: ", clientContext);
  let oList = clientContext.get_web().get_lists().getByTitle(nombreLista);
  let itemCreateInfo = new SP.ListItemCreationInformation();
  let oListItem = oList.addItem(itemCreateInfo);

  try {
    console.log("entre en el try.");
    for (let propName in itemData) {
      console.log("entre en el for.");
      console.log("propName:", propName);
      if (itemData.hasOwnProperty(propName)) {
        console.log("itemData[propName]:", itemData[propName]);
        oListItem.set_item(propName, itemData[propName]);
      }
    }

    oListItem.update();
    clientContext.load(oListItem);
    clientContext.executeQueryAsync(
      Function.createDelegate(this, onSuccess),
      function (sender, args) {
        console.log(args.get_message());
        // prints the error message        
        console.log(args.get_stackTrace()); // prints the stack trace
      }
    );
  } catch (error) {
    console.log("error catch spCrearItemAutorizantes:", error);
  }
}

export function ProcesartareaNintexBI(custom, ID, lista, callbackOk, callbackNoOk) {
  var url = getSitioCreditos(datosOperacionSeleccionada[0].Codigo_x0020_producto) + "/_vti_bin/NintexWorkflow/Workflow.asmx";
  var xhr = new XMLHttpRequest();
  var comentario = document.getElementById("comentairos").value;
  if (comentario == "") {
    comentario = "Sin comentario."
  }
  var mensaje = '<?xml version="1.0" encoding="utf-8"?>' +
    '<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">' +
    '<soap:Body>' +
    '<ProcessFlexiTaskResponse2 xmlns="http://nintex.com">' +
    '<comments>Nintex</comments>' +
    '<outcome>' + custom + '</outcome>' +
    '<spTaskId>' + ID + '</spTaskId>' +
    '<taskListName>' + lista + '</taskListName>' +
    '</ProcessFlexiTaskResponse2>' +
    '</soap:Body>' +
    '</soap:Envelope>';
  xhr.onloadend = function () {
    if (this.readyState == 4 && xhr.status == 200) {
      callbackOk(this.responseXML)
    } else {
      callbackNoOk(this.responseXML)
    };
  };
  xhr.open("POST", url, true, currentUser, "");
  xhr.setRequestHeader('Content-Type', 'text/xml');
  xhr.withCredentials = true;
  xhr.send(mensaje);
}

export function obtenerCaminoPorTarea(idTarea, callback, callbackError, lista, urlSitio) {

  var currentUser = localStorage.getItem('contextNombreUsuario')
  var urlApi = urlSitio + "/_vti_bin/NintexWorkflow/Workflow.asmx";
  var body = `<?xml version="1.0" encoding="utf-8"?>
                    <soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
                    <soap:Body>
                        <GetOutcomesForFlexiTask xmlns="http://nintex.com">
                        <spTaskId>${idTarea}</spTaskId>
                        <taskListName>${lista}</taskListName>
                        </GetOutcomesForFlexiTask>
                    </soap:Body>
                    </soap:Envelope>`

  var xhr = new XMLHttpRequest();
  xhr.onloadend = function () {
    if (this.readyState == 4 && xhr.status == 200) {

      var jsonRespuesta = xmlToJson(this.responseXML);
      callback(jsonRespuesta, idTarea);
    } else {
      var jsonRespuesta = xmlToJson(this.responseXML);

      callbackError(jsonRespuesta);

    };

  };
  xhr.open("POST", urlApi, true, currentUser, "");
  xhr.setRequestHeader('Content-Type', 'text/xml');
  xhr.withCredentials = true;
  xhr.send(body);
}

export async function ProcesartareaNintex(idMiTarea, custom, lista) {
  let statusValueLS = localStorage.getItem("statusValue");
  console.log("statusValue procesar:", statusValueLS);
  var currentUser = localStorage.getItem('contextNombreUsuario');
  var url = getUrlSeguimientoFactura() + "/_vti_bin/NintexWorkflow/Workflow.asmx";
  var xhr = new XMLHttpRequest();


  var mensaje = '<?xml version="1.0" encoding="utf-8"?>' +
    '<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">' +
    '<soap:Body>' +
    '<ProcessFlexiTaskResponse2 xmlns="http://nintex.com">' +
    '<comments>' + 'Comentario' + '</comments>' +
    '<outcome>' + custom + '</outcome>' +
    '<spTaskId>' + idMiTarea + '</spTaskId>' +
    '<taskListName>' + lista + '</taskListName>' +
    '</ProcessFlexiTaskResponse2>' +
    '</soap:Body>' +
    '</soap:Envelope>';
  console.log(mensaje);
  /* una vez que termina la llamada*/

  let statusValue;

  xhr.onloadend = function () {
    if (this.readyState == 4 && xhr.status == 200) {
      console.log(this.responseXML);
      statusValue = xhr.status;
      localStorage.setItem("statusValue", xhr.status);
      console.log("if:", statusValue);

    } else {
      console.log(this.responseXML);
      statusValue = xhr.status;
      localStorage.setItem("statusValue", xhr.status);
      console.log("else:", statusValue);
    };

  };

  xhr.open("POST", url, false, currentUser, "");
  xhr.setRequestHeader('Content-Type', 'text/xml');
  xhr.withCredentials = true;
  xhr.send(mensaje);
  console.log("statusValue:", statusValue);
  return statusValue;
}

